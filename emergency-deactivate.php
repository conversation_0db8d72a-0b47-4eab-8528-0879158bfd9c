<?php
/**
 * EMERGENCY: Direct Database Plugin Deactivation
 * This bypasses WordPress and directly modifies the database
 */

// Database configuration - adjust these for your setup
$db_host = 'localhost';
$db_name = 'wp_redco';
$db_user = 'root';
$db_pass = '';
$table_prefix = 'wp_';

echo "🚨 EMERGENCY PLUGIN DEACTIVATION\n";
echo "================================\n\n";

try {
    // Connect to database
    $pdo = new PDO("mysql:host={$db_host};dbname={$db_name}", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful\n";
    
    // Get current active plugins
    $stmt = $pdo->prepare("SELECT option_value FROM {$table_prefix}options WHERE option_name = 'active_plugins'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        $active_plugins = unserialize($result['option_value']);
        echo "📋 Current active plugins:\n";
        
        if (is_array($active_plugins)) {
            foreach ($active_plugins as $plugin) {
                echo "  - {$plugin}\n";
            }
            
            // Remove Redco Optimizer from active plugins
            $redco_plugin = 'redco-optimizer/redco-optimizer.php';
            $updated_plugins = array_diff($active_plugins, array($redco_plugin));
            
            if (count($updated_plugins) !== count($active_plugins)) {
                // Update the database
                $serialized_plugins = serialize(array_values($updated_plugins));
                $update_stmt = $pdo->prepare("UPDATE {$table_prefix}options SET option_value = ? WHERE option_name = 'active_plugins'");
                $update_stmt->execute(array($serialized_plugins));
                
                echo "\n✅ Redco Optimizer deactivated successfully!\n";
                echo "📝 Plugin removed from active plugins list\n";
                echo "\n🧪 TEST NOW:\n";
                echo "Try accessing: http://localhost/wordpress/wp-admin/\n";
                echo "If it loads, the issue was with Redco Optimizer\n";
                
            } else {
                echo "\n⚠️ Redco Optimizer was not in the active plugins list\n";
                echo "The 500 error is caused by something else\n";
            }
            
        } else {
            echo "❌ Active plugins data is corrupted\n";
            
            // Reset all plugins
            $empty_array = serialize(array());
            $reset_stmt = $pdo->prepare("UPDATE {$table_prefix}options SET option_value = ? WHERE option_name = 'active_plugins'");
            $reset_stmt->execute(array($empty_array));
            
            echo "🔄 All plugins deactivated as emergency measure\n";
        }
        
    } else {
        echo "❌ Could not find active_plugins option in database\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    echo "\nPlease check your database configuration:\n";
    echo "- Host: {$db_host}\n";
    echo "- Database: {$db_name}\n";
    echo "- User: {$db_user}\n";
    echo "- Table prefix: {$table_prefix}\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n📋 NEXT STEPS:\n";
echo "==============\n";
echo "1. Test WordPress admin access\n";
echo "2. If it works, the issue was with Redco Optimizer\n";
echo "3. If it still fails, check server logs and other plugins\n";
echo "4. You can reactivate plugins through WordPress admin when ready\n";

?>
