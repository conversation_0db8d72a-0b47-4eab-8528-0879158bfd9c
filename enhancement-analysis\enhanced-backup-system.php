<?php
/**
 * Enhanced Backup & Rollback System for Redco Optimizer
 * 
 * Comprehensive backup system with granular rollback capabilities
 */

class Redco_Enhanced_Backup_System {
    
    private $backup_types = array(
        'full_site' => 'Complete site backup (files + database)',
        'files_only' => 'Files backup only',
        'database_only' => 'Database backup only',
        'selective' => 'Selective backup (specific files/tables)',
        'incremental' => 'Incremental backup (changes only)'
    );
    
    private $compression_methods = array(
        'zip' => array('extension' => '.zip', 'class' => 'ZipArchive'),
        'tar_gz' => array('extension' => '.tar.gz', 'command' => 'tar -czf'),
        'tar_bz2' => array('extension' => '.tar.bz2', 'command' => 'tar -cjf')
    );
    
    /**
     * Create comprehensive backup before applying fixes
     */
    public function create_pre_fix_backup($fix_details, $backup_config = array()) {
        $backup_id = wp_generate_uuid4();
        
        $default_config = array(
            'type' => $this->determine_backup_type($fix_details),
            'compression' => 'zip',
            'include_uploads' => false,
            'include_plugins' => true,
            'include_themes' => true,
            'include_database' => true,
            'retention_days' => 30,
            'verify_integrity' => true
        );
        
        $config = array_merge($default_config, $backup_config);
        
        try {
            $backup_info = array(
                'id' => $backup_id,
                'type' => $config['type'],
                'created_at' => current_time('mysql'),
                'created_by' => get_current_user_id(),
                'related_fix_id' => $fix_details['id'],
                'config' => $config,
                'status' => 'creating',
                'files_included' => array(),
                'database_tables' => array(),
                'backup_size' => 0,
                'backup_hash' => '',
                'metadata' => array(
                    'wp_version' => get_bloginfo('version'),
                    'php_version' => PHP_VERSION,
                    'active_plugins' => get_option('active_plugins'),
                    'active_theme' => get_template()
                )
            );
            
            // Store initial backup info
            $this->store_backup_info($backup_info);
            
            // Create backup directory
            $backup_path = $this->create_backup_directory($backup_id);
            
            // Perform backup based on type
            switch ($config['type']) {
                case 'full_site':
                    $result = $this->create_full_site_backup($backup_id, $backup_path, $config);
                    break;
                case 'files_only':
                    $result = $this->create_files_backup($backup_id, $backup_path, $config);
                    break;
                case 'database_only':
                    $result = $this->create_database_backup($backup_id, $backup_path, $config);
                    break;
                case 'selective':
                    $result = $this->create_selective_backup($backup_id, $backup_path, $config, $fix_details);
                    break;
                case 'incremental':
                    $result = $this->create_incremental_backup($backup_id, $backup_path, $config);
                    break;
            }
            
            if ($result['success']) {
                // Verify backup integrity
                if ($config['verify_integrity']) {
                    $verification = $this->verify_backup_integrity($backup_id, $backup_path);
                    $result['verification'] = $verification;
                }
                
                // Update backup info with results
                $backup_info['status'] = 'completed';
                $backup_info['backup_path'] = $backup_path;
                $backup_info['backup_size'] = $result['size'];
                $backup_info['backup_hash'] = $result['hash'];
                $backup_info['files_included'] = $result['files'];
                $backup_info['database_tables'] = $result['tables'] ?? array();
                
                $this->update_backup_info($backup_id, $backup_info);
                
                // Schedule cleanup
                $this->schedule_backup_cleanup($backup_id, $config['retention_days']);
                
                return array(
                    'success' => true,
                    'backup_id' => $backup_id,
                    'backup_info' => $backup_info
                );
                
            } else {
                // Mark backup as failed
                $backup_info['status'] = 'failed';
                $backup_info['error'] = $result['error'];
                $this->update_backup_info($backup_id, $backup_info);
                
                return array(
                    'success' => false,
                    'error' => $result['error']
                );
            }
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => 'Backup creation failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Create selective backup based on fix requirements
     */
    private function create_selective_backup($backup_id, $backup_path, $config, $fix_details) {
        $files_to_backup = array();
        $tables_to_backup = array();
        
        // Determine what to backup based on fix type
        switch ($fix_details['category']) {
            case 'database':
                $tables_to_backup = $this->get_affected_database_tables($fix_details);
                break;
                
            case 'frontend':
                $files_to_backup = array_merge(
                    $this->get_theme_files(),
                    $this->get_css_js_files(),
                    array(ABSPATH . '.htaccess')
                );
                break;
                
            case 'server':
                $files_to_backup = array(
                    ABSPATH . '.htaccess',
                    ABSPATH . 'wp-config.php',
                    ABSPATH . 'web.config'
                );
                break;
                
            case 'images':
                $files_to_backup = $this->get_image_files();
                break;
                
            default:
                // Backup critical files for unknown fix types
                $files_to_backup = array(
                    ABSPATH . '.htaccess',
                    ABSPATH . 'wp-config.php'
                );
                $tables_to_backup = array('options', 'postmeta', 'usermeta');
                break;
        }
        
        $backup_result = array(
            'success' => true,
            'files' => array(),
            'tables' => array(),
            'size' => 0,
            'hash' => ''
        );
        
        // Backup selected files
        if (!empty($files_to_backup)) {
            $file_backup_result = $this->backup_specific_files($files_to_backup, $backup_path);
            $backup_result['files'] = $file_backup_result['files'];
            $backup_result['size'] += $file_backup_result['size'];
        }
        
        // Backup selected database tables
        if (!empty($tables_to_backup)) {
            $db_backup_result = $this->backup_specific_tables($tables_to_backup, $backup_path);
            $backup_result['tables'] = $db_backup_result['tables'];
            $backup_result['size'] += $db_backup_result['size'];
        }
        
        // Generate backup hash
        $backup_result['hash'] = $this->generate_backup_hash($backup_path);
        
        return $backup_result;
    }
    
    /**
     * Create incremental backup (only changed files)
     */
    private function create_incremental_backup($backup_id, $backup_path, $config) {
        $last_backup = $this->get_last_backup('incremental');
        
        if (!$last_backup) {
            // No previous backup, create full backup
            return $this->create_full_site_backup($backup_id, $backup_path, $config);
        }
        
        $changed_files = $this->find_changed_files_since($last_backup['created_at']);
        $changed_tables = $this->find_changed_database_tables_since($last_backup['created_at']);
        
        $backup_result = array(
            'success' => true,
            'files' => array(),
            'tables' => array(),
            'size' => 0,
            'hash' => '',
            'incremental_base' => $last_backup['id']
        );
        
        // Backup changed files
        if (!empty($changed_files)) {
            $file_backup_result = $this->backup_specific_files($changed_files, $backup_path);
            $backup_result['files'] = $file_backup_result['files'];
            $backup_result['size'] += $file_backup_result['size'];
        }
        
        // Backup changed database tables
        if (!empty($changed_tables)) {
            $db_backup_result = $this->backup_specific_tables($changed_tables, $backup_path);
            $backup_result['tables'] = $db_backup_result['tables'];
            $backup_result['size'] += $db_backup_result['size'];
        }
        
        $backup_result['hash'] = $this->generate_backup_hash($backup_path);
        
        return $backup_result;
    }
    
    /**
     * Rollback system with granular control
     */
    public function rollback_fix($backup_id, $rollback_options = array()) {
        $backup_info = $this->get_backup_info($backup_id);
        
        if (!$backup_info) {
            return array(
                'success' => false,
                'error' => 'Backup not found'
            );
        }
        
        $default_options = array(
            'rollback_files' => true,
            'rollback_database' => true,
            'verify_before_rollback' => true,
            'create_pre_rollback_backup' => true,
            'selective_rollback' => false,
            'selected_files' => array(),
            'selected_tables' => array()
        );
        
        $options = array_merge($default_options, $rollback_options);
        
        try {
            // Verify backup integrity before rollback
            if ($options['verify_before_rollback']) {
                $verification = $this->verify_backup_integrity($backup_id, $backup_info['backup_path']);
                if (!$verification['valid']) {
                    return array(
                        'success' => false,
                        'error' => 'Backup integrity verification failed: ' . $verification['error']
                    );
                }
            }
            
            // Create pre-rollback backup
            if ($options['create_pre_rollback_backup']) {
                $pre_rollback_backup = $this->create_pre_fix_backup(
                    array('id' => 'pre_rollback_' . $backup_id),
                    array('type' => 'selective')
                );
                
                if (!$pre_rollback_backup['success']) {
                    return array(
                        'success' => false,
                        'error' => 'Failed to create pre-rollback backup'
                    );
                }
            }
            
            $rollback_result = array(
                'success' => true,
                'files_restored' => array(),
                'tables_restored' => array(),
                'errors' => array()
            );
            
            // Rollback files
            if ($options['rollback_files']) {
                $files_to_restore = $options['selective_rollback'] ? 
                    $options['selected_files'] : 
                    $backup_info['files_included'];
                    
                $file_rollback = $this->restore_files($backup_info['backup_path'], $files_to_restore);
                $rollback_result['files_restored'] = $file_rollback['restored'];
                
                if (!empty($file_rollback['errors'])) {
                    $rollback_result['errors'] = array_merge($rollback_result['errors'], $file_rollback['errors']);
                }
            }
            
            // Rollback database
            if ($options['rollback_database']) {
                $tables_to_restore = $options['selective_rollback'] ? 
                    $options['selected_tables'] : 
                    $backup_info['database_tables'];
                    
                $db_rollback = $this->restore_database_tables($backup_info['backup_path'], $tables_to_restore);
                $rollback_result['tables_restored'] = $db_rollback['restored'];
                
                if (!empty($db_rollback['errors'])) {
                    $rollback_result['errors'] = array_merge($rollback_result['errors'], $db_rollback['errors']);
                }
            }
            
            // Log rollback
            $this->log_rollback_action($backup_id, $rollback_result, $options);
            
            // Clear caches after rollback
            $this->clear_caches_after_rollback();
            
            return $rollback_result;
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => 'Rollback failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Verify backup integrity
     */
    private function verify_backup_integrity($backup_id, $backup_path) {
        $verification_result = array(
            'valid' => false,
            'checks' => array(),
            'error' => ''
        );
        
        try {
            // Check if backup files exist
            if (!file_exists($backup_path)) {
                $verification_result['error'] = 'Backup path does not exist';
                return $verification_result;
            }
            
            // Verify file integrity using checksums
            $backup_info = $this->get_backup_info($backup_id);
            $current_hash = $this->generate_backup_hash($backup_path);
            
            if ($current_hash !== $backup_info['backup_hash']) {
                $verification_result['error'] = 'Backup hash mismatch - backup may be corrupted';
                return $verification_result;
            }
            
            $verification_result['checks']['hash_match'] = true;
            
            // Test backup extraction
            $test_extraction = $this->test_backup_extraction($backup_path);
            $verification_result['checks']['extraction_test'] = $test_extraction['success'];
            
            if (!$test_extraction['success']) {
                $verification_result['error'] = 'Backup extraction test failed: ' . $test_extraction['error'];
                return $verification_result;
            }
            
            // Verify database backup if included
            if (!empty($backup_info['database_tables'])) {
                $db_verification = $this->verify_database_backup($backup_path);
                $verification_result['checks']['database_valid'] = $db_verification['valid'];
                
                if (!$db_verification['valid']) {
                    $verification_result['error'] = 'Database backup verification failed: ' . $db_verification['error'];
                    return $verification_result;
                }
            }
            
            $verification_result['valid'] = true;
            
        } catch (Exception $e) {
            $verification_result['error'] = 'Verification failed: ' . $e->getMessage();
        }
        
        return $verification_result;
    }
    
    /**
     * Automated rollback system
     */
    public function setup_automated_rollback($fix_id, $backup_id, $rollback_config) {
        $rollback_schedule = array(
            'id' => wp_generate_uuid4(),
            'fix_id' => $fix_id,
            'backup_id' => $backup_id,
            'trigger_conditions' => $rollback_config['conditions'],
            'rollback_delay' => $rollback_config['delay_hours'],
            'monitoring_metrics' => $rollback_config['metrics'],
            'created_at' => current_time('mysql'),
            'status' => 'active'
        );
        
        // Store rollback schedule
        $this->store_rollback_schedule($rollback_schedule);
        
        // Schedule monitoring checks
        wp_schedule_event(
            time() + (60 * 15), // Start monitoring after 15 minutes
            'redco_15min',
            'redco_check_rollback_conditions',
            array($rollback_schedule['id'])
        );
        
        return $rollback_schedule['id'];
    }
    
    /**
     * Check rollback conditions
     */
    public function check_rollback_conditions($rollback_schedule_id) {
        $schedule = $this->get_rollback_schedule($rollback_schedule_id);
        
        if (!$schedule || $schedule['status'] !== 'active') {
            return;
        }
        
        $conditions_met = false;
        $triggered_conditions = array();
        
        foreach ($schedule['trigger_conditions'] as $condition) {
            switch ($condition['type']) {
                case 'error_rate_increase':
                    if ($this->check_error_rate_increase($condition['threshold'])) {
                        $conditions_met = true;
                        $triggered_conditions[] = $condition;
                    }
                    break;
                    
                case 'performance_degradation':
                    if ($this->check_performance_degradation($condition['metric'], $condition['threshold'])) {
                        $conditions_met = true;
                        $triggered_conditions[] = $condition;
                    }
                    break;
                    
                case 'uptime_failure':
                    if ($this->check_uptime_failure()) {
                        $conditions_met = true;
                        $triggered_conditions[] = $condition;
                    }
                    break;
            }
        }
        
        if ($conditions_met) {
            $this->execute_automated_rollback($schedule, $triggered_conditions);
        }
    }
    
    /**
     * Execute automated rollback
     */
    private function execute_automated_rollback($schedule, $triggered_conditions) {
        // Mark schedule as triggered
        $this->update_rollback_schedule_status($schedule['id'], 'triggered');
        
        // Send alert notification
        $this->send_rollback_alert($schedule, $triggered_conditions);
        
        // Execute rollback
        $rollback_result = $this->rollback_fix($schedule['backup_id'], array(
            'rollback_files' => true,
            'rollback_database' => true,
            'create_pre_rollback_backup' => true
        ));
        
        // Log automated rollback
        $this->log_automated_rollback($schedule, $triggered_conditions, $rollback_result);
        
        // Send completion notification
        $this->send_rollback_completion_notification($schedule, $rollback_result);
    }
}
