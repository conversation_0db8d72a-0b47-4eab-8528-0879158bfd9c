/**
 * Admin Styles for Redco Diagnostic & Auto-Fix
 * 
 * Modern, responsive design for the diagnostic interface
 */

/* Main Container */
.redco-diagnostic-admin {
    margin: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.redco-diagnostic-admin h1 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    color: #1d2327;
}

.redco-diagnostic-admin h1 .dashicons {
    color: #2271b1;
    font-size: 28px;
}

/* Header Navigation */
.redco-diagnostic-header {
    margin-bottom: 30px;
    border-bottom: 1px solid #c3c4c7;
}

.redco-diagnostic-nav {
    display: flex;
    gap: 0;
    margin: 0;
}

.redco-diagnostic-nav .nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-bottom: none;
    color: #50575e;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.redco-diagnostic-nav .nav-tab:hover {
    background: #fff;
    color: #2271b1;
}

.redco-diagnostic-nav .nav-tab.nav-tab-active {
    background: #fff;
    color: #2271b1;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
}

.redco-diagnostic-nav .nav-tab .dashicons {
    font-size: 16px;
}

/* Tab Content */
.redco-tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.redco-tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Cards */
.redco-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.redco-card-header {
    background: #f6f7f7;
    border-bottom: 1px solid #c3c4c7;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.redco-card-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1d2327;
    font-size: 16px;
    font-weight: 600;
}

.redco-card-header .dashicons {
    color: #2271b1;
    font-size: 18px;
}

.redco-card-body {
    padding: 20px;
}

.redco-card-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f1;
    display: flex;
    gap: 10px;
}

/* Dashboard Layout */
.redco-dashboard {
    display: grid;
    gap: 20px;
}

.redco-overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* Statistics */
.redco-stat-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.redco-stat {
    text-align: center;
    padding: 15px;
    background: #f6f7f7;
    border-radius: 6px;
}

.redco-stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #2271b1;
    margin-bottom: 5px;
}

.redco-stat-label {
    font-size: 12px;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Tier Overview */
.redco-tier-overview {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.redco-tier-item {
    flex: 1;
    text-align: center;
    padding: 15px;
    border-radius: 6px;
    border: 2px solid;
}

.redco-tier-item.safe {
    border-color: #00a32a;
    background: rgba(0, 163, 42, 0.1);
}

.redco-tier-item.moderate {
    border-color: #dba617;
    background: rgba(219, 166, 23, 0.1);
}

.redco-tier-item.advanced {
    border-color: #d63638;
    background: rgba(214, 54, 56, 0.1);
}

.redco-tier-count {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 5px;
}

.redco-tier-item.safe .redco-tier-count {
    color: #00a32a;
}

.redco-tier-item.moderate .redco-tier-count {
    color: #dba617;
}

.redco-tier-item.advanced .redco-tier-count {
    color: #d63638;
}

.redco-tier-label {
    font-size: 12px;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Core Web Vitals */
.redco-vitals-overview {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.redco-vital-item {
    flex: 1;
    text-align: center;
    padding: 15px;
    border-radius: 6px;
    border: 2px solid #c3c4c7;
}

.redco-vital-value {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 5px;
}

.redco-vital-value.good {
    color: #00a32a;
}

.redco-vital-value.needs-improvement {
    color: #dba617;
}

.redco-vital-value.poor {
    color: #d63638;
}

.redco-vital-label {
    font-size: 12px;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.redco-overall-score {
    text-align: center;
}

.redco-score-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #f6f7f7;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    border: 3px solid #2271b1;
}

.redco-score-value {
    font-size: 24px;
    font-weight: 700;
    color: #2271b1;
}

.redco-score-label {
    font-size: 12px;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Scan Controls */
.redco-scan-options {
    display: grid;
    gap: 15px;
    margin-bottom: 20px;
}

.redco-scan-options label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 15px;
    border: 1px solid #c3c4c7;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.redco-scan-options label:hover {
    border-color: #2271b1;
    background: rgba(34, 113, 177, 0.05);
}

.redco-scan-options input[type="radio"] {
    margin: 0;
}

.redco-scan-options input[type="radio"]:checked + strong {
    color: #2271b1;
}

.redco-scan-options .description {
    display: block;
    color: #646970;
    font-size: 13px;
    margin-top: 5px;
}

.redco-scan-actions {
    text-align: center;
}

/* Progress Bar */
.redco-progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f1;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.redco-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2271b1, #72aee6);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.redco-progress-text {
    text-align: center;
    color: #646970;
    font-size: 14px;
}

/* Loading States */
.redco-loading {
    text-align: center;
    padding: 40px;
    color: #646970;
}

.redco-loading::before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f0f0f1;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chart Controls */
.redco-chart-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.redco-chart-controls select {
    padding: 5px 10px;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    background: #fff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-overview-cards {
        grid-template-columns: 1fr;
    }
    
    .redco-diagnostic-nav {
        flex-wrap: wrap;
    }
    
    .redco-diagnostic-nav .nav-tab {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
    
    .redco-tier-overview {
        flex-direction: column;
    }
    
    .redco-vitals-overview {
        flex-direction: column;
    }
    
    .redco-stat-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .redco-diagnostic-admin {
        margin: 10px 0;
    }
    
    .redco-card-body {
        padding: 15px;
    }
    
    .redco-stat-grid {
        grid-template-columns: 1fr;
    }
}

/* Utility Classes */
.redco-text-center {
    text-align: center;
}

.redco-text-success {
    color: #00a32a;
}

.redco-text-warning {
    color: #dba617;
}

.redco-text-danger {
    color: #d63638;
}

.redco-text-muted {
    color: #646970;
}

.redco-mb-0 {
    margin-bottom: 0;
}

.redco-mt-20 {
    margin-top: 20px;
}

.redco-hidden {
    display: none;
}

/* Button Enhancements */
.button.button-large {
    padding: 8px 20px;
    font-size: 14px;
    height: auto;
}

.button .dashicons {
    margin-right: 5px;
    font-size: 16px;
    vertical-align: middle;
}
