/**
 * CRITICAL FIX: jQuery Dependency Test Suite
 * Tests all JavaScript files to ensure jQuery is properly available
 * Run this in browser console to validate the fixes
 */

(function($) {
    'use strict';

    /**
     * jQuery Dependency Test Suite
     */
    window.RedcoJQueryTest = {
        
        /**
         * Test results
         */
        results: [],
        
        /**
         * Run all jQuery dependency tests
         */
        runAllTests: function() {
            console.log('🧪 Starting Redco jQuery Dependency Test Suite...');
            this.results = [];
            
            // Test 1: Basic jQuery availability
            this.testJQueryAvailability();
            
            // Test 2: Test all utility modules
            this.testUtilityModules();
            
            // Test 3: Test main modules
            this.testMainModules();
            
            // Test 4: Test diagnostic module
            this.testDiagnosticModule();
            
            // Test 5: Test AJAX functionality
            this.testAjaxFunctionality();
            
            // Show results
            this.showTestResults();
        },
        
        /**
         * Test 1: Basic jQuery availability
         */
        testJQueryAvailability: function() {
            console.log('🔍 Test 1: Testing jQuery availability...');
            
            const result = {
                test: 'jQuery Availability',
                passed: false,
                details: []
            };
            
            // Test jQuery global
            if (typeof jQuery !== 'undefined') {
                result.details.push('✅ jQuery global available');
            } else {
                result.details.push('❌ jQuery global not available');
            }
            
            // Test $ alias
            if (typeof $ !== 'undefined') {
                result.details.push('✅ $ alias available');
            } else {
                result.details.push('❌ $ alias not available');
            }
            
            // Test jQuery functionality
            try {
                const $testElement = $('<div>');
                if ($testElement.length === 1) {
                    result.details.push('✅ jQuery element creation works');
                    result.passed = true;
                } else {
                    result.details.push('❌ jQuery element creation failed');
                }
            } catch (e) {
                result.details.push('❌ jQuery functionality error: ' + e.message);
            }
            
            this.results.push(result);
        },
        
        /**
         * Test 2: Test utility modules
         */
        testUtilityModules: function() {
            console.log('🔍 Test 2: Testing utility modules...');
            
            const modules = [
                { name: 'RedcoAjax', description: 'AJAX Utilities' },
                { name: 'RedcoValidation', description: 'Validation Utilities' },
                { name: 'RedcoProgress', description: 'Progress Utilities' },
                { name: 'redcoDebug', description: 'Debug Utilities' }
            ];
            
            modules.forEach(module => {
                const result = {
                    test: module.description,
                    passed: false,
                    details: []
                };
                
                if (typeof window[module.name] !== 'undefined') {
                    result.details.push(`✅ ${module.name} module loaded`);
                    result.passed = true;
                } else {
                    result.details.push(`❌ ${module.name} module not found`);
                }
                
                this.results.push(result);
            });
        },
        
        /**
         * Test 3: Test main modules
         */
        testMainModules: function() {
            console.log('🔍 Test 3: Testing main modules...');
            
            const modules = [
                { name: 'RedcoAutoSave', description: 'Auto-Save Module' },
                { name: 'RedcoPerformanceMonitor', description: 'Performance Monitor' },
                { name: 'RedcoUIUtils', description: 'UI Utilities' },
                { name: 'RedcoOptimizer', description: 'Main Optimizer' }
            ];
            
            modules.forEach(module => {
                const result = {
                    test: module.description,
                    passed: false,
                    details: []
                };
                
                if (typeof window[module.name] !== 'undefined') {
                    result.details.push(`✅ ${module.name} module loaded`);
                    
                    // Test if module has init function
                    if (typeof window[module.name].init === 'function') {
                        result.details.push(`✅ ${module.name}.init() function available`);
                        result.passed = true;
                    } else {
                        result.details.push(`⚠️ ${module.name}.init() function not found`);
                        result.passed = true; // Still pass if module exists
                    }
                } else {
                    result.details.push(`❌ ${module.name} module not found`);
                }
                
                this.results.push(result);
            });
        },
        
        /**
         * Test 4: Test diagnostic module
         */
        testDiagnosticModule: function() {
            console.log('🔍 Test 4: Testing diagnostic module...');
            
            const result = {
                test: 'Diagnostic Module',
                passed: false,
                details: []
            };
            
            // Check if diagnostic module elements exist
            if ($('#run-comprehensive-scan').length > 0 || $('.redco-run-scan').length > 0) {
                result.details.push('✅ Diagnostic scan button found');
            } else {
                result.details.push('❌ Diagnostic scan button not found');
            }
            
            // Check if diagnostic AJAX config exists
            if (typeof redcoDiagnosticAjax !== 'undefined') {
                result.details.push('✅ Diagnostic AJAX config available');
                
                if (redcoDiagnosticAjax.nonce) {
                    result.details.push('✅ Diagnostic nonce available');
                    result.passed = true;
                } else {
                    result.details.push('❌ Diagnostic nonce missing');
                }
            } else {
                result.details.push('❌ Diagnostic AJAX config not found');
            }
            
            this.results.push(result);
        },
        
        /**
         * Test 5: Test AJAX functionality
         */
        testAjaxFunctionality: function() {
            console.log('🔍 Test 5: Testing AJAX functionality...');
            
            const result = {
                test: 'AJAX Functionality',
                passed: false,
                details: []
            };
            
            // Test jQuery AJAX
            if (typeof $.ajax === 'function') {
                result.details.push('✅ jQuery AJAX function available');
                result.passed = true;
            } else {
                result.details.push('❌ jQuery AJAX function not available');
            }
            
            // Test RedcoAjax utility
            if (typeof RedcoAjax !== 'undefined' && typeof RedcoAjax.request === 'function') {
                result.details.push('✅ RedcoAjax utility available');
            } else {
                result.details.push('⚠️ RedcoAjax utility not available (fallback to jQuery)');
            }
            
            // Test AJAX configuration
            if (typeof redcoAjax !== 'undefined') {
                result.details.push('✅ Global AJAX config available');
                
                if (redcoAjax.ajaxurl) {
                    result.details.push('✅ AJAX URL configured');
                } else {
                    result.details.push('❌ AJAX URL missing');
                    result.passed = false;
                }
                
                if (redcoAjax.nonce) {
                    result.details.push('✅ AJAX nonce configured');
                } else {
                    result.details.push('❌ AJAX nonce missing');
                    result.passed = false;
                }
            } else {
                result.details.push('❌ Global AJAX config not found');
                result.passed = false;
            }
            
            this.results.push(result);
        },
        
        /**
         * Show test results
         */
        showTestResults: function() {
            const passed = this.results.filter(r => r.passed).length;
            const total = this.results.length;
            
            console.log('\n📊 REDCO JQUERY DEPENDENCY TEST RESULTS');
            console.log('========================================');
            console.log(`✅ Passed: ${passed}/${total}`);
            console.log(`❌ Failed: ${total - passed}/${total}`);
            console.log('');
            
            this.results.forEach((result, index) => {
                console.log(`${index + 1}. ${result.test}: ${result.passed ? '✅ PASSED' : '❌ FAILED'}`);
                result.details.forEach(detail => {
                    console.log(`   ${detail}`);
                });
                console.log('');
            });
            
            if (passed === total) {
                console.log('🎉 ALL TESTS PASSED! jQuery dependencies are working correctly.');
            } else {
                console.log('⚠️ SOME TESTS FAILED. Please review the issues above.');
            }
            
            // Return summary for programmatic use
            return {
                passed: passed,
                total: total,
                success: passed === total,
                results: this.results
            };
        },
        
        /**
         * Test specific module
         */
        testModule: function(moduleName) {
            console.log(`🔍 Testing specific module: ${moduleName}`);
            
            const result = {
                test: `${moduleName} Module Test`,
                passed: false,
                details: []
            };
            
            if (typeof window[moduleName] !== 'undefined') {
                result.details.push(`✅ ${moduleName} is available`);
                
                // Test common methods
                const commonMethods = ['init', 'config', 'state'];
                commonMethods.forEach(method => {
                    if (typeof window[moduleName][method] !== 'undefined') {
                        result.details.push(`✅ ${moduleName}.${method} exists`);
                    }
                });
                
                result.passed = true;
            } else {
                result.details.push(`❌ ${moduleName} is not available`);
            }
            
            console.log(result.passed ? '✅ PASSED' : '❌ FAILED', result.details);
            return result;
        }
    };

    // Auto-run tests if in debug mode
    $(document).ready(function() {
        if (typeof redcoDebug !== 'undefined' && redcoDebug.enabled) {
            console.log('🧪 jQuery dependency test suite loaded. Run RedcoJQueryTest.runAllTests() to test jQuery dependencies.');
        }
    });

})(jQuery);
