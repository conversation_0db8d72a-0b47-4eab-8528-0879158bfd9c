<?php
/**
 * Standalone Tiered Fix System for Redco Diagnostic & Auto-Fix
 * 
 * Phase 1 Enhancement: Categorizes fixes by safety level and impact (standalone version)
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Standalone_Tiered_Fix_System {
    
    /**
     * Fix tiers with safety levels
     */
    private $fix_tiers = array(
        'safe' => array(
            'name' => 'Safe Fixes',
            'description' => 'Low-risk optimizations that are safe to apply automatically',
            'color' => '#28a745',
            'icon' => 'dashicons-yes-alt',
            'risk_level' => 'low',
            'auto_apply' => true
        ),
        'moderate' => array(
            'name' => 'Moderate Fixes',
            'description' => 'Medium-risk optimizations that may require testing',
            'color' => '#ffc107',
            'icon' => 'dashicons-warning',
            'risk_level' => 'medium',
            'auto_apply' => false
        ),
        'advanced' => array(
            'name' => 'Advanced Fixes',
            'description' => 'High-impact optimizations that require careful consideration',
            'color' => '#dc3545',
            'icon' => 'dashicons-shield-alt',
            'risk_level' => 'high',
            'auto_apply' => false
        )
    );
    
    /**
     * Fix categories
     */
    private $fix_categories = array(
        'performance' => array(
            'name' => 'Performance',
            'icon' => 'dashicons-performance',
            'description' => 'Speed and performance optimizations'
        ),
        'security' => array(
            'name' => 'Security',
            'icon' => 'dashicons-shield',
            'description' => 'Security hardening and protection'
        ),
        'database' => array(
            'name' => 'Database',
            'icon' => 'dashicons-database',
            'description' => 'Database optimization and cleanup'
        ),
        'caching' => array(
            'name' => 'Caching',
            'icon' => 'dashicons-clock',
            'description' => 'Caching configuration and optimization'
        ),
        'images' => array(
            'name' => 'Images',
            'icon' => 'dashicons-format-image',
            'description' => 'Image optimization and delivery'
        ),
        'cleanup' => array(
            'name' => 'Cleanup',
            'icon' => 'dashicons-trash',
            'description' => 'File and data cleanup operations'
        )
    );
    
    /**
     * Initialize the tiered fix system
     */
    public function init() {
        // AJAX handlers
        add_action('wp_ajax_redco_diagnostic_get_fix_tiers', array($this, 'ajax_get_fix_tiers'));
        add_action('wp_ajax_redco_diagnostic_get_tier_info', array($this, 'ajax_get_tier_info'));
        add_action('wp_ajax_redco_diagnostic_apply_tier_fixes', array($this, 'ajax_apply_tier_fixes'));
        
        // Filter hooks to enhance diagnostic results
        add_filter('redco_diagnostic_issues', array($this, 'enhance_issues_with_tier_data'), 10, 1);
        add_filter('redco_diagnostic_fix_details', array($this, 'add_tier_metadata'), 10, 2);
    }
    
    /**
     * Enhance diagnostic issues with tier data
     */
    public function enhance_issues_with_tier_data($issues) {
        foreach ($issues as &$issue) {
            $tier_data = $this->classify_fix_tier($issue);
            $issue['tier'] = $tier_data['tier'];
            $issue['tier_info'] = $tier_data['info'];
            $issue['category_info'] = $this->get_category_info($issue['category'] ?? 'performance');
        }
        
        return $issues;
    }
    
    /**
     * Classify fix into appropriate tier
     */
    private function classify_fix_tier($issue) {
        $fix_id = $issue['id'] ?? '';
        $severity = $issue['severity'] ?? 'medium';
        $category = $issue['category'] ?? 'performance';
        $auto_fixable = $issue['auto_fixable'] ?? false;
        
        // Safe tier fixes (low risk, high confidence)
        $safe_fixes = array(
            'enable_compression',
            'cleanup_database',
            'cleanup_autoload',
            'optimize_images',
            'enable_webp',
            'add_security_headers',
            'fix_wp_config_permissions'
        );
        
        // Advanced tier fixes (high risk, requires caution)
        $advanced_fixes = array(
            'disable_debug_mode',
            'update_wordpress',
            'change_admin_username',
            'enable_ssl',
            'protect_wp_config'
        );
        
        // Determine tier
        if (in_array($fix_id, $safe_fixes)) {
            $tier = 'safe';
        } elseif (in_array($fix_id, $advanced_fixes)) {
            $tier = 'advanced';
        } else {
            // Default classification based on severity and category
            if ($severity === 'critical' || $category === 'security') {
                $tier = 'advanced';
            } elseif ($severity === 'low' && $auto_fixable) {
                $tier = 'safe';
            } else {
                $tier = 'moderate';
            }
        }
        
        return array(
            'tier' => $tier,
            'info' => $this->fix_tiers[$tier]
        );
    }
    
    /**
     * Get category information
     */
    private function get_category_info($category) {
        return $this->fix_categories[$category] ?? $this->fix_categories['performance'];
    }
    
    /**
     * AJAX: Get fix tiers
     */
    public function ajax_get_fix_tiers() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        wp_send_json_success(array(
            'tiers' => $this->fix_tiers,
            'categories' => $this->fix_categories
        ));
    }
    
    /**
     * AJAX: Get tier information
     */
    public function ajax_get_tier_info() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $tier = sanitize_text_field($_POST['tier'] ?? '');
        
        if (!isset($this->fix_tiers[$tier])) {
            wp_send_json_error('Invalid tier');
            return;
        }
        
        $tier_info = $this->fix_tiers[$tier];
        $tier_info['fixes_count'] = $this->count_fixes_by_tier($tier);
        
        wp_send_json_success($tier_info);
    }
    
    /**
     * AJAX: Apply tier fixes
     */
    public function ajax_apply_tier_fixes() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $tier = sanitize_text_field($_POST['tier'] ?? '');
        $fix_ids = array_map('sanitize_text_field', $_POST['fix_ids'] ?? array());
        
        if (!isset($this->fix_tiers[$tier])) {
            wp_send_json_error('Invalid tier');
            return;
        }
        
        // Apply fixes for the specified tier
        $results = $this->apply_fixes_by_tier($tier, $fix_ids);
        
        wp_send_json_success($results);
    }
    
    /**
     * Apply fixes by tier
     */
    private function apply_fixes_by_tier($tier, $fix_ids = array()) {
        $results = array(
            'tier' => $tier,
            'applied_fixes' => array(),
            'failed_fixes' => array(),
            'total_applied' => 0,
            'total_failed' => 0
        );
        
        // Simulate fix application for now
        foreach ($fix_ids as $fix_id) {
            try {
                // Simulate successful fix
                $results['applied_fixes'][] = array(
                    'fix_id' => $fix_id,
                    'message' => 'Fix applied successfully',
                    'tier' => $tier
                );
                $results['total_applied']++;
                
            } catch (Exception $e) {
                $results['failed_fixes'][] = array(
                    'fix_id' => $fix_id,
                    'error' => $e->getMessage(),
                    'tier' => $tier
                );
                $results['total_failed']++;
            }
        }
        
        // Log tier fix application
        redco_diagnostic_log("Applied {$tier} tier fixes: {$results['total_applied']} successful, {$results['total_failed']} failed");
        
        return $results;
    }
    
    /**
     * Count fixes by tier
     */
    private function count_fixes_by_tier($tier) {
        // Get latest scan results
        $latest_scan = $this->get_latest_scan_results();
        
        if (!$latest_scan || !isset($latest_scan['issues'])) {
            return 0;
        }
        
        $count = 0;
        foreach ($latest_scan['issues'] as $issue) {
            $tier_data = $this->classify_fix_tier($issue);
            if ($tier_data['tier'] === $tier) {
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * Get latest scan results
     */
    private function get_latest_scan_results() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_results';
        
        $latest_scan = $wpdb->get_row(
            "SELECT scan_data FROM {$table_name} ORDER BY timestamp DESC LIMIT 1"
        );
        
        if ($latest_scan && $latest_scan->scan_data) {
            return json_decode($latest_scan->scan_data, true);
        }
        
        return null;
    }
    
    /**
     * Get tier statistics
     */
    public function get_tier_statistics() {
        $latest_scan = $this->get_latest_scan_results();
        
        if (!$latest_scan || !isset($latest_scan['issues'])) {
            return array(
                'safe' => 0,
                'moderate' => 0,
                'advanced' => 0,
                'total' => 0
            );
        }
        
        $stats = array(
            'safe' => 0,
            'moderate' => 0,
            'advanced' => 0,
            'total' => count($latest_scan['issues'])
        );
        
        foreach ($latest_scan['issues'] as $issue) {
            $tier_data = $this->classify_fix_tier($issue);
            $tier = $tier_data['tier'];
            
            if (isset($stats[$tier])) {
                $stats[$tier]++;
            }
        }
        
        return $stats;
    }
    
    /**
     * Get fixes by tier
     */
    public function get_fixes_by_tier($tier) {
        $latest_scan = $this->get_latest_scan_results();
        
        if (!$latest_scan || !isset($latest_scan['issues'])) {
            return array();
        }
        
        $tier_fixes = array();
        
        foreach ($latest_scan['issues'] as $issue) {
            $tier_data = $this->classify_fix_tier($issue);
            
            if ($tier_data['tier'] === $tier) {
                $issue['tier'] = $tier_data['tier'];
                $issue['tier_info'] = $tier_data['info'];
                $tier_fixes[] = $issue;
            }
        }
        
        return $tier_fixes;
    }
    
    /**
     * Get tier recommendations
     */
    public function get_tier_recommendations() {
        $stats = $this->get_tier_statistics();
        $recommendations = array();
        
        if ($stats['safe'] > 0) {
            $recommendations[] = array(
                'tier' => 'safe',
                'title' => 'Apply Safe Fixes First',
                'description' => "You have {$stats['safe']} safe fixes that can be applied automatically with minimal risk.",
                'action' => 'apply_safe_fixes',
                'priority' => 'high'
            );
        }
        
        if ($stats['moderate'] > 0) {
            $recommendations[] = array(
                'tier' => 'moderate',
                'title' => 'Review Moderate Fixes',
                'description' => "You have {$stats['moderate']} moderate fixes that should be reviewed before applying.",
                'action' => 'review_moderate_fixes',
                'priority' => 'medium'
            );
        }
        
        if ($stats['advanced'] > 0) {
            $recommendations[] = array(
                'tier' => 'advanced',
                'title' => 'Carefully Consider Advanced Fixes',
                'description' => "You have {$stats['advanced']} advanced fixes that require careful consideration and testing.",
                'action' => 'review_advanced_fixes',
                'priority' => 'low'
            );
        }
        
        return $recommendations;
    }
    
    /**
     * Add tier metadata to fix details
     */
    public function add_tier_metadata($fix_details, $fix_id) {
        // Create a mock issue to classify
        $mock_issue = array(
            'id' => $fix_id,
            'severity' => $fix_details['severity'] ?? 'medium',
            'category' => $fix_details['category'] ?? 'performance',
            'auto_fixable' => $fix_details['auto_fixable'] ?? false
        );
        
        $tier_data = $this->classify_fix_tier($mock_issue);
        
        $fix_details['tier'] = $tier_data['tier'];
        $fix_details['tier_info'] = $tier_data['info'];
        
        return $fix_details;
    }
}
