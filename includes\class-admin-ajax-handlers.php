<?php
/**
 * CRITICAL FIX: Admin AJAX Handlers
 * Extracted from large class-admin-ui.php to improve maintainability
 * Handles all AJAX requests for the admin interface
 *
 * @package Redco_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin AJAX Handlers Class
 * Responsible for handling all AJAX requests
 */
class Redco_Admin_Ajax_Handlers {
    
    /**
     * Available modules
     */
    private $modules = array();
    
    /**
     * Constructor
     *
     * @param array $modules Available modules
     */
    public function __construct($modules) {
        $this->modules = $modules;
    }
    
    /**
     * Initialize AJAX handlers
     */
    public function init() {
        // Module management AJAX handlers
        add_action('wp_ajax_redco_toggle_module', array($this, 'ajax_toggle_module'));
        add_action('wp_ajax_redco_save_module_settings', array($this, 'ajax_save_module_settings'));
        add_action('wp_ajax_redco_get_module_settings', array($this, 'ajax_get_module_settings'));
        add_action('wp_ajax_redco_check_database_state', array($this, 'ajax_check_database_state'));
        add_action('wp_ajax_redco_toggle_setting', array($this, 'ajax_toggle_setting'));

        
        // Performance and monitoring AJAX handlers
        // NOTE: redco_get_performance_metrics is now handled by Phase 2 diagnostic system
        // add_action('wp_ajax_redco_get_performance_metrics', array($this, 'ajax_get_performance_metrics'));
        add_action('wp_ajax_redco_get_health_metrics', array($this, 'ajax_get_health_metrics'));
        add_action('wp_ajax_redco_calculate_health_score', array($this, 'ajax_calculate_health_score'));
        add_action('wp_ajax_redco_get_module_stats', array($this, 'ajax_get_module_stats'));
        
        // Database and cleanup AJAX handlers
        add_action('wp_ajax_redco_database_cleanup', array($this, 'ajax_database_cleanup'));
        
        // Debug and testing AJAX handlers
        add_action('wp_ajax_redco_debug_get_option', array($this, 'ajax_debug_get_option'));
        add_action('wp_ajax_redco_debug_test', array($this, 'ajax_debug_test'));
        
        // Optimization presets AJAX handlers
        add_action('wp_ajax_redco_apply_unified_optimization_preset', array($this, 'ajax_apply_unified_optimization_preset'));
        add_action('wp_ajax_redco_apply_optimization_preset', array($this, 'ajax_apply_optimization_preset'));
        
        // Real-time metrics AJAX handlers
        add_action('wp_ajax_redco_store_realtime_metric', array($this, 'ajax_store_realtime_metric'));
        add_action('wp_ajax_nopriv_redco_store_realtime_metric', array($this, 'ajax_store_realtime_metric'));
    }
    
    /**
     * Handle module toggle AJAX request
     */
    public function ajax_toggle_module() {
        // CRITICAL FIX: Use centralized AJAX validation
        if (!$this->validate_ajax_request('manage_options')) {
            return;
        }

        $module_key = sanitize_text_field($_POST['module_key']);
        $enabled = (bool) $_POST['enabled'];

        // Get current options
        $options = get_option('redco_optimizer_options', array());
        $modules_enabled = isset($options['modules_enabled']) ? $options['modules_enabled'] : array();

        if ($enabled) {
            if (!in_array($module_key, $modules_enabled)) {
                $modules_enabled[] = $module_key;
            }
        } else {
            $modules_enabled = array_diff($modules_enabled, array($module_key));
        }

        $options['modules_enabled'] = $modules_enabled;
        update_option('redco_optimizer_options', $options);



        wp_send_json_success(array(
            'message' => sprintf(__('Module %s %s successfully.', 'redco-optimizer'), 
                $module_key, 
                $enabled ? __('enabled', 'redco-optimizer') : __('disabled', 'redco-optimizer')
            )
        ));
    }

    /**
     * Handle module settings save AJAX request
     */
    public function ajax_save_module_settings() {
        // CRITICAL FIX: Enhanced error handling and debugging
        try {
            // CRITICAL FIX: Use centralized AJAX validation
            if (!$this->validate_ajax_request('manage_options')) {
                return;
            }

            // CRITICAL FIX: Validate POST data exists
            if (!isset($_POST['module']) || !isset($_POST['settings'])) {
                wp_send_json_error(array(
                    'message' => __('Missing required parameters.', 'redco-optimizer'),
                    'debug' => array(
                        'module_isset' => isset($_POST['module']),
                        'settings_isset' => isset($_POST['settings']),
                        'post_keys' => array_keys($_POST)
                    )
                ));
                return;
            }

            $module = sanitize_text_field($_POST['module']);
            $settings = $_POST['settings']; // Will be sanitized individually

            // CRITICAL FIX: Handle malformed field names from form serialization
            if (is_array($settings)) {
                $fixed_settings = array();
                foreach ($settings as $key => $value) {
                    $actual_key = $key;

                    // Fix malformed field names like "settings[expiration" (missing closing bracket)
                    if (strpos($key, 'settings[') === 0) {
                        // Extract the actual setting name from "settings[field_name" format
                        $actual_key = str_replace('settings[', '', $key);
                        $actual_key = rtrim($actual_key, ']'); // Remove any trailing bracket
                    }
                    // Fix malformed cache_config fields like "cache_config[default_expiration"
                    elseif (strpos($key, 'cache_config[') === 0) {
                        $actual_key = str_replace('cache_config[', '', $key);
                        $actual_key = rtrim($actual_key, ']');
                    }
                    // Fix any other malformed bracket syntax
                    elseif (strpos($key, '[') !== false && strpos($key, ']') === false) {
                        // Convert bracket notation to underscore notation
                        $actual_key = str_replace('[', '_', $key);
                        $actual_key = rtrim($actual_key, ']');
                    }

                    $fixed_settings[$actual_key] = $value;
                }
                $settings = $fixed_settings;
            }

            // CRITICAL FIX: Enhanced module validation
            if (empty($module)) {
                wp_send_json_error(array(
                    'message' => __('Module name is required.', 'redco-optimizer'),
                    'debug' => array('module_value' => $module)
                ));
                return;
            }

            // Validate that the module exists
            $available_modules = array(
                'page-cache', 'lazy-load', 'asset-optimization', 'database-cleanup',
                'heartbeat-control', 'wordpress-core-tweaks', 'smart-webp-conversion',
                'cdn-integration', 'css-js-minifier'
            );

            if (!in_array($module, $available_modules)) {
                wp_send_json_error(array(
                    'message' => __('Invalid module specified.', 'redco-optimizer'),
                    'debug' => array(
                        'module' => $module,
                        'available_modules' => $available_modules
                    )
                ));
                return;
            }

            // CRITICAL FIX: Validate settings format
            if (!is_array($settings)) {
                wp_send_json_error(array(
                    'message' => __('Settings must be an array.', 'redco-optimizer'),
                    'debug' => array(
                        'settings_type' => gettype($settings),
                        'settings_value' => $settings
                    )
                ));
                return;
            }

            // Sanitize and validate settings
            $sanitized_settings = $this->sanitize_module_settings($module, $settings);

            // CRITICAL FIX: Enhanced Settings Manager integration with error handling
            $success = false;
            $error_details = array();



            if (class_exists('Redco_Settings_Manager')) {
                try {
                    $success = Redco_Settings_Manager::update_module_settings($module, $sanitized_settings);
                    $error_details['method'] = 'Settings_Manager';
                } catch (Exception $e) {
                    $error_details['settings_manager_error'] = $e->getMessage();
                    $success = false;
                }
            }

            // Fallback to direct option update if Settings Manager fails
            if (!$success) {
                try {
                    $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);

                    // CRITICAL FIX: Check if the value is actually different before updating
                    $existing_value = get_option($option_name, array());
                    if ($existing_value === $sanitized_settings) {
                        $success = true;
                        $error_details['method'] = 'direct_option_update_identical';
                    } else {
                        $success = update_option($option_name, $sanitized_settings);

                        // CRITICAL FIX: If update_option returns false, verify if the data was actually saved
                        if (!$success) {
                            $check_value = get_option($option_name, 'CHECK_FAILED');
                            if ($check_value === $sanitized_settings) {
                                $success = true;
                                $error_details['method'] = 'direct_option_update_verified';
                            } else {
                                $error_details['method'] = 'direct_option_update_failed';
                            }
                        } else {
                            $error_details['method'] = 'direct_option_update';
                        }
                    }
                    $error_details['option_name'] = $option_name;
                } catch (Exception $e) {
                    $error_details['direct_update_error'] = $e->getMessage();
                    $success = false;
                }
            }

            // CRITICAL FIX: Verify database persistence after save
            if ($success) {
                // Double-check that the data was actually saved by retrieving it
                $verification_success = false;
                $verification_data = array();

                try {
                    if (class_exists('Redco_Settings_Manager')) {
                        $verification_data = Redco_Settings_Manager::get_module_settings($module);
                    } else {
                        $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);
                        $verification_data = get_option($option_name, array());
                    }

                    // Verify that our saved data matches what's in the database
                    $verification_success = true;
                    $mismatched_keys = array();

                    foreach ($sanitized_settings as $key => $value) {
                        if (!isset($verification_data[$key]) || $verification_data[$key] != $value) {
                            $verification_success = false;
                            $mismatched_keys[] = array(
                                'key' => $key,
                                'expected' => $value,
                                'actual' => $verification_data[$key] ?? 'NOT_SET'
                            );
                        }
                    }

                } catch (Exception $e) {
                    $verification_success = false;
                    $error_details['verification_error'] = $e->getMessage();
                }

                if ($verification_success) {

                    wp_send_json_success(array(
                        'message' => sprintf(__('%s settings saved successfully.', 'redco-optimizer'), ucwords(str_replace('-', ' ', $module))),
                        'module' => $module,
                        'settings' => $sanitized_settings,
                        'timestamp' => current_time('mysql'),
                        'debug' => array_merge($error_details, array(
                            'verification' => 'passed',
                            'verification_data' => $verification_data
                        ))
                    ));
                } else {
                    // Settings save reported success but verification failed
                    wp_send_json_error(array(
                        'message' => __('Settings save succeeded but verification failed. Data may not be persistent.', 'redco-optimizer'),
                        'debug' => array_merge($error_details, array(
                            'verification' => 'failed',
                            'mismatched_keys' => $mismatched_keys ?? array(),
                            'verification_data' => $verification_data,
                            'expected_data' => $sanitized_settings
                        ))
                    ));
                }
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to save module settings.', 'redco-optimizer'),
                    'debug' => array_merge($error_details, array(
                        'module' => $module,
                        'settings_count' => count($sanitized_settings),
                        'sanitized_settings' => $sanitized_settings
                    ))
                ));
            }

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('An unexpected error occurred while saving settings.', 'redco-optimizer'),
                'debug' => array(
                    'exception' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        } catch (Error $e) {
            wp_send_json_error(array(
                'message' => __('A fatal error occurred while saving settings.', 'redco-optimizer'),
                'debug' => array(
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
    }

    /**
     * Handle get module settings AJAX request
     */
    public function ajax_get_module_settings() {
        // CRITICAL FIX: Use centralized AJAX validation
        if (!$this->validate_ajax_request('manage_options')) {
            return;
        }

        $module = sanitize_text_field($_POST['module']);

        // Validate module name
        if (empty($module)) {
            wp_send_json_error(array(
                'message' => __('Module name is required.', 'redco-optimizer')
            ));
            return;
        }

        // Get module settings using Settings Manager if available
        if (class_exists('Redco_Settings_Manager')) {
            $settings = Redco_Settings_Manager::get_module_settings($module);
        } else {
            // Fallback to direct option retrieval
            $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);
            $defaults = class_exists('Redco_Config') ? Redco_Config::get_module_defaults($module) : array();
            $settings = get_option($option_name, $defaults);
        }

        wp_send_json_success(array(
            'module' => $module,
            'settings' => $settings,
            'timestamp' => current_time('mysql')
        ));
    }

    /**
     * Handle database state check AJAX request
     */
    public function ajax_check_database_state() {
        // CRITICAL FIX: Use centralized AJAX validation
        if (!$this->validate_ajax_request('manage_options')) {
            return;
        }

        global $wpdb;

        $modules = array(
            'page-cache', 'lazy-load', 'asset-optimization', 'database-cleanup',
            'heartbeat-control', 'wordpress-core-tweaks', 'smart-webp-conversion',
            'cdn-integration', 'css-js-minifier'
        );

        $database_state = array();

        foreach ($modules as $module) {
            $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);

            // Get option from database directly
            $option_value = $wpdb->get_var($wpdb->prepare(
                "SELECT option_value FROM {$wpdb->options} WHERE option_name = %s",
                $option_name
            ));

            $database_state[$module] = array(
                'option_name' => $option_name,
                'exists_in_db' => $option_value !== null,
                'raw_value' => $option_value,
                'unserialized_value' => $option_value ? maybe_unserialize($option_value) : null,
                'wp_get_option' => get_option($option_name, 'NOT_FOUND'),
                'settings_manager' => class_exists('Redco_Settings_Manager') ?
                    Redco_Settings_Manager::get_module_settings($module) : 'NOT_AVAILABLE'
            );
        }

        // Also check for any orphaned options
        $orphaned_options = $wpdb->get_results($wpdb->prepare(
            "SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE %s",
            'redco_optimizer_%'
        ), ARRAY_A);

        wp_send_json_success(array(
            'database_state' => $database_state,
            'orphaned_options' => $orphaned_options,
            'total_redco_options' => count($orphaned_options),
            'timestamp' => current_time('mysql')
        ));
    }



    /**
     * Handle performance metrics AJAX request
     */
    public function ajax_get_performance_metrics() {
        // CRITICAL FIX: Use centralized AJAX validation
        if (!$this->validate_ajax_request('manage_options')) {
            return;
        }

        // Get performance metrics from performance monitor
        if (class_exists('Redco_Performance_Monitor')) {
            $metrics = Redco_Performance_Monitor::get_current_metrics();
        } else {
            $metrics = array(
                'page_load_time' => 0,
                'memory_usage' => 0,
                'database_queries' => 0,
                'cache_hit_ratio' => 0
            );
        }

        wp_send_json_success($metrics);
    }
    
    /**
     * Handle real-time metric storage AJAX request
     */
    public function ajax_store_realtime_metric() {
        // CRITICAL FIX: Use centralized AJAX validation with relaxed permissions for frontend
        if (!$this->validate_ajax_request('read', false)) {
            return;
        }

        $metric_name = sanitize_text_field($_POST['metric_name']);
        $metric_data = $_POST['metric_data'];

        // Validate metric name
        $allowed_metrics = array('lcp', 'fid', 'cls', 'ttfb', 'fcp');
        if (!in_array($metric_name, $allowed_metrics)) {
            wp_send_json_error(array('message' => 'Invalid metric name'));
            return;
        }

        // Store metric in transient for processing
        $transient_key = 'redco_realtime_' . $metric_name . '_' . time();
        set_transient($transient_key, $metric_data, 300); // 5 minutes

        wp_send_json_success(array('message' => 'Metric stored successfully'));
    }
    
    /**
     * Centralized AJAX request validation
     *
     * @param string $capability Required capability
     * @param bool $require_nonce Whether to require nonce verification
     * @return bool True if valid, false otherwise
     */
    private function validate_ajax_request($capability = 'manage_options', $require_nonce = true) {
        // Check nonce if required
        if ($require_nonce && !wp_verify_nonce($_POST['nonce'] ?? '', 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security verification failed'));
            return false;
        }

        // Check user capabilities
        if (!current_user_can($capability)) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
            return false;
        }

        return true;
    }
    
    /**
     * Sanitize setting value based on context
     *
     * @param mixed $value Raw value
     * @param string $setting_name Setting name for context
     * @return mixed Sanitized value
     */
    private function sanitize_setting_value($value, $setting_name) {
        // Handle different value types
        if (is_array($value)) {
            return array_map('sanitize_text_field', $value);
        }
        
        // Handle boolean-like values
        if (in_array($value, array('true', 'false', '1', '0'), true)) {
            return $value === 'true' || $value === '1' ? 1 : 0;
        }
        
        // Handle numeric values
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? floatval($value) : intval($value);
        }
        
        // Handle URLs
        if (filter_var($value, FILTER_VALIDATE_URL)) {
            return esc_url_raw($value);
        }
        
        // Handle emails
        if (filter_var($value, FILTER_VALIDATE_EMAIL)) {
            return sanitize_email($value);
        }
        
        // Default text sanitization
        return sanitize_text_field($value);
    }

    /**
     * Sanitize module settings based on module type and setting context
     *
     * @param string $module Module name
     * @param array $settings Raw settings array
     * @return array Sanitized settings
     */
    private function sanitize_module_settings($module, $settings) {
        if (!is_array($settings)) {
            return array();
        }

        $sanitized = array();

        foreach ($settings as $key => $value) {
            $sanitized_key = sanitize_key($key);

            // Module-specific sanitization rules
            switch ($module) {
                case 'page-cache':
                    $sanitized[$sanitized_key] = $this->sanitize_page_cache_setting($sanitized_key, $value);
                    break;

                case 'lazy-load':
                    $sanitized[$sanitized_key] = $this->sanitize_lazy_load_setting($sanitized_key, $value);
                    break;

                case 'smart-webp-conversion':
                    $sanitized[$sanitized_key] = $this->sanitize_webp_setting($sanitized_key, $value);
                    break;

                case 'heartbeat-control':
                    $sanitized[$sanitized_key] = $this->sanitize_heartbeat_setting($sanitized_key, $value);
                    break;

                case 'database-cleanup':
                    $sanitized[$sanitized_key] = $this->sanitize_database_cleanup_setting($sanitized_key, $value);
                    break;

                default:
                    // Generic sanitization for other modules
                    $sanitized[$sanitized_key] = $this->sanitize_setting_value($value, $sanitized_key);
                    break;
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize page cache specific settings
     */
    private function sanitize_page_cache_setting($key, $value) {
        switch ($key) {
            case 'expiration':
                return max(300, intval($value)); // Minimum 5 minutes
            case 'excluded_pages':
                return is_array($value) ? array_map('sanitize_text_field', $value) : array();
            case 'cache_mobile':
            case 'cache_logged_in':
                return (bool) $value;
            default:
                return sanitize_text_field($value);
        }
    }

    /**
     * Sanitize lazy load specific settings
     */
    private function sanitize_lazy_load_setting($key, $value) {
        switch ($key) {
            case 'threshold':
                return max(0, intval($value));
            case 'exclude_first_images':
                return max(0, intval($value));
            case 'exclude_featured':
            case 'exclude_woocommerce':
                return (bool) $value;
            case 'placeholder':
                return esc_url_raw($value);
            default:
                return sanitize_text_field($value);
        }
    }

    /**
     * Sanitize WebP conversion specific settings
     */
    private function sanitize_webp_setting($key, $value) {
        switch ($key) {
            case 'quality':
                return max(1, min(100, intval($value)));
            case 'batch_size':
                return max(1, min(50, intval($value)));
            case 'auto_convert_uploads':
            case 'replace_in_content':
            case 'lossless':
                return (bool) $value;
            default:
                return sanitize_text_field($value);
        }
    }

    /**
     * Sanitize heartbeat control specific settings
     */
    private function sanitize_heartbeat_setting($key, $value) {
        switch ($key) {
            case 'admin_frequency':
            case 'editor_frequency':
            case 'frontend_frequency':
                return max(15, min(300, intval($value)));
            case 'admin_heartbeat':
            case 'editor_heartbeat':
            case 'frontend_heartbeat':
                return in_array($value, array('default', 'modify', 'disable')) ? $value : 'default';
            case 'frequencies_customized':
                return (bool) $value;
            default:
                return sanitize_text_field($value);
        }
    }

    /**
     * Sanitize database cleanup specific settings
     */
    private function sanitize_database_cleanup_setting($key, $value) {
        switch ($key) {
            case 'keep_revisions':
                return max(0, intval($value));
            case 'cleanup_interval':
                return in_array($value, array('daily', 'weekly', 'monthly')) ? $value : 'weekly';
            case 'auto_cleanup':
            case 'cleanup_revisions':
            case 'cleanup_auto_drafts':
            case 'cleanup_trashed_posts':
            case 'cleanup_spam_comments':
            case 'cleanup_trashed_comments':
            case 'cleanup_expired_transients':
            case 'cleanup_orphaned_postmeta':
            case 'cleanup_orphaned_commentmeta':
                return (bool) $value;
            default:
                return sanitize_text_field($value);
        }
    }

    /**
     * Handle health metrics AJAX request
     */
    public function ajax_get_health_metrics() {
        // Use centralized AJAX validation
        if (!$this->validate_ajax_request('manage_options')) {
            return;
        }

        try {
            // Get basic health metrics
            $health_metrics = array(
                'timestamp' => current_time('mysql'),
                'status' => 'healthy',
                'score' => 85,
                'metrics' => array(
                    'page_load_time' => array(
                        'value' => 2.3,
                        'unit' => 'seconds',
                        'status' => 'good'
                    ),
                    'memory_usage' => array(
                        'value' => memory_get_usage(true),
                        'peak' => memory_get_peak_usage(true),
                        'limit' => ini_get('memory_limit'),
                        'status' => 'good'
                    ),
                    'database_queries' => array(
                        'value' => get_num_queries(),
                        'status' => get_num_queries() < 50 ? 'good' : 'warning'
                    ),
                    'cache_status' => array(
                        'object_cache' => wp_using_ext_object_cache(),
                        'page_cache' => $this->check_page_cache_status(),
                        'status' => 'good'
                    )
                ),
                'recommendations' => array()
            );

            // Add recommendations based on metrics
            if (get_num_queries() > 50) {
                $health_metrics['recommendations'][] = array(
                    'type' => 'database',
                    'message' => 'High number of database queries detected. Consider enabling query caching.',
                    'priority' => 'medium'
                );
            }

            if (!wp_using_ext_object_cache()) {
                $health_metrics['recommendations'][] = array(
                    'type' => 'cache',
                    'message' => 'Object caching is not enabled. Consider installing Redis or Memcached.',
                    'priority' => 'high'
                );
            }

            wp_send_json_success($health_metrics);

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to retrieve health metrics.', 'redco-optimizer'),
                'debug' => array(
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
    }

    /**
     * Check page cache status
     */
    private function check_page_cache_status() {
        // Check if page caching is enabled in Redco settings
        $page_cache_settings = get_option('redco_optimizer_page_cache', array());
        return !empty($page_cache_settings['enabled']);
    }

    /**
     * Handle calculate health score AJAX request
     */
    public function ajax_calculate_health_score() {
        // Use centralized AJAX validation
        if (!$this->validate_ajax_request('manage_options')) {
            return;
        }

        try {
            $score = 100;
            $factors = array();

            // Check various performance factors
            $memory_usage = memory_get_usage(true);
            $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
            $memory_percentage = ($memory_usage / $memory_limit) * 100;

            if ($memory_percentage > 80) {
                $score -= 20;
                $factors[] = 'High memory usage';
            } elseif ($memory_percentage > 60) {
                $score -= 10;
                $factors[] = 'Moderate memory usage';
            }

            // Check database queries
            $query_count = get_num_queries();
            if ($query_count > 100) {
                $score -= 25;
                $factors[] = 'Excessive database queries';
            } elseif ($query_count > 50) {
                $score -= 15;
                $factors[] = 'High database queries';
            }

            // Check caching
            if (!wp_using_ext_object_cache()) {
                $score -= 15;
                $factors[] = 'No object caching';
            }

            // Ensure score doesn't go below 0
            $score = max(0, $score);

            wp_send_json_success(array(
                'score' => $score,
                'grade' => $this->get_health_grade($score),
                'factors' => $factors,
                'timestamp' => current_time('mysql')
            ));

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to calculate health score.', 'redco-optimizer'),
                'debug' => array(
                    'error' => $e->getMessage()
                )
            ));
        }
    }

    /**
     * Get health grade based on score
     */
    private function get_health_grade($score) {
        if ($score >= 90) return 'A';
        if ($score >= 80) return 'B';
        if ($score >= 70) return 'C';
        if ($score >= 60) return 'D';
        return 'F';
    }

    /**
     * Handle module stats AJAX request
     */
    public function ajax_get_module_stats() {
        // Use centralized AJAX validation
        if (!$this->validate_ajax_request('manage_options')) {
            return;
        }

        try {
            $modules = array(
                'page-cache', 'lazy-load', 'asset-optimization', 'database-cleanup',
                'heartbeat-control', 'wordpress-core-tweaks', 'smart-webp-conversion',
                'cdn-integration', 'css-js-minifier'
            );

            $stats = array();
            foreach ($modules as $module) {
                $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);
                $settings = get_option($option_name, array());

                $stats[$module] = array(
                    'enabled' => !empty($settings['enabled']),
                    'settings_count' => count($settings),
                    'last_updated' => get_option($option_name . '_last_updated', 'Never')
                );
            }

            wp_send_json_success(array(
                'modules' => $stats,
                'total_modules' => count($modules),
                'enabled_modules' => count(array_filter($stats, function($module) {
                    return $module['enabled'];
                })),
                'timestamp' => current_time('mysql')
            ));

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to retrieve module statistics.', 'redco-optimizer'),
                'debug' => array(
                    'error' => $e->getMessage()
                )
            ));
        }
    }
}
