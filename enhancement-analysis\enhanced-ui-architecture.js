/**
 * Enhanced UI Architecture for Granular Fix Controls
 * 
 * Provides intuitive interface for managing tiered fixes, scheduling, and previews
 */

class RedcoEnhancedUI {
    constructor() {
        this.currentView = 'overview';
        this.selectedFixes = new Set();
        this.previewMode = false;
        this.filterState = {
            tier: 'all',
            category: 'all',
            status: 'all'
        };
        
        this.init();
    }
    
    /**
     * Initialize enhanced UI components
     */
    init() {
        this.initTieredFixInterface();
        this.initSchedulingInterface();
        this.initPreviewSystem();
        this.initRealTimeMonitoring();
        this.initAdvancedFiltering();
        this.bindEvents();
    }
    
    /**
     * Initialize tiered fix interface with safety indicators
     */
    initTieredFixInterface() {
        const fixContainer = document.getElementById('redco-fix-container');
        
        // Create tier tabs
        const tierTabs = this.createTierTabs();
        fixContainer.appendChild(tierTabs);
        
        // Create fix grid with categories
        const fixGrid = this.createFixGrid();
        fixContainer.appendChild(fixGrid);
        
        // Create bulk action controls
        const bulkControls = this.createBulkActionControls();
        fixContainer.appendChild(bulkControls);
    }
    
    /**
     * Create tier navigation tabs
     */
    createTierTabs() {
        const tabContainer = document.createElement('div');
        tabContainer.className = 'redco-tier-tabs';
        
        const tiers = [
            { id: 'safe', name: 'Safe Fixes', icon: '🟢', count: 12 },
            { id: 'moderate', name: 'Moderate Fixes', icon: '🟡', count: 8 },
            { id: 'advanced', name: 'Advanced Fixes', icon: '🔴', count: 5 }
        ];
        
        tiers.forEach(tier => {
            const tab = document.createElement('button');
            tab.className = `redco-tier-tab ${tier.id === 'safe' ? 'active' : ''}`;
            tab.dataset.tier = tier.id;
            tab.innerHTML = `
                <span class="tier-icon">${tier.icon}</span>
                <span class="tier-name">${tier.name}</span>
                <span class="tier-count">${tier.count}</span>
            `;
            
            tab.addEventListener('click', () => this.switchTier(tier.id));
            tabContainer.appendChild(tab);
        });
        
        return tabContainer;
    }
    
    /**
     * Create categorized fix grid
     */
    createFixGrid() {
        const gridContainer = document.createElement('div');
        gridContainer.className = 'redco-fix-grid';
        
        const categories = [
            { id: 'database', name: 'Database', icon: 'dashicons-database' },
            { id: 'frontend', name: 'Frontend', icon: 'dashicons-performance' },
            { id: 'server', name: 'Server', icon: 'dashicons-admin-settings' },
            { id: 'security', name: 'Security', icon: 'dashicons-shield' },
            { id: 'seo', name: 'SEO', icon: 'dashicons-search' },
            { id: 'images', name: 'Images', icon: 'dashicons-format-image' }
        ];
        
        categories.forEach(category => {
            const categorySection = this.createCategorySection(category);
            gridContainer.appendChild(categorySection);
        });
        
        return gridContainer;
    }
    
    /**
     * Create category section with fixes
     */
    createCategorySection(category) {
        const section = document.createElement('div');
        section.className = 'redco-category-section';
        section.dataset.category = category.id;
        
        // Category header
        const header = document.createElement('div');
        header.className = 'category-header';
        header.innerHTML = `
            <span class="dashicons ${category.icon}"></span>
            <h3>${category.name}</h3>
            <span class="category-toggle">▼</span>
        `;
        
        // Category fixes container
        const fixesContainer = document.createElement('div');
        fixesContainer.className = 'category-fixes';
        
        // Load fixes for this category
        this.loadCategoryFixes(category.id, fixesContainer);
        
        section.appendChild(header);
        section.appendChild(fixesContainer);
        
        // Toggle functionality
        header.addEventListener('click', () => {
            section.classList.toggle('collapsed');
        });
        
        return section;
    }
    
    /**
     * Create individual fix card with enhanced information
     */
    createFixCard(fix) {
        const card = document.createElement('div');
        card.className = `redco-fix-card tier-${fix.tier}`;
        card.dataset.fixId = fix.id;
        
        card.innerHTML = `
            <div class="fix-header">
                <div class="fix-selection">
                    <input type="checkbox" id="fix-${fix.id}" class="fix-checkbox">
                    <label for="fix-${fix.id}"></label>
                </div>
                <div class="fix-tier-indicator ${fix.tier}">
                    ${this.getTierIcon(fix.tier)}
                </div>
                <h4 class="fix-title">${fix.title}</h4>
            </div>
            
            <div class="fix-content">
                <p class="fix-description">${fix.description}</p>
                
                <div class="fix-metrics">
                    <div class="metric">
                        <span class="metric-label">Impact:</span>
                        <span class="metric-value impact-${fix.estimated_impact}">
                            ${fix.estimated_impact.toUpperCase()}
                        </span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Time:</span>
                        <span class="metric-value">${fix.estimated_time}</span>
                    </div>
                </div>
                
                <div class="fix-safety-info">
                    ${this.createSafetyIndicators(fix)}
                </div>
                
                <div class="fix-prerequisites">
                    ${this.createPrerequisitesList(fix)}
                </div>
            </div>
            
            <div class="fix-actions">
                <button class="button button-secondary preview-fix" data-fix-id="${fix.id}">
                    Preview
                </button>
                <button class="button button-primary apply-fix" data-fix-id="${fix.id}">
                    Apply Now
                </button>
                <button class="button schedule-fix" data-fix-id="${fix.id}">
                    Schedule
                </button>
            </div>
        `;
        
        this.bindFixCardEvents(card, fix);
        return card;
    }
    
    /**
     * Create safety indicators for fix
     */
    createSafetyIndicators(fix) {
        const indicators = [];
        
        if (fix.safety_info.backup_required) {
            indicators.push('<span class="safety-indicator backup-required">🛡️ Backup Required</span>');
        }
        
        if (fix.safety_info.reversible) {
            indicators.push('<span class="safety-indicator reversible">↩️ Reversible</span>');
        }
        
        if (fix.safety_info.conflicts.length > 0) {
            indicators.push('<span class="safety-indicator conflicts">⚠️ Potential Conflicts</span>');
        }
        
        return indicators.join('');
    }
    
    /**
     * Initialize scheduling interface
     */
    initSchedulingInterface() {
        this.createSchedulingModal();
        this.createScheduleCalendar();
        this.initMaintenanceWindows();
    }
    
    /**
     * Create scheduling modal
     */
    createSchedulingModal() {
        const modal = document.createElement('div');
        modal.id = 'redco-scheduling-modal';
        modal.className = 'redco-modal';
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Schedule Fix Application</h2>
                    <button class="modal-close">&times;</button>
                </div>
                
                <div class="modal-body">
                    <div class="schedule-options">
                        <label class="schedule-option">
                            <input type="radio" name="schedule_type" value="immediate" checked>
                            <span class="option-content">
                                <strong>Apply Immediately</strong>
                                <small>Execute the fix right now</small>
                            </span>
                        </label>
                        
                        <label class="schedule-option">
                            <input type="radio" name="schedule_type" value="maintenance_window">
                            <span class="option-content">
                                <strong>Next Maintenance Window</strong>
                                <small>Sunday 2:00 AM (in 3 days)</small>
                            </span>
                        </label>
                        
                        <label class="schedule-option">
                            <input type="radio" name="schedule_type" value="low_traffic">
                            <span class="option-content">
                                <strong>Low Traffic Period</strong>
                                <small>Tonight at 3:00 AM</small>
                            </span>
                        </label>
                        
                        <label class="schedule-option">
                            <input type="radio" name="schedule_type" value="custom_time">
                            <span class="option-content">
                                <strong>Custom Date & Time</strong>
                                <small>Choose specific date and time</small>
                            </span>
                        </label>
                    </div>
                    
                    <div class="custom-datetime" style="display: none;">
                        <input type="datetime-local" id="custom-datetime" class="regular-text">
                    </div>
                    
                    <div class="notification-settings">
                        <h3>Notifications</h3>
                        <label>
                            <input type="checkbox" id="email-notifications" checked>
                            Email notifications
                        </label>
                        <input type="email" id="notification-email" placeholder="<EMAIL>" class="regular-text">
                    </div>
                    
                    <div class="rollback-settings">
                        <h3>Automatic Rollback</h3>
                        <label>
                            <input type="checkbox" id="auto-rollback">
                            Enable automatic rollback if issues detected
                        </label>
                        <select id="rollback-timeframe">
                            <option value="1">After 1 hour</option>
                            <option value="6">After 6 hours</option>
                            <option value="24" selected>After 24 hours</option>
                            <option value="72">After 72 hours</option>
                        </select>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button class="button button-secondary cancel-schedule">Cancel</button>
                    <button class="button button-primary confirm-schedule">Schedule Fix</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        this.bindSchedulingEvents(modal);
    }
    
    /**
     * Initialize preview system
     */
    initPreviewSystem() {
        this.createPreviewModal();
        this.initPreviewComparison();
    }
    
    /**
     * Create preview modal with before/after comparison
     */
    createPreviewModal() {
        const modal = document.createElement('div');
        modal.id = 'redco-preview-modal';
        modal.className = 'redco-modal large-modal';
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Fix Preview</h2>
                    <button class="modal-close">&times;</button>
                </div>
                
                <div class="modal-body">
                    <div class="preview-tabs">
                        <button class="preview-tab active" data-tab="overview">Overview</button>
                        <button class="preview-tab" data-tab="metrics">Metrics</button>
                        <button class="preview-tab" data-tab="files">File Changes</button>
                        <button class="preview-tab" data-tab="database">Database</button>
                    </div>
                    
                    <div class="preview-content">
                        <div class="preview-panel active" id="preview-overview">
                            <div class="before-after-comparison">
                                <div class="comparison-section">
                                    <h3>Current State</h3>
                                    <div id="current-metrics"></div>
                                </div>
                                <div class="comparison-arrow">→</div>
                                <div class="comparison-section">
                                    <h3>After Fix</h3>
                                    <div id="projected-metrics"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="preview-panel" id="preview-metrics">
                            <canvas id="metrics-chart"></canvas>
                        </div>
                        
                        <div class="preview-panel" id="preview-files">
                            <div class="file-changes-list"></div>
                        </div>
                        
                        <div class="preview-panel" id="preview-database">
                            <div class="database-changes-list"></div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button class="button button-secondary cancel-preview">Cancel</button>
                    <button class="button button-primary apply-after-preview">Apply Fix</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        this.bindPreviewEvents(modal);
    }
    
    /**
     * Initialize real-time monitoring dashboard
     */
    initRealTimeMonitoring() {
        this.createMonitoringDashboard();
        this.initLiveMetrics();
        this.initAlertSystem();
    }
    
    /**
     * Create real-time monitoring dashboard
     */
    createMonitoringDashboard() {
        const dashboard = document.createElement('div');
        dashboard.id = 'redco-monitoring-dashboard';
        dashboard.className = 'monitoring-dashboard';
        
        dashboard.innerHTML = `
            <div class="dashboard-header">
                <h2>Real-Time Performance Monitoring</h2>
                <div class="dashboard-controls">
                    <button class="button refresh-metrics">Refresh</button>
                    <select id="monitoring-interval">
                        <option value="5">5 seconds</option>
                        <option value="30" selected>30 seconds</option>
                        <option value="60">1 minute</option>
                    </select>
                </div>
            </div>
            
            <div class="dashboard-grid">
                <div class="metric-card">
                    <h3>Page Load Time</h3>
                    <div class="metric-value" id="load-time-metric">--</div>
                    <div class="metric-trend" id="load-time-trend"></div>
                </div>
                
                <div class="metric-card">
                    <h3>TTFB</h3>
                    <div class="metric-value" id="ttfb-metric">--</div>
                    <div class="metric-trend" id="ttfb-trend"></div>
                </div>
                
                <div class="metric-card">
                    <h3>Core Web Vitals</h3>
                    <div class="cwv-metrics">
                        <div class="cwv-metric">
                            <span class="cwv-label">LCP</span>
                            <span class="cwv-value" id="lcp-value">--</span>
                        </div>
                        <div class="cwv-metric">
                            <span class="cwv-label">FID</span>
                            <span class="cwv-value" id="fid-value">--</span>
                        </div>
                        <div class="cwv-metric">
                            <span class="cwv-label">CLS</span>
                            <span class="cwv-value" id="cls-value">--</span>
                        </div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <h3>Active Alerts</h3>
                    <div class="alert-summary" id="alert-summary">
                        <div class="alert-count critical">0 Critical</div>
                        <div class="alert-count warning">0 Warnings</div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-charts">
                <div class="chart-container">
                    <canvas id="performance-timeline-chart"></canvas>
                </div>
            </div>
        `;
        
        // Insert dashboard into appropriate location
        const container = document.getElementById('redco-diagnostic-container');
        if (container) {
            container.insertBefore(dashboard, container.firstChild);
        }
    }
    
    /**
     * Initialize live metrics updates
     */
    initLiveMetrics() {
        this.metricsInterval = setInterval(() => {
            this.updateLiveMetrics();
        }, 30000); // Update every 30 seconds
        
        // Initial load
        this.updateLiveMetrics();
    }
    
    /**
     * Update live metrics display
     */
    updateLiveMetrics() {
        jQuery.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_get_live_metrics',
                nonce: redcoAjax.nonce
            },
            success: (response) => {
                if (response.success) {
                    this.displayLiveMetrics(response.data);
                }
            }
        });
    }
    
    /**
     * Display live metrics in dashboard
     */
    displayLiveMetrics(metrics) {
        // Update load time
        const loadTimeElement = document.getElementById('load-time-metric');
        if (loadTimeElement && metrics.page_load_time) {
            loadTimeElement.textContent = metrics.page_load_time + 'ms';
            this.updateTrend('load-time-trend', metrics.page_load_time_trend);
        }
        
        // Update TTFB
        const ttfbElement = document.getElementById('ttfb-metric');
        if (ttfbElement && metrics.ttfb) {
            ttfbElement.textContent = metrics.ttfb + 'ms';
            this.updateTrend('ttfb-trend', metrics.ttfb_trend);
        }
        
        // Update Core Web Vitals
        if (metrics.core_web_vitals) {
            const lcpElement = document.getElementById('lcp-value');
            const fidElement = document.getElementById('fid-value');
            const clsElement = document.getElementById('cls-value');
            
            if (lcpElement) lcpElement.textContent = metrics.core_web_vitals.lcp + 's';
            if (fidElement) fidElement.textContent = metrics.core_web_vitals.fid + 'ms';
            if (clsElement) clsElement.textContent = metrics.core_web_vitals.cls;
        }
        
        // Update alerts
        this.updateAlertSummary(metrics.alerts);
    }
    
    /**
     * Bind all event handlers
     */
    bindEvents() {
        // Tier switching
        document.addEventListener('click', (e) => {
            if (e.target.matches('.redco-tier-tab')) {
                this.switchTier(e.target.dataset.tier);
            }
        });
        
        // Fix selection
        document.addEventListener('change', (e) => {
            if (e.target.matches('.fix-checkbox')) {
                this.toggleFixSelection(e.target.dataset.fixId, e.target.checked);
            }
        });
        
        // Preview buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.preview-fix')) {
                this.showFixPreview(e.target.dataset.fixId);
            }
        });
        
        // Schedule buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.schedule-fix')) {
                this.showSchedulingModal(e.target.dataset.fixId);
            }
        });
        
        // Apply buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.apply-fix')) {
                this.applyFix(e.target.dataset.fixId);
            }
        });
    }
}
