<?php
/**
 * Configuration Management for Redco Optimizer
 * 
 * Centralized configuration system to replace hardcoded values
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Config {
    
    /**
     * Default module settings
     */
    const DEFAULT_SETTINGS = array(
        'page-cache' => array(
            'expiration' => 21600, // 6 hours
            'excluded_pages' => array(),
            'cache_mobile' => true,
            'cache_logged_in' => false
        ),
        'lazy-load' => array(
            'exclude_featured' => false,
            'exclude_woocommerce' => false,
            'exclude_first_images' => 2,
            'threshold' => 200,
            'placeholder' => 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E'
        ),
        'asset-optimization' => array(
            'minify_css' => true,
            'minify_js' => true,
            'minify_inline' => true,
            'exclude_css' => array(),
            'exclude_js' => array('jquery-core', 'jquery-migrate'),
            'critical_css' => true,
            'defer_non_critical' => true,
            'optimize_js' => true,
            'optimize_fonts' => true,
            'resource_hints' => true,
            'preconnect_google_fonts' => true,
            'preconnect_analytics' => true,
            'combine_css' => false,
            'combine_js' => false,
            'async_js' => true,
            'defer_js' => true,
            'preload_critical' => true,
            'remove_unused_css' => false,
            'enable_gzip' => true,
            'cache_duration' => 86400,
            'enable_brotli' => false
        ),
        'heartbeat-control' => array(
            'admin_heartbeat' => 'modify',
            'admin_frequency' => 60,
            'editor_heartbeat' => 'modify',
            'editor_frequency' => 30,
            'frontend_heartbeat' => 'disable',
            'frontend_frequency' => 60
        ),
        'smart-webp-conversion' => array(
            'auto_convert_uploads' => true,
            'replace_in_content' => true,
            'quality' => 85,
            'lossless' => false,
            'backup_original' => true, // Always backup originals for safety
            'batch_size' => 10,
            'convert_thumbnails' => true,
            'smart_quality' => true,
            'max_width' => 2048,
            'max_height' => 2048,
            'memory_optimization' => true
        ),
        'wordpress-core-tweaks' => array(
            'emoji_remove_frontend' => true,
            'emoji_remove_admin' => false,
            'emoji_remove_feeds' => true,
            'emoji_remove_emails' => true,
            'remove_css_versions' => true,
            'remove_js_versions' => true,
            'remove_theme_versions' => true,
            'remove_plugin_versions' => true,
            'remove_wp_version' => true,
            'exclude_handles' => array()
        ),

        'database-cleanup' => array(
            'auto_cleanup' => false,
            'cleanup_frequency' => 'weekly',
            'cleanup_revisions' => true,
            'cleanup_auto_drafts' => true,
            'cleanup_trashed_posts' => true,
            'cleanup_spam_comments' => true,
            'cleanup_trashed_comments' => true,
            'cleanup_expired_transients' => true,
            'cleanup_orphaned_postmeta' => true,
            'cleanup_orphaned_commentmeta' => true,
            'keep_revisions' => 5,
            'older_than_days' => 30,
            'backup_before_cleanup' => true,
            'max_execution_time' => 300,
            'batch_size' => 100
        ),
        'cdn-integration' => array(
            'enabled' => false,
            'provider' => '',
            'enable_images' => true,
            'enable_css' => true,
            'enable_js' => true,
            'enable_fonts' => true,
            'enable_media' => false,
            'enable_documents' => false,
            'skip_logged_in' => true,
            'excluded_pages' => array(),
            // Cloudflare settings
            'cloudflare_zone_id' => '',
            'cloudflare_api_token' => '',
            'cloudflare_zone_url' => '',
            // BunnyCDN settings
            'bunnycdn_api_key' => '',
            'bunnycdn_pull_zone_id' => '',
            'bunnycdn_pull_zone_url' => '',
            // KeyCDN settings
            'keycdn_api_key' => '',
            'keycdn_zone_id' => '',
            'keycdn_zone_url' => '',
            // Amazon CloudFront settings
            'cloudfront_distribution_id' => '',
            'cloudfront_distribution_url' => '',
            'aws_access_key' => '',
            'aws_secret_key' => '',
            // Sucuri settings
            'sucuri_api_key' => '',
            'sucuri_api_secret' => '',
            'sucuri_cdn_url' => '',
            // Fastly settings
            'fastly_api_token' => '',
            'fastly_service_id' => '',
            'fastly_service_url' => '',
            // Custom CDN settings
            'custom_cdn_url' => ''
        )
    );
    
    /**
     * Performance thresholds and limits
     */
    const PERFORMANCE_LIMITS = array(
        'cache_expiration_min' => 3600,     // 1 hour minimum
        'cache_expiration_max' => 604800,   // 1 week maximum
        'batch_size_min' => 1,
        'batch_size_max' => 50,
        'quality_min' => 30,
        'quality_max' => 100,
        'threshold_min' => 50,
        'threshold_max' => 1000,
        'heartbeat_min' => 15,
        'heartbeat_max' => 300,
        'memory_limit_min' => 67108864,     // 64MB
        'execution_time_min' => 30
    );
    
    /**
     * File and directory paths
     */
    const PATHS = array(
        'cache_dir' => 'redco-cache',
        'backup_dir' => 'redco-backups',
        'logs_dir' => 'redco-logs',
        'temp_dir' => 'redco-temp'
    );
    
    /**
     * Cache and timeout settings
     */
    const CACHE_SETTINGS = array(
        'stats_cache_duration' => 300,      // 5 minutes
        'api_cache_duration' => 3600,       // 1 hour
        'settings_cache_duration' => 3600,  // 1 hour
        'default_timeout' => 30,
        'api_timeout' => 60,
        'validation_timeout' => 30
    );

    /**
     * Security settings
     */
    const SECURITY_SETTINGS = array(
        'required_capability' => 'manage_options',
        'nonce_actions' => array(
            'main' => 'redco_optimizer_nonce',
            'webp_bulk_convert' => 'redco_webp_bulk_convert',
            'webp_stats' => 'redco_webp_stats',
            'webp_test' => 'redco_webp_test',
            'settings_save' => 'redco_settings_save',
            'cache_clear' => 'redco_cache_clear',
            'api_test' => 'redco_api_test'
        ),
        'allowed_file_types' => array('jpg', 'jpeg', 'png', 'gif', 'webp'),
        'max_file_size' => 52428800, // 50MB
        'rate_limit_requests' => 100,
        'rate_limit_window' => 3600, // 1 hour
        'session_timeout' => 7200,   // 2 hours
        'csrf_protection' => true,
        'sanitize_inputs' => true
    );
    
    /**
     * Get default settings for a module
     * 
     * @param string $module_key Module identifier
     * @return array Default settings
     */
    public static function get_module_defaults($module_key) {
        return isset(self::DEFAULT_SETTINGS[$module_key]) 
            ? self::DEFAULT_SETTINGS[$module_key] 
            : array();
    }
    
    /**
     * Get performance limit for a setting
     * 
     * @param string $limit_key Limit identifier
     * @return mixed Limit value or null if not found
     */
    public static function get_performance_limit($limit_key) {
        return isset(self::PERFORMANCE_LIMITS[$limit_key]) 
            ? self::PERFORMANCE_LIMITS[$limit_key] 
            : null;
    }
    
    /**
     * Get cache directory path
     * 
     * @param string $type Cache type (cache, backup, logs, temp)
     * @return string Full directory path
     */
    public static function get_cache_dir($type = 'cache') {
        $upload_dir = wp_upload_dir();
        $base_dir = $upload_dir['basedir'];
        
        switch ($type) {
            case 'backup':
                return $base_dir . '/' . self::PATHS['backup_dir'];
            case 'logs':
                return $base_dir . '/' . self::PATHS['logs_dir'];
            case 'temp':
                return $base_dir . '/' . self::PATHS['temp_dir'];
            case 'cache':
            default:
                return $base_dir . '/' . self::PATHS['cache_dir'];
        }
    }
    
    /**
     * Get cache duration for a specific type
     * 
     * @param string $type Cache type
     * @return int Duration in seconds
     */
    public static function get_cache_duration($type) {
        return isset(self::CACHE_SETTINGS[$type . '_cache_duration']) 
            ? self::CACHE_SETTINGS[$type . '_cache_duration'] 
            : self::CACHE_SETTINGS['stats_cache_duration'];
    }
    
    /**
     * Get timeout setting
     * 
     * @param string $type Timeout type
     * @return int Timeout in seconds
     */
    public static function get_timeout($type = 'default') {
        $key = $type . '_timeout';
        return isset(self::CACHE_SETTINGS[$key]) 
            ? self::CACHE_SETTINGS[$key] 
            : self::CACHE_SETTINGS['default_timeout'];
    }
    
    /**
     * Validate setting value against limits
     * 
     * @param string $setting_key Setting identifier
     * @param mixed $value Value to validate
     * @return mixed Validated value
     */
    public static function validate_setting($setting_key, $value) {
        switch ($setting_key) {
            case 'cache_expiration':
                return max(
                    self::PERFORMANCE_LIMITS['cache_expiration_min'],
                    min(self::PERFORMANCE_LIMITS['cache_expiration_max'], (int)$value)
                );
                
            case 'batch_size':
                return max(
                    self::PERFORMANCE_LIMITS['batch_size_min'],
                    min(self::PERFORMANCE_LIMITS['batch_size_max'], (int)$value)
                );
                
            case 'quality':
                return max(
                    self::PERFORMANCE_LIMITS['quality_min'],
                    min(self::PERFORMANCE_LIMITS['quality_max'], (int)$value)
                );
                
            case 'threshold':
                return max(
                    self::PERFORMANCE_LIMITS['threshold_min'],
                    min(self::PERFORMANCE_LIMITS['threshold_max'], (int)$value)
                );
                
            case 'heartbeat_frequency':
                return max(
                    self::PERFORMANCE_LIMITS['heartbeat_min'],
                    min(self::PERFORMANCE_LIMITS['heartbeat_max'], (int)$value)
                );
                
            default:
                return $value;
        }
    }
    
    /**
     * Get environment-specific configuration
     * 
     * @return array Environment configuration
     */
    public static function get_environment_config() {
        $is_development = self::is_development_environment();
        
        return array(
            'debug_mode' => $is_development,
            'cache_duration' => $is_development ? 60 : 3600, // Shorter cache in dev
            'api_timeout' => $is_development ? 15 : 60,
            'batch_size' => $is_development ? 5 : 10,
            'logging_enabled' => $is_development
        );
    }
    
    /**
     * Check if this is a development environment
     * 
     * @return bool True if development environment
     */
    public static function is_development_environment() {
        $dev_indicators = array('localhost', '127.0.0.1', '.local', '.dev', 'staging', 'test');
        $site_url = home_url();
        
        foreach ($dev_indicators as $indicator) {
            if (strpos($site_url, $indicator) !== false) {
                return true;
            }
        }
        
        return defined('WP_DEBUG') && WP_DEBUG;
    }
    
    /**
     * Get plugin information
     *
     * @return array Plugin information
     */
    public static function get_plugin_info() {
        return array(
            'version' => REDCO_OPTIMIZER_VERSION,
            'name' => 'Redco Optimizer',
            'slug' => 'redco-optimizer',
            'text_domain' => 'redco-optimizer',
            'min_wp_version' => '5.0',
            'min_php_version' => '7.4',
            'author' => 'Redconic Digital Solution',
            'author_uri' => 'https://redconic.com',
            'plugin_uri' => 'https://redconic.com/optimizer'
        );
    }

    /**
     * Get security setting
     *
     * @param string $setting_key Security setting key
     * @return mixed Security setting value
     */
    public static function get_security_setting($setting_key) {
        return isset(self::SECURITY_SETTINGS[$setting_key])
            ? self::SECURITY_SETTINGS[$setting_key]
            : null;
    }

    /**
     * Get nonce action for a specific operation
     *
     * @param string $operation Operation type
     * @return string Nonce action
     */
    public static function get_nonce_action($operation = 'main') {
        $nonce_actions = self::SECURITY_SETTINGS['nonce_actions'];
        return isset($nonce_actions[$operation])
            ? $nonce_actions[$operation]
            : $nonce_actions['main'];
    }

    /**
     * CRITICAL SECURITY FIX: Encrypt sensitive data before storage
     *
     * @param string $data Data to encrypt
     * @return string Encrypted data
     */
    public static function encrypt_sensitive_data($data) {
        if (empty($data)) {
            return $data;
        }

        // Use WordPress salts for encryption key
        $key = wp_salt('secure_auth') . wp_salt('logged_in');
        $key = hash('sha256', $key);

        // Generate random IV
        $iv = openssl_random_pseudo_bytes(16);

        // Encrypt the data
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);

        // Combine IV and encrypted data
        return base64_encode($iv . $encrypted);
    }

    /**
     * CRITICAL SECURITY FIX: Decrypt sensitive data after retrieval
     *
     * @param string $encrypted_data Encrypted data
     * @return string Decrypted data
     */
    public static function decrypt_sensitive_data($encrypted_data) {
        if (empty($encrypted_data)) {
            return $encrypted_data;
        }

        // Use WordPress salts for encryption key
        $key = wp_salt('secure_auth') . wp_salt('logged_in');
        $key = hash('sha256', $key);

        // Decode the data
        $data = base64_decode($encrypted_data);

        if ($data === false) {
            return $encrypted_data; // Return original if not base64
        }

        // Extract IV and encrypted data
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);

        // Decrypt the data
        $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);

        return $decrypted !== false ? $decrypted : $encrypted_data;
    }

    /**
     * CRITICAL SECURITY FIX: Check if data appears to be encrypted
     *
     * @param string $data Data to check
     * @return bool True if data appears encrypted
     */
    public static function is_encrypted_data($data) {
        if (empty($data) || strlen($data) < 24) {
            return false;
        }

        // Check if it's valid base64 and has minimum length for encrypted data
        $decoded = base64_decode($data, true);
        return $decoded !== false && strlen($decoded) >= 16;
    }

    /**
     * Check if user has required capability
     *
     * @param string $capability Optional custom capability
     * @return bool True if user has capability
     */
    public static function user_can_manage($capability = null) {
        $required_cap = $capability ?: self::SECURITY_SETTINGS['required_capability'];
        return current_user_can($required_cap);
    }

    /**
     * Verify nonce for operation
     *
     * @param string $nonce Nonce value
     * @param string $operation Operation type
     * @return bool True if nonce is valid
     */
    public static function verify_nonce($nonce, $operation = 'main') {
        $action = self::get_nonce_action($operation);
        return wp_verify_nonce($nonce, $action);
    }

    /**
     * Create nonce for operation
     *
     * @param string $operation Operation type
     * @return string Nonce value
     */
    public static function create_nonce($operation = 'main') {
        $action = self::get_nonce_action($operation);
        return wp_create_nonce($action);
    }

    /**
     * Validate file type
     *
     * @param string $file_path File path or extension
     * @return bool True if file type is allowed
     */
    public static function is_allowed_file_type($file_path) {
        $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
        return in_array($extension, self::SECURITY_SETTINGS['allowed_file_types']);
    }

    /**
     * Check file size limit
     *
     * @param int $file_size File size in bytes
     * @return bool True if file size is within limit
     */
    public static function is_file_size_allowed($file_size) {
        return $file_size <= self::SECURITY_SETTINGS['max_file_size'];
    }
}
