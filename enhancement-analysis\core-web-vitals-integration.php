<?php
/**
 * Enhanced Core Web Vitals Integration for Redco Optimizer
 * 
 * Provides real-time Core Web Vitals monitoring and optimization recommendations
 */

class Redco_Core_Web_Vitals_Analyzer {
    
    private $api_endpoints = array(
        'pagespeed' => 'https://www.googleapis.com/pagespeed/v5/runPagespeed',
        'crux' => 'https://chromeuxreport.googleapis.com/v1/records:queryRecord'
    );
    
    /**
     * Comprehensive Core Web Vitals Analysis
     */
    public function analyze_core_web_vitals($url, $api_key) {
        $results = array(
            'lcp' => $this->analyze_largest_contentful_paint($url, $api_key),
            'fid' => $this->analyze_first_input_delay($url, $api_key),
            'cls' => $this->analyze_cumulative_layout_shift($url, $api_key),
            'fcp' => $this->analyze_first_contentful_paint($url, $api_key),
            'ttfb' => $this->analyze_time_to_first_byte($url),
            'recommendations' => array()
        );
        
        // Generate specific optimization recommendations
        $results['recommendations'] = $this->generate_cwv_recommendations($results);
        
        return $results;
    }
    
    /**
     * Largest Contentful Paint (LCP) Analysis
     */
    private function analyze_largest_contentful_paint($url, $api_key) {
        $issues = array();
        
        // Get PageSpeed Insights data
        $psi_data = $this->get_pagespeed_data($url, $api_key);
        
        if ($psi_data && isset($psi_data['lighthouseResult']['audits']['largest-contentful-paint'])) {
            $lcp_audit = $psi_data['lighthouseResult']['audits']['largest-contentful-paint'];
            $lcp_value = $lcp_audit['numericValue'] / 1000; // Convert to seconds
            
            if ($lcp_value > 2.5) {
                $issues[] = array(
                    'id' => 'poor_lcp',
                    'title' => 'Poor Largest Contentful Paint',
                    'description' => sprintf('LCP is %.2fs. Target: <2.5s', $lcp_value),
                    'severity' => $lcp_value > 4 ? 'critical' : 'high',
                    'category' => 'core_web_vitals',
                    'auto_fixable' => true,
                    'fix_action' => 'optimize_lcp',
                    'specific_recommendations' => $this->get_lcp_recommendations($psi_data)
                );
            }
        }
        
        return array(
            'value' => $lcp_value ?? null,
            'status' => $this->get_cwv_status($lcp_value, 2.5, 4.0),
            'issues' => $issues
        );
    }
    
    /**
     * First Input Delay (FID) Analysis
     */
    private function analyze_first_input_delay($url, $api_key) {
        $issues = array();
        
        // Get Chrome UX Report data for real user metrics
        $crux_data = $this->get_crux_data($url, $api_key);
        
        if ($crux_data && isset($crux_data['record']['metrics']['first_input_delay'])) {
            $fid_data = $crux_data['record']['metrics']['first_input_delay'];
            $fid_p75 = $fid_data['percentiles']['p75'] ?? null;
            
            if ($fid_p75 && $fid_p75 > 100) { // 100ms threshold
                $issues[] = array(
                    'id' => 'poor_fid',
                    'title' => 'Poor First Input Delay',
                    'description' => sprintf('FID P75 is %dms. Target: <100ms', $fid_p75),
                    'severity' => $fid_p75 > 300 ? 'critical' : 'high',
                    'category' => 'core_web_vitals',
                    'auto_fixable' => true,
                    'fix_action' => 'optimize_fid',
                    'specific_recommendations' => array(
                        'Reduce JavaScript execution time',
                        'Split long tasks into smaller chunks',
                        'Use web workers for heavy computations',
                        'Optimize third-party scripts'
                    )
                );
            }
        }
        
        return array(
            'value' => $fid_p75 ?? null,
            'status' => $this->get_cwv_status($fid_p75, 100, 300),
            'issues' => $issues
        );
    }
    
    /**
     * Cumulative Layout Shift (CLS) Analysis
     */
    private function analyze_cumulative_layout_shift($url, $api_key) {
        $issues = array();
        
        $psi_data = $this->get_pagespeed_data($url, $api_key);
        
        if ($psi_data && isset($psi_data['lighthouseResult']['audits']['cumulative-layout-shift'])) {
            $cls_audit = $psi_data['lighthouseResult']['audits']['cumulative-layout-shift'];
            $cls_value = $cls_audit['numericValue'];
            
            if ($cls_value > 0.1) {
                $issues[] = array(
                    'id' => 'poor_cls',
                    'title' => 'Poor Cumulative Layout Shift',
                    'description' => sprintf('CLS is %.3f. Target: <0.1', $cls_value),
                    'severity' => $cls_value > 0.25 ? 'critical' : 'high',
                    'category' => 'core_web_vitals',
                    'auto_fixable' => true,
                    'fix_action' => 'optimize_cls',
                    'specific_recommendations' => $this->get_cls_recommendations($psi_data)
                );
            }
        }
        
        return array(
            'value' => $cls_value ?? null,
            'status' => $this->get_cwv_status($cls_value, 0.1, 0.25),
            'issues' => $issues
        );
    }
    
    /**
     * Generate Core Web Vitals optimization recommendations
     */
    private function generate_cwv_recommendations($cwv_results) {
        $recommendations = array();
        
        // LCP Optimizations
        if ($cwv_results['lcp']['status'] !== 'good') {
            $recommendations['lcp'] = array(
                'priority' => 'high',
                'title' => 'Optimize Largest Contentful Paint',
                'actions' => array(
                    'Optimize images (WebP conversion, lazy loading)',
                    'Implement critical CSS inlining',
                    'Preload key resources',
                    'Optimize server response times',
                    'Remove render-blocking resources'
                )
            );
        }
        
        // FID Optimizations
        if ($cwv_results['fid']['status'] !== 'good') {
            $recommendations['fid'] = array(
                'priority' => 'high',
                'title' => 'Improve First Input Delay',
                'actions' => array(
                    'Minimize JavaScript execution time',
                    'Code splitting and lazy loading',
                    'Remove unused JavaScript',
                    'Optimize third-party scripts',
                    'Use web workers for heavy tasks'
                )
            );
        }
        
        // CLS Optimizations
        if ($cwv_results['cls']['status'] !== 'good') {
            $recommendations['cls'] = array(
                'priority' => 'high',
                'title' => 'Reduce Cumulative Layout Shift',
                'actions' => array(
                    'Set explicit dimensions for images and videos',
                    'Reserve space for ads and embeds',
                    'Avoid inserting content above existing content',
                    'Use CSS aspect-ratio for responsive images',
                    'Preload fonts to prevent FOIT/FOUT'
                )
            );
        }
        
        return $recommendations;
    }
    
    /**
     * Get PageSpeed Insights data
     */
    private function get_pagespeed_data($url, $api_key) {
        $endpoint = add_query_arg(array(
            'url' => $url,
            'key' => $api_key,
            'strategy' => 'mobile',
            'category' => 'performance'
        ), $this->api_endpoints['pagespeed']);
        
        $response = wp_remote_get($endpoint, array('timeout' => 30));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        return json_decode(wp_remote_retrieve_body($response), true);
    }
    
    /**
     * Get Chrome UX Report data
     */
    private function get_crux_data($url, $api_key) {
        $body = json_encode(array(
            'url' => $url,
            'formFactor' => 'PHONE',
            'metrics' => array('first_input_delay', 'cumulative_layout_shift', 'largest_contentful_paint')
        ));
        
        $response = wp_remote_post($this->api_endpoints['crux'] . '?key=' . $api_key, array(
            'headers' => array('Content-Type' => 'application/json'),
            'body' => $body,
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        return json_decode(wp_remote_retrieve_body($response), true);
    }
    
    /**
     * Determine Core Web Vitals status
     */
    private function get_cwv_status($value, $good_threshold, $poor_threshold) {
        if ($value === null) return 'unknown';
        if ($value <= $good_threshold) return 'good';
        if ($value <= $poor_threshold) return 'needs_improvement';
        return 'poor';
    }
}
