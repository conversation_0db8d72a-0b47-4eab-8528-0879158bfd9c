/**
 * CRITICAL VALIDATION: Test Suite for Redco Optimizer Utilities
 * Validates all critical fixes and new utility systems
 * Only runs in development environments
 */

(function($) {
    'use strict';

    /**
     * Test Suite for Redco Utilities
     */
    window.RedcoTestSuite = {
        
        /**
         * Test results storage
         */
        results: {
            passed: 0,
            failed: 0,
            tests: []
        },
        
        /**
         * Run all tests
         */
        runAllTests: function() {
            if (!this.isDevelopmentEnvironment()) {
                console.warn('Test suite only runs in development environment');
                return;
            }
            
            console.group('🧪 Redco Optimizer Test Suite');
            
            this.resetResults();
            
            // Test debug utilities
            this.testDebugUtilities();
            
            // Test AJAX utilities
            this.testAjaxUtilities();
            
            // Test validation utilities
            this.testValidationUtilities();
            
            // Test progress utilities
            this.testProgressUtilities();
            
            // Test extracted admin classes
            this.testAdminClasses();
            
            this.displayResults();
            console.groupEnd();
        },
        
        /**
         * Test debug utilities
         */
        testDebugUtilities: function() {
            console.group('🔍 Testing Debug Utilities');
            
            // Test debug utility existence
            this.assert(
                typeof redcoDebug !== 'undefined',
                'RedcoDebug utility should be available'
            );
            
            // Test debug methods
            if (typeof redcoDebug !== 'undefined') {
                this.assert(
                    typeof redcoDebug.log === 'function',
                    'RedcoDebug.log should be a function'
                );
                
                this.assert(
                    typeof redcoDebug.enabled === 'boolean',
                    'RedcoDebug.enabled should be a boolean'
                );
                
                // Test conditional logging
                const originalConsoleLog = console.log;
                let logCalled = false;
                console.log = function() { logCalled = true; };
                
                redcoDebug.log('Test message');
                
                this.assert(
                    logCalled === redcoDebug.enabled,
                    'Debug logging should only work when enabled'
                );
                
                console.log = originalConsoleLog;
            }
            
            console.groupEnd();
        },
        
        /**
         * Test AJAX utilities
         */
        testAjaxUtilities: function() {
            console.group('📡 Testing AJAX Utilities');
            
            // Test AJAX utility existence
            this.assert(
                typeof RedcoAjax !== 'undefined',
                'RedcoAjax utility should be available'
            );
            
            if (typeof RedcoAjax !== 'undefined') {
                this.assert(
                    typeof RedcoAjax.request === 'function',
                    'RedcoAjax.request should be a function'
                );
                
                this.assert(
                    typeof RedcoAjax.progressRequest === 'function',
                    'RedcoAjax.progressRequest should be a function'
                );
                
                this.assert(
                    typeof RedcoAjax.batchRequest === 'function',
                    'RedcoAjax.batchRequest should be a function'
                );
                
                // Test request ID generation
                const id1 = RedcoAjax.generateRequestId('test_action', {param: 'value'});
                const id2 = RedcoAjax.generateRequestId('test_action', {param: 'value'});
                
                this.assert(
                    id1 === id2,
                    'Same parameters should generate same request ID'
                );
                
                // Test cache functionality
                this.assert(
                    typeof RedcoAjax.cache === 'object',
                    'RedcoAjax should have cache object'
                );
            }
            
            console.groupEnd();
        },
        
        /**
         * Test validation utilities
         */
        testValidationUtilities: function() {
            console.group('✅ Testing Validation Utilities');
            
            // Test validation utility existence
            this.assert(
                typeof RedcoValidation !== 'undefined',
                'RedcoValidation utility should be available'
            );
            
            if (typeof RedcoValidation !== 'undefined') {
                // Test email validation
                this.assert(
                    RedcoValidation.isValidEmail('<EMAIL>') === true,
                    'Valid email should pass validation'
                );
                
                this.assert(
                    RedcoValidation.isValidEmail('invalid-email') === false,
                    'Invalid email should fail validation'
                );
                
                // Test URL validation
                this.assert(
                    RedcoValidation.isValidUrl('https://example.com') === true,
                    'Valid URL should pass validation'
                );
                
                this.assert(
                    RedcoValidation.isValidUrl('not-a-url') === false,
                    'Invalid URL should fail validation'
                );
                
                // Test number validation
                this.assert(
                    RedcoValidation.isValidNumber(50, 0, 100) === true,
                    'Number within range should be valid'
                );
                
                this.assert(
                    RedcoValidation.isValidNumber(150, 0, 100) === false,
                    'Number outside range should be invalid'
                );
            }
            
            console.groupEnd();
        },
        
        /**
         * Test progress utilities
         */
        testProgressUtilities: function() {
            console.group('📊 Testing Progress Utilities');
            
            // Test progress utility existence
            this.assert(
                typeof RedcoProgress !== 'undefined',
                'RedcoProgress utility should be available'
            );
            
            if (typeof RedcoProgress !== 'undefined') {
                this.assert(
                    typeof RedcoProgress.showModal === 'function',
                    'RedcoProgress.showModal should be a function'
                );
                
                this.assert(
                    typeof RedcoProgress.updateProgress === 'function',
                    'RedcoProgress.updateProgress should be a function'
                );
                
                this.assert(
                    typeof RedcoProgress.simulateProgress === 'function',
                    'RedcoProgress.simulateProgress should be a function'
                );
                
                // Test progress instance tracking
                this.assert(
                    RedcoProgress.activeProgress instanceof Map,
                    'RedcoProgress should track active instances'
                );
            }
            
            console.groupEnd();
        },
        
        /**
         * Test extracted admin classes
         */
        testAdminClasses: function() {
            console.group('🏗️ Testing Admin Classes');
            
            // Test if admin classes are properly loaded
            // This would need to be tested on the PHP side, but we can check for their effects
            
            // Check if settings are being handled properly
            if (typeof redcoAjax !== 'undefined') {
                this.assert(
                    typeof redcoAjax.nonce === 'string',
                    'Admin nonce should be available'
                );
                
                this.assert(
                    typeof redcoAjax.ajaxurl === 'string',
                    'AJAX URL should be available'
                );
            }
            
            console.groupEnd();
        },
        
        /**
         * Assert function for tests
         */
        assert: function(condition, message) {
            const result = {
                condition: condition,
                message: message,
                passed: !!condition
            };
            
            this.results.tests.push(result);
            
            if (result.passed) {
                this.results.passed++;
                console.log('✅', message);
            } else {
                this.results.failed++;
                console.error('❌', message);
            }
        },
        
        /**
         * Reset test results
         */
        resetResults: function() {
            this.results = {
                passed: 0,
                failed: 0,
                tests: []
            };
        },
        
        /**
         * Display test results
         */
        displayResults: function() {
            const total = this.results.passed + this.results.failed;
            const passRate = total > 0 ? (this.results.passed / total * 100).toFixed(1) : 0;
            
            console.group('📊 Test Results Summary');
            console.log(`Total Tests: ${total}`);
            console.log(`Passed: ${this.results.passed}`);
            console.log(`Failed: ${this.results.failed}`);
            console.log(`Pass Rate: ${passRate}%`);
            
            if (this.results.failed > 0) {
                console.warn('Some tests failed. Check the detailed results above.');
            } else {
                console.log('🎉 All tests passed!');
            }
            console.groupEnd();
        },
        
        /**
         * Check if we're in development environment
         */
        isDevelopmentEnvironment: function() {
            return (
                window.location.hostname === 'localhost' ||
                window.location.hostname === '127.0.0.1' ||
                window.location.hostname.includes('.local') ||
                window.location.hostname.includes('.dev') ||
                (typeof redcoDebug !== 'undefined' && redcoDebug.enabled)
            );
        }
    };

    // Auto-run tests in development environment
    if (RedcoTestSuite.isDevelopmentEnvironment()) {
        // Run tests after DOM is ready
        $(document).ready(function() {
            setTimeout(function() {
                RedcoTestSuite.runAllTests();
            }, 1000); // Wait 1 second for all scripts to load
        });
    }

})(jQuery);
