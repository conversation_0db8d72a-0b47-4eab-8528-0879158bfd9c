<?php
/**
 * Mobile Optimization Analyzer for Redco Optimizer
 * 
 * Comprehensive mobile performance and usability diagnostics
 */

class Redco_Mobile_Optimization_Analyzer {
    
    /**
     * Comprehensive mobile optimization analysis
     */
    public function analyze_mobile_optimization($url) {
        return array(
            'viewport' => $this->analyze_viewport_configuration($url),
            'touch_targets' => $this->analyze_touch_targets($url),
            'mobile_performance' => $this->analyze_mobile_performance($url),
            'responsive_design' => $this->analyze_responsive_design($url),
            'mobile_usability' => $this->analyze_mobile_usability($url),
            'amp_compatibility' => $this->analyze_amp_compatibility($url),
            'pwa_features' => $this->analyze_pwa_features($url)
        );
    }
    
    /**
     * Viewport Configuration Analysis
     */
    private function analyze_viewport_configuration($url) {
        $issues = array();
        
        // Get page content
        $page_content = $this->get_page_content($url);
        
        // Check for viewport meta tag
        if (!preg_match('/<meta[^>]*name=["\']viewport["\'][^>]*>/i', $page_content)) {
            $issues[] = array(
                'id' => 'missing_viewport_meta',
                'title' => 'Missing Viewport Meta Tag',
                'description' => 'No viewport meta tag found. Essential for mobile responsiveness.',
                'severity' => 'high',
                'category' => 'mobile',
                'auto_fixable' => true,
                'fix_action' => 'add_viewport_meta',
                'recommendation' => 'Add <meta name="viewport" content="width=device-width, initial-scale=1">'
            );
        } else {
            // Check viewport configuration
            preg_match('/<meta[^>]*name=["\']viewport["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $page_content, $matches);
            $viewport_content = $matches[1] ?? '';
            
            if (strpos($viewport_content, 'width=device-width') === false) {
                $issues[] = array(
                    'id' => 'incorrect_viewport_width',
                    'title' => 'Incorrect Viewport Width',
                    'description' => 'Viewport should include width=device-width for proper mobile scaling.',
                    'severity' => 'medium',
                    'category' => 'mobile',
                    'auto_fixable' => true,
                    'fix_action' => 'fix_viewport_width'
                );
            }
            
            if (strpos($viewport_content, 'user-scalable=no') !== false) {
                $issues[] = array(
                    'id' => 'viewport_scaling_disabled',
                    'title' => 'Viewport Scaling Disabled',
                    'description' => 'user-scalable=no prevents accessibility zooming.',
                    'severity' => 'medium',
                    'category' => 'mobile',
                    'auto_fixable' => true,
                    'fix_action' => 'enable_viewport_scaling'
                );
            }
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * Touch Targets Analysis
     */
    private function analyze_touch_targets($url) {
        $issues = array();
        
        // This would require JavaScript execution to analyze actual touch targets
        // For now, we'll check common patterns in CSS and HTML
        
        $page_content = $this->get_page_content($url);
        
        // Check for small button/link patterns
        if (preg_match_all('/<a[^>]*class=["\'][^"\']*btn[^"\']*["\'][^>]*>/i', $page_content, $matches)) {
            // Analyze button sizes (would need CSS parsing in real implementation)
            $issues[] = array(
                'id' => 'small_touch_targets',
                'title' => 'Potentially Small Touch Targets',
                'description' => 'Some buttons may be smaller than the recommended 44px minimum.',
                'severity' => 'medium',
                'category' => 'mobile',
                'auto_fixable' => true,
                'fix_action' => 'optimize_touch_targets',
                'recommendation' => 'Ensure all touch targets are at least 44px × 44px'
            );
        }
        
        // Check for close proximity of clickable elements
        if (preg_match('/<nav[^>]*>.*?<\/nav>/is', $page_content, $nav_matches)) {
            $nav_content = $nav_matches[0];
            $link_count = substr_count($nav_content, '<a ');
            
            if ($link_count > 5) {
                $issues[] = array(
                    'id' => 'crowded_navigation',
                    'title' => 'Crowded Navigation Menu',
                    'description' => 'Navigation contains many links that may be too close together on mobile.',
                    'severity' => 'medium',
                    'category' => 'mobile',
                    'auto_fixable' => true,
                    'fix_action' => 'optimize_mobile_navigation'
                );
            }
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * Mobile Performance Analysis
     */
    private function analyze_mobile_performance($url) {
        $issues = array();
        
        // Analyze mobile-specific performance issues
        $mobile_metrics = $this->get_mobile_performance_metrics($url);
        
        // Large images on mobile
        if ($mobile_metrics['large_images_count'] > 0) {
            $issues[] = array(
                'id' => 'large_images_mobile',
                'title' => 'Large Images on Mobile',
                'description' => sprintf('%d images are larger than necessary for mobile devices.', $mobile_metrics['large_images_count']),
                'severity' => 'high',
                'category' => 'mobile',
                'auto_fixable' => true,
                'fix_action' => 'optimize_mobile_images',
                'recommendation' => 'Implement responsive images with srcset and sizes attributes'
            );
        }
        
        // Heavy JavaScript on mobile
        if ($mobile_metrics['js_size'] > 500000) { // 500KB
            $issues[] = array(
                'id' => 'heavy_js_mobile',
                'title' => 'Heavy JavaScript Load on Mobile',
                'description' => sprintf('JavaScript bundle is %s, which is heavy for mobile connections.', redco_format_bytes($mobile_metrics['js_size'])),
                'severity' => 'high',
                'category' => 'mobile',
                'auto_fixable' => true,
                'fix_action' => 'optimize_mobile_js'
            );
        }
        
        // No service worker for offline functionality
        if (!$mobile_metrics['has_service_worker']) {
            $issues[] = array(
                'id' => 'no_service_worker',
                'title' => 'No Service Worker for Offline Support',
                'description' => 'Service worker can improve mobile experience with caching and offline functionality.',
                'severity' => 'medium',
                'category' => 'mobile',
                'auto_fixable' => true,
                'fix_action' => 'implement_service_worker'
            );
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * Responsive Design Analysis
     */
    private function analyze_responsive_design($url) {
        $issues = array();
        
        $page_content = $this->get_page_content($url);
        
        // Check for responsive CSS patterns
        if (!preg_match('/@media[^{]*\([^)]*max-width[^)]*\)/i', $page_content)) {
            $issues[] = array(
                'id' => 'no_responsive_css',
                'title' => 'No Responsive CSS Detected',
                'description' => 'No media queries found for responsive design.',
                'severity' => 'high',
                'category' => 'mobile',
                'auto_fixable' => false,
                'fix_action' => 'implement_responsive_design',
                'recommendation' => 'Implement responsive CSS with media queries'
            );
        }
        
        // Check for fixed-width elements
        if (preg_match('/width:\s*\d+px(?![^}]*@media)/i', $page_content)) {
            $issues[] = array(
                'id' => 'fixed_width_elements',
                'title' => 'Fixed-Width Elements Detected',
                'description' => 'Some elements use fixed pixel widths that may not work well on mobile.',
                'severity' => 'medium',
                'category' => 'mobile',
                'auto_fixable' => true,
                'fix_action' => 'convert_to_responsive_units'
            );
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * Mobile Usability Analysis
     */
    private function analyze_mobile_usability($url) {
        $issues = array();
        
        $page_content = $this->get_page_content($url);
        
        // Check for Flash content
        if (preg_match('/<object[^>]*type=["\']application\/x-shockwave-flash["\'][^>]*>/i', $page_content) ||
            preg_match('/<embed[^>]*type=["\']application\/x-shockwave-flash["\'][^>]*>/i', $page_content)) {
            $issues[] = array(
                'id' => 'flash_content',
                'title' => 'Flash Content Detected',
                'description' => 'Flash content is not supported on mobile devices.',
                'severity' => 'critical',
                'category' => 'mobile',
                'auto_fixable' => false,
                'fix_action' => 'replace_flash_content'
            );
        }
        
        // Check for horizontal scrolling issues
        if (preg_match('/overflow-x:\s*scroll/i', $page_content)) {
            $issues[] = array(
                'id' => 'horizontal_scroll',
                'title' => 'Horizontal Scrolling Required',
                'description' => 'Content requires horizontal scrolling on mobile devices.',
                'severity' => 'medium',
                'category' => 'mobile',
                'auto_fixable' => true,
                'fix_action' => 'fix_horizontal_scroll'
            );
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * AMP Compatibility Analysis
     */
    private function analyze_amp_compatibility($url) {
        $issues = array();
        
        // Check if AMP is already implemented
        $amp_url = $url . (strpos($url, '?') !== false ? '&' : '?') . 'amp=1';
        $amp_response = wp_remote_head($amp_url);
        
        if (is_wp_error($amp_response) || wp_remote_retrieve_response_code($amp_response) !== 200) {
            $issues[] = array(
                'id' => 'no_amp_support',
                'title' => 'No AMP Support',
                'description' => 'AMP (Accelerated Mobile Pages) can significantly improve mobile loading speed.',
                'severity' => 'low',
                'category' => 'mobile',
                'auto_fixable' => true,
                'fix_action' => 'implement_amp_support',
                'recommendation' => 'Consider implementing AMP for critical pages'
            );
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * PWA Features Analysis
     */
    private function analyze_pwa_features($url) {
        $issues = array();
        
        $page_content = $this->get_page_content($url);
        
        // Check for web app manifest
        if (!preg_match('/<link[^>]*rel=["\']manifest["\'][^>]*>/i', $page_content)) {
            $issues[] = array(
                'id' => 'no_web_manifest',
                'title' => 'No Web App Manifest',
                'description' => 'Web app manifest enables "Add to Home Screen" functionality.',
                'severity' => 'medium',
                'category' => 'mobile',
                'auto_fixable' => true,
                'fix_action' => 'create_web_manifest'
            );
        }
        
        // Check for service worker registration
        if (!preg_match('/navigator\.serviceWorker\.register/i', $page_content)) {
            $issues[] = array(
                'id' => 'no_sw_registration',
                'title' => 'No Service Worker Registration',
                'description' => 'Service worker enables offline functionality and improved caching.',
                'severity' => 'medium',
                'category' => 'mobile',
                'auto_fixable' => true,
                'fix_action' => 'register_service_worker'
            );
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * Get page content for analysis
     */
    private function get_page_content($url) {
        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'user-agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        ));
        
        if (is_wp_error($response)) {
            return '';
        }
        
        return wp_remote_retrieve_body($response);
    }
    
    /**
     * Get mobile performance metrics
     */
    private function get_mobile_performance_metrics($url) {
        // This would integrate with actual performance measurement tools
        // For now, return mock data structure
        return array(
            'large_images_count' => 3,
            'js_size' => 750000,
            'css_size' => 150000,
            'has_service_worker' => false,
            'mobile_score' => 65
        );
    }
}
