<?php
/**
 * WebP Global Handlers
 *
 * Handles global AJAX requests for WebP conversion module
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Global AJAX handlers for WebP module
add_action('wp_ajax_redco_webp_enhanced_bulk_convert', 'redco_webp_ajax_bulk_convert');
add_action('wp_ajax_redco_webp_bulk_convert', 'redco_webp_ajax_bulk_convert'); // CRITICAL FIX: Add missing action name that JavaScript calls
add_action('wp_ajax_redco_webp_enhanced_test', 'redco_webp_ajax_test');
add_action('wp_ajax_redco_webp_enhanced_stats', 'redco_webp_ajax_stats');
add_action('wp_ajax_redco_webp_get_processable_images', 'redco_webp_ajax_get_processable_images');

/**
 * AJAX handler for bulk conversion
 * SECURITY FIX: Added comprehensive nonce verification and capability checks
 */
function redco_webp_ajax_bulk_convert() {
    // CRITICAL SECURITY FIX: Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'] ?? '', 'redco_webp_bulk_convert')) {
        wp_send_json_error(array(
            'message' => 'Security verification failed. Please refresh the page and try again.',
            'error_code' => 'NONCE_FAILED'
        ));
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error(array(
            'message' => 'Insufficient permissions to perform this action.',
            'error_code' => 'INSUFFICIENT_PERMISSIONS'
        ));
        return;
    }

    // SECURITY FIX: Rate limiting for resource-intensive operations
    $rate_limit_key = 'redco_webp_bulk_convert_' . get_current_user_id();
    if (get_transient($rate_limit_key)) {
        wp_send_json_error(array(
            'message' => 'Please wait before starting another bulk conversion.',
            'error_code' => 'RATE_LIMITED'
        ));
        return;
    }
    set_transient($rate_limit_key, true, 300); // 5 minutes

    global $redco_webp_instance;
    if ($redco_webp_instance && method_exists($redco_webp_instance, 'ajax_enhanced_bulk_convert')) {
        $redco_webp_instance->ajax_enhanced_bulk_convert();
    } else {
        wp_send_json_error(array('message' => 'WebP module not available'));
    }
}

/**
 * AJAX handler for test conversion
 * SECURITY FIX: Added comprehensive nonce verification and capability checks
 */
function redco_webp_ajax_test() {
    // CRITICAL SECURITY FIX: Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'] ?? '', 'redco_webp_test')) {
        wp_send_json_error(array(
            'message' => 'Security verification failed. Please refresh the page and try again.',
            'error_code' => 'NONCE_FAILED'
        ));
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error(array(
            'message' => 'Insufficient permissions to perform this action.',
            'error_code' => 'INSUFFICIENT_PERMISSIONS'
        ));
        return;
    }

    global $redco_webp_instance;
    if ($redco_webp_instance && method_exists($redco_webp_instance, 'ajax_enhanced_test')) {
        $redco_webp_instance->ajax_enhanced_test();
    } else {
        wp_send_json_error(array('message' => 'WebP module not available'));
    }
}

/**
 * AJAX handler for stats
 * SECURITY FIX: Added comprehensive nonce verification and capability checks
 */
function redco_webp_ajax_stats() {
    // CRITICAL SECURITY FIX: Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'] ?? '', 'redco_webp_stats')) {
        wp_send_json_error(array(
            'message' => 'Security verification failed. Please refresh the page and try again.',
            'error_code' => 'NONCE_FAILED'
        ));
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error(array(
            'message' => 'Insufficient permissions to perform this action.',
            'error_code' => 'INSUFFICIENT_PERMISSIONS'
        ));
        return;
    }

    global $redco_webp_instance;
    if ($redco_webp_instance && method_exists($redco_webp_instance, 'ajax_enhanced_stats')) {
        $redco_webp_instance->ajax_enhanced_stats();
    } else {
        wp_send_json_error(array('message' => 'WebP module not available'));
    }
}

/**
 * AJAX handler for getting processable images
 * SECURITY FIX: Added comprehensive nonce verification and capability checks
 */
function redco_webp_ajax_get_processable_images() {
    // CRITICAL SECURITY FIX: Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'] ?? '', 'redco_webp_stats')) {
        wp_send_json_error(array(
            'message' => 'Security verification failed. Please refresh the page and try again.',
            'error_code' => 'NONCE_FAILED'
        ));
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error(array(
            'message' => 'Insufficient permissions to perform this action.',
            'error_code' => 'INSUFFICIENT_PERMISSIONS'
        ));
        return;
    }

    global $redco_webp_instance;
    if ($redco_webp_instance && method_exists($redco_webp_instance, 'ajax_get_processable_images')) {
        $redco_webp_instance->ajax_get_processable_images();
    } else {
        wp_send_json_error(array('message' => 'WebP module not available'));
    }
}
