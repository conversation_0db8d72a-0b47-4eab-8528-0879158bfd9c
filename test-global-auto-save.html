<!DOCTYPE html>
<html>
<head>
    <title>Global Auto-Save Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .test-result { margin: 10px 0; padding: 10px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .debug-log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Redco Optimizer Global Auto-Save Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <p>This test page helps diagnose Global Settings auto-save issues. Follow these steps:</p>
        <ol>
            <li>Open your browser's Developer Tools (F12)</li>
            <li>Go to the Console tab</li>
            <li>Navigate to your Global Settings page</li>
            <li>Try changing a setting (checkbox, dropdown, text field)</li>
            <li>Check the console for debug messages</li>
            <li>Check the Network tab for AJAX requests</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Expected Console Messages</h2>
        <div class="debug-log">
🔧 Global Auto-Save: Checking page for initialization
🔧 Page indicators found: {redco-optimizer-admin: X, redco-module-tab: X, redco-settings-form: X, current_url: "..."}
🔧 Global Auto-Save: Initializing on Redco page
🔧 Global Auto-Save: Starting form detection
🔧 Global Auto-Save: Found forms {count: X, selectors_checked: "..."}
🔧 Global Auto-Save: Processing form {form_classes: "redco-settings-form", data_module: null, extracted_module: "global-settings-general"}
🔧 Global Auto-Save: Setting up auto-save for form with module: global-settings-general
        </div>
    </div>

    <div class="test-section">
        <h2>Expected AJAX Request</h2>
        <div class="debug-log">
URL: /wp-admin/admin-ajax.php
Method: POST
Data:
  action: redco_global_auto_save
  nonce: [nonce_value]
  module: global-settings-general (or performance, security, etc.)
  field_name: redco_optimizer_options[enabled] (or similar)
  field_value: 1 (or the actual value)
        </div>
    </div>

    <div class="test-section">
        <h2>Common Issues & Solutions</h2>
        
        <h3>Issue 1: "Not a Redco page, skipping initialization"</h3>
        <div class="error">
            <strong>Problem:</strong> Auto-save system not initializing<br>
            <strong>Solution:</strong> Check if Global Settings page has class "redco-optimizer-admin" or forms have class "redco-settings-form"
        </div>

        <h3>Issue 2: "No forms found with standard selectors"</h3>
        <div class="error">
            <strong>Problem:</strong> Forms not detected<br>
            <strong>Solution:</strong> Verify forms have class "redco-settings-form"
        </div>

        <h3>Issue 3: "Could not extract module from form"</h3>
        <div class="error">
            <strong>Problem:</strong> Module detection failing<br>
            <strong>Solution:</strong> Check URL contains "redco-optimizer-settings" and has proper settings_tab parameter
        </div>

        <h3>Issue 4: "Security verification failed"</h3>
        <div class="error">
            <strong>Problem:</strong> Nonce validation failing<br>
            <strong>Solution:</strong> Check if redcoAjax.global_auto_save_nonce is available in JavaScript
        </div>

        <h3>Issue 5: "No field name and not a special control"</h3>
        <div class="error">
            <strong>Problem:</strong> Field name extraction failing<br>
            <strong>Solution:</strong> Verify form fields have proper name attributes like "redco_optimizer_options[field_name]"
        </div>
    </div>

    <div class="test-section">
        <h2>Manual Test Commands</h2>
        <p>Run these commands in the browser console on the Global Settings page:</p>
        
        <div class="debug-log">
// Check if auto-save system is initialized
console.log('Auto-save initialized:', window.RedcoGlobalAutoSave?.state?.isInitialized);

// Check if redcoAjax is available
console.log('redcoAjax available:', typeof redcoAjax !== 'undefined');
console.log('Global auto-save nonce:', redcoAjax?.global_auto_save_nonce);

// Check for forms
console.log('Settings forms found:', $('.redco-settings-form').length);
console.log('All forms:', $('form').length);

// Check page indicators
console.log('Page indicators:', {
    'redco-optimizer-admin': $('.redco-optimizer-admin').length,
    'redco-module-tab': $('.redco-module-tab').length,
    'redco-settings-form': $('.redco-settings-form').length
});

// Test module extraction
if (window.RedcoGlobalAutoSave) {
    $('.redco-settings-form').each(function() {
        const module = window.RedcoGlobalAutoSave.extractModuleFromForm($(this));
        console.log('Form module:', module, this);
    });
}

// Test field detection
$('.redco-settings-form input, .redco-settings-form select, .redco-settings-form textarea').each(function() {
    console.log('Field:', {
        name: $(this).attr('name'),
        type: $(this).attr('type'),
        value: $(this).val(),
        hasAutoSave: $(this).hasClass('redco-auto-save-enabled')
    });
});
        </div>
    </div>

    <div class="test-section">
        <h2>Server-Side Debug</h2>
        <p>Check your WordPress debug.log file for these messages:</p>
        
        <div class="debug-log">
🔧 Auto-save request received: {"module":"global-settings-general","field_name":"redco_optimizer_options[enabled]","field_value":"1","nonce":"PROVIDED","user_can_manage":true}
🔧 Sanitized value: 1
🔧 Current settings count: X
🔧 Field key extracted: enabled from redco_optimizer_options[enabled]
🔧 Values comparison: {"module":"global-settings-general","option_name":"redco_optimizer_options","current_count":X,"new_count":X,"values_identical":false,"current_sample":{...},"new_sample":{...}}
🔧 Update result: SUCCESS (values_identical: NO)
✅ Auto-save successful for global-settings-general.enabled
        </div>
    </div>

    <script>
        // Add some basic testing functionality
        function runBasicTests() {
            const results = [];
            
            // Test 1: Check if we're on a WordPress admin page
            results.push({
                test: 'WordPress Admin Page',
                result: window.location.href.includes('/wp-admin/'),
                message: window.location.href.includes('/wp-admin/') ? 'On WordPress admin page' : 'Not on WordPress admin page'
            });
            
            // Test 2: Check if jQuery is available
            results.push({
                test: 'jQuery Available',
                result: typeof jQuery !== 'undefined',
                message: typeof jQuery !== 'undefined' ? 'jQuery is loaded' : 'jQuery is not loaded'
            });
            
            // Test 3: Check if redcoAjax is available
            results.push({
                test: 'redcoAjax Available',
                result: typeof redcoAjax !== 'undefined',
                message: typeof redcoAjax !== 'undefined' ? 'redcoAjax is loaded' : 'redcoAjax is not loaded'
            });
            
            // Display results
            const resultsDiv = document.getElementById('test-results');
            if (resultsDiv) {
                resultsDiv.innerHTML = results.map(r => 
                    `<div class="test-result ${r.result ? 'success' : 'error'}">
                        <strong>${r.test}:</strong> ${r.message}
                    </div>`
                ).join('');
            }
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runBasicTests);
    </script>

    <div class="test-section">
        <h2>Basic Environment Tests</h2>
        <div id="test-results">
            <p>Loading tests...</p>
        </div>
    </div>
</body>
</html>
