<?php
/**
 * Enhanced Backup System for Redco Diagnostic & Auto-Fix
 * 
 * Phase 1 Enhancement: Advanced backup and rollback functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Diagnostic_Enhanced_Backup {
    
    private $backup_dir;
    private $max_backups = 10;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->backup_dir = redco_diagnostic_get_cache_dir('backup');
    }
    
    /**
     * Initialize the enhanced backup system
     */
    public function init() {
        // AJAX handlers
        add_action('wp_ajax_redco_diagnostic_create_backup', array($this, 'ajax_create_backup'));
        add_action('wp_ajax_redco_diagnostic_restore_backup', array($this, 'ajax_restore_backup'));
        add_action('wp_ajax_redco_diagnostic_delete_backup', array($this, 'ajax_delete_backup'));
        add_action('wp_ajax_redco_diagnostic_list_backups', array($this, 'ajax_list_backups'));
        
        // Hooks for automatic backup creation
        add_action('redco_diagnostic_before_fix_apply', array($this, 'create_automatic_backup'), 10, 2);
        
        // Cleanup old backups
        add_action('redco_diagnostic_cleanup_backups', array($this, 'cleanup_old_backups'));
        
        // Schedule cleanup if not already scheduled
        if (!wp_next_scheduled('redco_diagnostic_cleanup_backups')) {
            wp_schedule_event(time(), 'daily', 'redco_diagnostic_cleanup_backups');
        }
        
        // Create database table for backup metadata
        $this->maybe_create_backup_table();
    }
    
    /**
     * AJAX: Create backup
     */
    public function ajax_create_backup() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $backup_type = sanitize_text_field($_POST['backup_type'] ?? 'manual');
        $description = sanitize_text_field($_POST['description'] ?? '');
        
        try {
            $backup_id = $this->create_backup($backup_type, $description);
            
            wp_send_json_success(array(
                'backup_id' => $backup_id,
                'message' => 'Backup created successfully',
                'backup_info' => $this->get_backup_info($backup_id)
            ));
            
        } catch (Exception $e) {
            wp_send_json_error('Backup failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Create backup
     */
    public function create_backup($type = 'manual', $description = '', $fix_context = array()) {
        $backup_id = 'backup_' . time() . '_' . wp_generate_password(8, false);
        $backup_path = $this->backup_dir . $backup_id . '/';
        
        // Create backup directory
        if (!wp_mkdir_p($backup_path)) {
            throw new Exception('Failed to create backup directory');
        }
        
        redco_diagnostic_log("Creating backup (ID: {$backup_id}, Type: {$type})");
        
        $backup_data = array(
            'backup_id' => $backup_id,
            'type' => $type,
            'description' => $description,
            'created_at' => current_time('mysql'),
            'created_by' => get_current_user_id(),
            'fix_context' => $fix_context,
            'files' => array(),
            'database_changes' => array(),
            'settings_backup' => array(),
            'size' => 0
        );
        
        // Backup critical files
        $backup_data['files'] = $this->backup_critical_files($backup_path);
        
        // Backup database settings
        $backup_data['settings_backup'] = $this->backup_settings();
        
        // Backup .htaccess if it exists
        if (file_exists(ABSPATH . '.htaccess')) {
            $htaccess_backup = $backup_path . 'htaccess_backup.txt';
            copy(ABSPATH . '.htaccess', $htaccess_backup);
            $backup_data['files']['htaccess'] = $htaccess_backup;
        }
        
        // Calculate backup size
        $backup_data['size'] = $this->calculate_backup_size($backup_path);
        
        // Save backup metadata
        $this->save_backup_metadata($backup_data);
        
        // Save backup manifest
        file_put_contents(
            $backup_path . 'manifest.json',
            json_encode($backup_data, JSON_PRETTY_PRINT)
        );
        
        redco_diagnostic_log("Backup created successfully (ID: {$backup_id}, Size: " . redco_diagnostic_format_bytes($backup_data['size']) . ")");
        
        // Cleanup old backups
        $this->cleanup_old_backups();
        
        return $backup_id;
    }
    
    /**
     * Backup critical files
     */
    private function backup_critical_files($backup_path) {
        $files_backed_up = array();
        
        // wp-config.php
        if (file_exists(ABSPATH . 'wp-config.php')) {
            $wp_config_backup = $backup_path . 'wp-config.php';
            if (copy(ABSPATH . 'wp-config.php', $wp_config_backup)) {
                $files_backed_up['wp_config'] = $wp_config_backup;
            }
        }
        
        // Active theme's functions.php
        $theme_dir = get_template_directory();
        $functions_file = $theme_dir . '/functions.php';
        if (file_exists($functions_file)) {
            $functions_backup = $backup_path . 'functions.php';
            if (copy($functions_file, $functions_backup)) {
                $files_backed_up['functions'] = $functions_backup;
            }
        }
        
        // Active plugins (just the main files for reference)
        $active_plugins = get_option('active_plugins', array());
        $plugins_backup_dir = $backup_path . 'plugins/';
        wp_mkdir_p($plugins_backup_dir);
        
        foreach ($active_plugins as $plugin) {
            $plugin_file = WP_PLUGIN_DIR . '/' . $plugin;
            if (file_exists($plugin_file)) {
                $plugin_backup = $plugins_backup_dir . basename($plugin);
                if (copy($plugin_file, $plugin_backup)) {
                    $files_backed_up['plugins'][] = $plugin_backup;
                }
            }
        }
        
        return $files_backed_up;
    }
    
    /**
     * Backup settings
     */
    private function backup_settings() {
        $settings_backup = array();
        
        // WordPress core settings
        $core_options = array(
            'active_plugins',
            'stylesheet',
            'template',
            'blogname',
            'blogdescription',
            'users_can_register',
            'default_role',
            'timezone_string',
            'date_format',
            'time_format'
        );
        
        foreach ($core_options as $option) {
            $settings_backup['core'][$option] = get_option($option);
        }
        
        // Plugin-specific settings that might be affected by fixes
        $plugin_options = array(
            'redco_diagnostic_options',
            'redco_diagnostic_settings'
        );
        
        foreach ($plugin_options as $option) {
            $settings_backup['plugin'][$option] = get_option($option);
        }
        
        return $settings_backup;
    }
    
    /**
     * Calculate backup size
     */
    private function calculate_backup_size($backup_path) {
        $size = 0;
        
        if (is_dir($backup_path)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($backup_path, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        }
        
        return $size;
    }
    
    /**
     * Save backup metadata to database
     */
    private function save_backup_metadata($backup_data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        
        $wpdb->insert(
            $table_name,
            array(
                'backup_id' => $backup_data['backup_id'],
                'type' => $backup_data['type'],
                'description' => $backup_data['description'],
                'created_at' => $backup_data['created_at'],
                'created_by' => $backup_data['created_by'],
                'size' => $backup_data['size'],
                'fix_context' => json_encode($backup_data['fix_context']),
                'backup_data' => json_encode($backup_data)
            ),
            array('%s', '%s', '%s', '%s', '%d', '%d', '%s', '%s')
        );
    }
    
    /**
     * AJAX: Restore backup
     */
    public function ajax_restore_backup() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $backup_id = sanitize_text_field($_POST['backup_id'] ?? '');
        
        if (empty($backup_id)) {
            wp_send_json_error('Backup ID is required');
            return;
        }
        
        try {
            $result = $this->restore_backup($backup_id);
            
            wp_send_json_success(array(
                'message' => 'Backup restored successfully',
                'restored_items' => $result['restored_items'],
                'warnings' => $result['warnings'] ?? array()
            ));
            
        } catch (Exception $e) {
            wp_send_json_error('Restore failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Restore backup
     */
    public function restore_backup($backup_id) {
        $backup_path = $this->backup_dir . $backup_id . '/';
        
        if (!is_dir($backup_path)) {
            throw new Exception('Backup not found');
        }
        
        // Load backup manifest
        $manifest_file = $backup_path . 'manifest.json';
        if (!file_exists($manifest_file)) {
            throw new Exception('Backup manifest not found');
        }
        
        $backup_data = json_decode(file_get_contents($manifest_file), true);
        if (!$backup_data) {
            throw new Exception('Invalid backup manifest');
        }
        
        redco_diagnostic_log("Restoring backup (ID: {$backup_id})");
        
        $restored_items = array();
        $warnings = array();
        
        // Restore files
        if (isset($backup_data['files'])) {
            foreach ($backup_data['files'] as $file_type => $file_path) {
                if ($file_type === 'plugins') {
                    // Skip plugin restoration for safety
                    $warnings[] = 'Plugin files were not restored for safety reasons';
                    continue;
                }
                
                try {
                    $this->restore_file($file_type, $file_path, $backup_path);
                    $restored_items[] = $file_type;
                } catch (Exception $e) {
                    $warnings[] = "Failed to restore {$file_type}: " . $e->getMessage();
                }
            }
        }
        
        // Restore settings
        if (isset($backup_data['settings_backup'])) {
            try {
                $this->restore_settings($backup_data['settings_backup']);
                $restored_items[] = 'settings';
            } catch (Exception $e) {
                $warnings[] = 'Failed to restore settings: ' . $e->getMessage();
            }
        }
        
        redco_diagnostic_log("Backup restored (ID: {$backup_id}, Items: " . implode(', ', $restored_items) . ")");
        
        return array(
            'restored_items' => $restored_items,
            'warnings' => $warnings
        );
    }
    
    /**
     * Restore individual file
     */
    private function restore_file($file_type, $backup_file_path, $backup_dir) {
        switch ($file_type) {
            case 'wp_config':
                $target = ABSPATH . 'wp-config.php';
                break;
            case 'functions':
                $target = get_template_directory() . '/functions.php';
                break;
            case 'htaccess':
                $target = ABSPATH . '.htaccess';
                $backup_file_path = $backup_dir . 'htaccess_backup.txt';
                break;
            default:
                throw new Exception("Unknown file type: {$file_type}");
        }
        
        if (!file_exists($backup_file_path)) {
            throw new Exception("Backup file not found: {$backup_file_path}");
        }
        
        // Create backup of current file before restoring
        if (file_exists($target)) {
            $current_backup = $target . '.pre-restore-' . time();
            copy($target, $current_backup);
        }
        
        if (!copy($backup_file_path, $target)) {
            throw new Exception("Failed to restore file: {$target}");
        }
    }
    
    /**
     * Restore settings
     */
    private function restore_settings($settings_backup) {
        // Restore core settings
        if (isset($settings_backup['core'])) {
            foreach ($settings_backup['core'] as $option => $value) {
                update_option($option, $value);
            }
        }
        
        // Restore plugin settings
        if (isset($settings_backup['plugin'])) {
            foreach ($settings_backup['plugin'] as $option => $value) {
                update_option($option, $value);
            }
        }
    }
    
    /**
     * AJAX: List backups
     */
    public function ajax_list_backups() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $backups = $this->list_backups();
        
        wp_send_json_success(array(
            'backups' => $backups,
            'total_count' => count($backups),
            'total_size' => $this->calculate_total_backup_size()
        ));
    }
    
    /**
     * List all backups
     */
    public function list_backups() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        
        $backups = $wpdb->get_results(
            "SELECT * FROM {$table_name} ORDER BY created_at DESC",
            ARRAY_A
        );
        
        foreach ($backups as &$backup) {
            $backup['size_formatted'] = redco_diagnostic_format_bytes($backup['size']);
            $backup['created_at_formatted'] = date('Y-m-d H:i:s', strtotime($backup['created_at']));
            $backup['fix_context'] = json_decode($backup['fix_context'], true);
        }
        
        return $backups;
    }
    
    /**
     * Get backup information
     */
    public function get_backup_info($backup_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        
        $backup = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$table_name} WHERE backup_id = %s", $backup_id),
            ARRAY_A
        );
        
        if ($backup) {
            $backup['size_formatted'] = redco_diagnostic_format_bytes($backup['size']);
            $backup['created_at_formatted'] = date('Y-m-d H:i:s', strtotime($backup['created_at']));
            $backup['fix_context'] = json_decode($backup['fix_context'], true);
        }
        
        return $backup;
    }
    
    /**
     * Create automatic backup before applying fixes
     */
    public function create_automatic_backup($fix_id, $fix_context) {
        try {
            $description = "Automatic backup before applying fix: {$fix_id}";
            $backup_id = $this->create_backup('automatic', $description, $fix_context);
            
            redco_diagnostic_log("Automatic backup created before fix application (Backup ID: {$backup_id}, Fix: {$fix_id})");
            
            return $backup_id;
        } catch (Exception $e) {
            redco_diagnostic_log("Failed to create automatic backup: " . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * Cleanup old backups
     */
    public function cleanup_old_backups() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        
        // Get backups older than the limit
        $old_backups = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT backup_id FROM {$table_name} ORDER BY created_at DESC LIMIT %d, 999999",
                $this->max_backups
            )
        );
        
        foreach ($old_backups as $backup) {
            $this->delete_backup($backup->backup_id);
        }
    }
    
    /**
     * Delete backup
     */
    public function delete_backup($backup_id) {
        global $wpdb;
        
        // Delete backup files
        $backup_path = $this->backup_dir . $backup_id . '/';
        if (is_dir($backup_path)) {
            $this->delete_directory($backup_path);
        }
        
        // Delete from database
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        $wpdb->delete($table_name, array('backup_id' => $backup_id), array('%s'));
        
        redco_diagnostic_log("Backup deleted (ID: {$backup_id})");
    }
    
    /**
     * Delete directory recursively
     */
    private function delete_directory($dir) {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), array('.', '..'));
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->delete_directory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
    
    /**
     * Calculate total backup size
     */
    private function calculate_total_backup_size() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        
        $total_size = $wpdb->get_var("SELECT SUM(size) FROM {$table_name}");
        
        return $total_size ?: 0;
    }
    
    /**
     * Maybe create backup table
     */
    private function maybe_create_backup_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        $charset_collate = $wpdb->get_charset_collate();
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
            $sql = "CREATE TABLE {$table_name} (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                backup_id varchar(50) NOT NULL,
                type varchar(20) NOT NULL DEFAULT 'manual',
                description text,
                created_at datetime NOT NULL,
                created_by bigint(20) unsigned NOT NULL,
                size bigint(20) unsigned NOT NULL DEFAULT 0,
                fix_context text,
                backup_data longtext,
                PRIMARY KEY (id),
                UNIQUE KEY backup_id (backup_id),
                KEY created_at (created_at),
                KEY type (type)
            ) {$charset_collate};";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }
}
