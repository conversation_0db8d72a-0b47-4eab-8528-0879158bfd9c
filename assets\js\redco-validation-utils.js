/**
 * CRITICAL FIX: Centralized Validation Utility Library
 * Eliminates code duplication of email/URL validation across 8 instances
 * Provides consistent validation patterns and error handling
 */

(function() {
    'use strict';

    /**
     * Centralized Validation Utility System
     */
    window.RedcoValidation = {
        
        /**
         * Validation patterns
         */
        patterns: {
            email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            url: /^https?:\/\/.+/,
            domain: /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/,
            ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
            slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
            hexColor: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
        },
        
        /**
         * Validate email address
         * 
         * @param {string} email Email to validate
         * @return {boolean} True if valid
         */
        isValidEmail: function(email) {
            if (!email || typeof email !== 'string') {
                return false;
            }
            return this.patterns.email.test(email.trim());
        },
        
        /**
         * Validate URL
         * 
         * @param {string} url URL to validate
         * @return {boolean} True if valid
         */
        isValidUrl: function(url) {
            if (!url || typeof url !== 'string') {
                return false;
            }
            
            try {
                new URL(url);
                return true;
            } catch {
                return false;
            }
        },
        
        /**
         * Validate domain name
         * 
         * @param {string} domain Domain to validate
         * @return {boolean} True if valid
         */
        isValidDomain: function(domain) {
            if (!domain || typeof domain !== 'string') {
                return false;
            }
            return this.patterns.domain.test(domain.trim());
        },
        
        /**
         * Validate IPv4 address
         * 
         * @param {string} ip IP address to validate
         * @return {boolean} True if valid
         */
        isValidIPv4: function(ip) {
            if (!ip || typeof ip !== 'string') {
                return false;
            }
            return this.patterns.ipv4.test(ip.trim());
        },
        
        /**
         * Validate number within range
         * 
         * @param {number} value Value to validate
         * @param {number} min Minimum value
         * @param {number} max Maximum value
         * @return {boolean} True if valid
         */
        isValidNumber: function(value, min = null, max = null) {
            const num = parseFloat(value);
            if (isNaN(num)) {
                return false;
            }
            
            if (min !== null && num < min) {
                return false;
            }
            
            if (max !== null && num > max) {
                return false;
            }
            
            return true;
        },
        
        /**
         * Validate field based on type and rules
         * 
         * @param {jQuery} $field Field element
         * @param {Object} rules Validation rules
         * @return {Object} Validation result
         */
        validateField: function($field, rules = {}) {
            const value = $field.val().trim();
            const fieldType = $field.attr('type') || 'text';
            const required = $field.attr('required') || rules.required || false;
            
            const result = {
                valid: true,
                message: '',
                value: value
            };
            
            // Check required fields
            if (required && !value) {
                result.valid = false;
                result.message = 'This field is required';
                return result;
            }
            
            // Skip validation for empty optional fields
            if (!value && !required) {
                return result;
            }
            
            // Type-specific validation
            switch (fieldType) {
                case 'email':
                    if (!this.isValidEmail(value)) {
                        result.valid = false;
                        result.message = 'Please enter a valid email address';
                    }
                    break;
                    
                case 'url':
                    if (!this.isValidUrl(value)) {
                        result.valid = false;
                        result.message = 'Please enter a valid URL';
                    }
                    break;
                    
                case 'number':
                    const min = parseFloat($field.attr('min'));
                    const max = parseFloat($field.attr('max'));
                    if (!this.isValidNumber(value, isNaN(min) ? null : min, isNaN(max) ? null : max)) {
                        result.valid = false;
                        result.message = 'Please enter a valid number';
                        if (!isNaN(min) && !isNaN(max)) {
                            result.message += ` between ${min} and ${max}`;
                        } else if (!isNaN(min)) {
                            result.message += ` greater than or equal to ${min}`;
                        } else if (!isNaN(max)) {
                            result.message += ` less than or equal to ${max}`;
                        }
                    }
                    break;
            }
            
            // Custom pattern validation
            if (rules.pattern && result.valid) {
                const pattern = new RegExp(rules.pattern);
                if (!pattern.test(value)) {
                    result.valid = false;
                    result.message = rules.patternMessage || 'Invalid format';
                }
            }
            
            // Custom validation function
            if (rules.validator && typeof rules.validator === 'function' && result.valid) {
                const customResult = rules.validator(value, $field);
                if (customResult !== true) {
                    result.valid = false;
                    result.message = customResult || 'Invalid value';
                }
            }
            
            return result;
        },
        
        /**
         * Validate entire form
         * 
         * @param {jQuery} $form Form element
         * @param {Object} fieldRules Field-specific rules
         * @return {Object} Validation result
         */
        validateForm: function($form, fieldRules = {}) {
            const results = {
                valid: true,
                errors: {},
                errorCount: 0
            };
            
            $form.find('input, select, textarea').each((index, element) => {
                const $field = $(element);
                const fieldName = $field.attr('name');
                
                if (!fieldName) return;
                
                const rules = fieldRules[fieldName] || {};
                const result = this.validateField($field, rules);
                
                if (!result.valid) {
                    results.valid = false;
                    results.errors[fieldName] = result.message;
                    results.errorCount++;
                    
                    // Add visual error indication
                    this.showFieldError($field, result.message);
                } else {
                    // Remove error indication
                    this.clearFieldError($field);
                }
            });
            
            return results;
        },
        
        /**
         * Show field validation error
         * 
         * @param {jQuery} $field Field element
         * @param {string} message Error message
         */
        showFieldError: function($field, message) {
            $field.addClass('redco-field-error');
            
            // Remove existing error message
            $field.siblings('.redco-field-error-message').remove();
            
            // Add error message
            $field.after(`<div class="redco-field-error-message">${message}</div>`);
        },
        
        /**
         * Clear field validation error
         * 
         * @param {jQuery} $field Field element
         */
        clearFieldError: function($field) {
            $field.removeClass('redco-field-error');
            $field.siblings('.redco-field-error-message').remove();
        },
        
        /**
         * Setup real-time validation for form
         * 
         * @param {jQuery} $form Form element
         * @param {Object} fieldRules Field-specific rules
         */
        setupRealTimeValidation: function($form, fieldRules = {}) {
            const self = this;
            
            $form.find('input, select, textarea').on('blur change', function() {
                const $field = $(this);
                const fieldName = $field.attr('name');
                
                if (fieldName && fieldRules[fieldName]) {
                    const result = self.validateField($field, fieldRules[fieldName]);
                    
                    if (!result.valid) {
                        self.showFieldError($field, result.message);
                    } else {
                        self.clearFieldError($field);
                    }
                }
            });
        },
        
        /**
         * Debounce function for performance
         * 
         * @param {Function} func Function to debounce
         * @param {number} wait Wait time in milliseconds
         * @return {Function} Debounced function
         */
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };

})();
