/**
 * Enhanced Auto-Save System for Redco Optimizer
 * 
 * Provides intelligent auto-save functionality with debouncing,
 * visual feedback, and comprehensive error handling.
 */

(function($) {
    'use strict';

    const RedcoAutoSave = {
        // Configuration
        config: {
            debounceDelay: 2500, // 2.5 seconds
            maxRetries: 3,
            retryDelay: 1000,
            validationDelay: 500
        },

        // State management
        state: {
            pendingSaves: new Map(),
            saveTimers: new Map(),
            validationTimers: new Map(),
            isOnline: navigator.onLine
        },

        // Initialize auto-save system
        init: function() {
            this.bindEvents();
            this.setupNetworkMonitoring();
            this.setupVisualFeedback();
            this.loadSavedState();
        },

        // Bind events to form elements
        bindEvents: function() {
            const self = this;

            // Auto-save on input change with debouncing
            $(document).on('input change', '.auto-save-field', function() {
                const $field = $(this);
                const module = $field.data('module');
                const setting = $field.data('setting');
                
                if (module && setting) {
                    self.scheduleAutoSave($field, module, setting);
                }
            });

            // Handle checkbox changes
            $(document).on('change', 'input[type="checkbox"].auto-save-field', function() {
                const $field = $(this);
                const module = $field.data('module');
                const setting = $field.data('setting');
                
                if (module && setting) {
                    self.scheduleAutoSave($field, module, setting);
                }
            });

            // Handle select changes
            $(document).on('change', 'select.auto-save-field', function() {
                const $field = $(this);
                const module = $field.data('module');
                const setting = $field.data('setting');
                
                if (module && setting) {
                    self.scheduleAutoSave($field, module, setting);
                }
            });

            // Prevent form submission for auto-save enabled forms
            $(document).on('submit', '.auto-save-form', function(e) {
                e.preventDefault();
                self.showMessage('Settings are automatically saved', 'info');
            });
        },

        // Schedule auto-save with debouncing
        scheduleAutoSave: function($field, module, setting) {
            const key = `${module}_${setting}`;
            
            // Clear existing timer
            if (this.state.saveTimers.has(key)) {
                clearTimeout(this.state.saveTimers.get(key));
            }

            // Show saving indicator
            this.showFieldStatus($field, 'saving');

            // Schedule new save
            const timer = setTimeout(() => {
                this.performAutoSave($field, module, setting);
            }, this.config.debounceDelay);

            this.state.saveTimers.set(key, timer);
        },

        // Perform the actual auto-save
        performAutoSave: function($field, module, setting, retryCount = 0) {
            const self = this;
            const key = `${module}_${setting}`;
            const value = this.getFieldValue($field);

            // Check network connectivity
            if (!this.state.isOnline) {
                this.showFieldStatus($field, 'offline');
                this.queueForLater(module, setting, value);
                return;
            }

            // Validate before saving
            this.validateSetting(module, setting, value).then(function(isValid) {
                if (!isValid.valid && isValid.validated_value !== undefined) {
                    // Update field with validated value
                    self.setFieldValue($field, isValid.validated_value);
                    value = isValid.validated_value;
                }

                // Perform AJAX save
                $.ajax({
                    url: redcoAdmin.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_auto_save_setting',
                        module: module,
                        setting: setting,
                        value: value,
                        nonce: redcoAdmin.nonce
                    },
                    timeout: 10000,
                    success: function(response) {
                        if (response.success) {
                            self.showFieldStatus($field, 'saved');
                            self.saveToLocalStorage(module, setting, value);
                            self.state.pendingSaves.delete(key);
                        } else {
                            self.handleSaveError($field, module, setting, response.data, retryCount);
                        }
                    },
                    error: function(xhr, status, error) {
                        self.handleSaveError($field, module, setting, error, retryCount);
                    }
                });
            });
        },

        // Validate setting before saving
        validateSetting: function(module, setting, value) {
            return $.ajax({
                url: redcoAdmin.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_validate_setting',
                    module: module,
                    setting: setting,
                    value: value,
                    nonce: redcoAdmin.nonce
                },
                timeout: 5000
            }).then(function(response) {
                return response.success ? response.data : { valid: true };
            }).catch(function() {
                return { valid: true }; // Assume valid if validation fails
            });
        },

        // Handle save errors with retry logic
        handleSaveError: function($field, module, setting, error, retryCount) {
            if (retryCount < this.config.maxRetries) {
                // Retry after delay
                setTimeout(() => {
                    this.performAutoSave($field, module, setting, retryCount + 1);
                }, this.config.retryDelay * (retryCount + 1));
                
                this.showFieldStatus($field, 'retrying');
            } else {
                // Max retries reached
                this.showFieldStatus($field, 'error');
                this.queueForLater(module, setting, this.getFieldValue($field));
                this.showMessage('Failed to save setting. Will retry when connection is restored.', 'error');
            }
        },

        // Get field value based on field type
        getFieldValue: function($field) {
            if ($field.is(':checkbox')) {
                return $field.is(':checked');
            } else if ($field.is(':radio')) {
                return $field.filter(':checked').val();
            } else {
                return $field.val();
            }
        },

        // Set field value based on field type
        setFieldValue: function($field, value) {
            if ($field.is(':checkbox')) {
                $field.prop('checked', !!value);
            } else if ($field.is(':radio')) {
                $field.filter(`[value="${value}"]`).prop('checked', true);
            } else {
                $field.val(value);
            }
        },

        // Show field status with visual feedback
        showFieldStatus: function($field, status) {
            const $container = $field.closest('.setting-item, .form-group, .redco-setting');
            let $indicator = $container.find('.auto-save-indicator');
            
            if ($indicator.length === 0) {
                $indicator = $('<span class="auto-save-indicator"></span>');
                $field.after($indicator);
            }

            // Remove existing status classes
            $indicator.removeClass('saving saved error retrying offline');
            
            // Add new status class and content
            switch (status) {
                case 'saving':
                    $indicator.addClass('saving').html('<span class="dashicons dashicons-update-alt"></span> Saving...');
                    break;
                case 'saved':
                    $indicator.addClass('saved').html('<span class="dashicons dashicons-yes-alt"></span> Saved');
                    setTimeout(() => $indicator.fadeOut(), 2000);
                    break;
                case 'error':
                    $indicator.addClass('error').html('<span class="dashicons dashicons-warning"></span> Error');
                    break;
                case 'retrying':
                    $indicator.addClass('retrying').html('<span class="dashicons dashicons-update"></span> Retrying...');
                    break;
                case 'offline':
                    $indicator.addClass('offline').html('<span class="dashicons dashicons-cloud"></span> Offline');
                    break;
            }
            
            $indicator.show();
        },

        // Setup visual feedback styles
        setupVisualFeedback: function() {
            if ($('#redco-auto-save-styles').length === 0) {
                $('head').append(`
                    <style id="redco-auto-save-styles">
                        .auto-save-indicator {
                            margin-left: 8px;
                            font-size: 12px;
                            display: inline-flex;
                            align-items: center;
                            gap: 4px;
                        }
                        .auto-save-indicator.saving { color: #0073aa; }
                        .auto-save-indicator.saved { color: #46b450; }
                        .auto-save-indicator.error { color: #dc3232; }
                        .auto-save-indicator.retrying { color: #ffb900; }
                        .auto-save-indicator.offline { color: #666; }
                        .auto-save-indicator .dashicons {
                            font-size: 14px;
                            width: 14px;
                            height: 14px;
                        }
                        .auto-save-indicator.saving .dashicons {
                            animation: spin 1s linear infinite;
                        }
                        @keyframes spin {
                            from { transform: rotate(0deg); }
                            to { transform: rotate(360deg); }
                        }
                    </style>
                `);
            }
        },

        // Setup network monitoring
        setupNetworkMonitoring: function() {
            const self = this;
            
            window.addEventListener('online', function() {
                self.state.isOnline = true;
                self.processPendingSaves();
                self.showMessage('Connection restored. Processing pending saves...', 'success');
            });

            window.addEventListener('offline', function() {
                self.state.isOnline = false;
                self.showMessage('Connection lost. Changes will be saved when connection is restored.', 'warning');
            });
        },

        // Queue settings for later when offline
        queueForLater: function(module, setting, value) {
            const key = `${module}_${setting}`;
            this.state.pendingSaves.set(key, { module, setting, value });
            this.saveToLocalStorage(module, setting, value, true);
        },

        // Process pending saves when back online
        processPendingSaves: function() {
            const self = this;
            this.state.pendingSaves.forEach(function(data, key) {
                const $field = $(`.auto-save-field[data-module="${data.module}"][data-setting="${data.setting}"]`);
                if ($field.length > 0) {
                    self.performAutoSave($field, data.module, data.setting);
                }
            });
        },

        // Save to localStorage for persistence
        saveToLocalStorage: function(module, setting, value, isPending = false) {
            try {
                const key = `redco_${module}_${setting}`;
                const data = { value, timestamp: Date.now(), pending: isPending };
                localStorage.setItem(key, JSON.stringify(data));
            } catch (e) {
                // localStorage not available or full
            }
        },

        // Load saved state from localStorage
        loadSavedState: function() {
            try {
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('redco_')) {
                        const data = JSON.parse(localStorage.getItem(key));
                        if (data.pending) {
                            const parts = key.replace('redco_', '').split('_');
                            const setting = parts.pop();
                            const module = parts.join('_');
                            this.state.pendingSaves.set(`${module}_${setting}`, {
                                module, setting, value: data.value
                            });
                        }
                    }
                }
            } catch (e) {
                // Error loading from localStorage
            }
        },

        // Show user messages
        showMessage: function(message, type = 'info') {
            // Create or update message container
            let $container = $('#redco-auto-save-messages');
            if ($container.length === 0) {
                $container = $('<div id="redco-auto-save-messages"></div>');
                $('.redco-module-content, .wrap').first().prepend($container);
            }

            const $message = $(`
                <div class="notice notice-${type} is-dismissible auto-save-message">
                    <p>${message}</p>
                </div>
            `);

            $container.append($message);

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                $message.fadeOut(() => $message.remove());
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Only initialize on Redco Optimizer pages
        if ($('.redco-optimizer-admin, .redco-module-tab').length > 0) {
            RedcoAutoSave.init();
        }
    });

    // Expose to global scope for external access
    window.RedcoAutoSave = RedcoAutoSave;

})(jQuery);
