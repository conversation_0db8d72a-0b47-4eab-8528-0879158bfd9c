/**
 * Diagnostic & Auto-Fix Module Styles
 * Redco Optimizer Plugin
 * PERFORMANCE OPTIMIZED: Includes loading states and progressive enhancement
 */

/* PERFORMANCE: Using global universal loading system - custom loading styles removed */

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Further Optimization Opportunities Section */
.optimization-opportunities-section .optimization-categories {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.optimization-category {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background: #f9f9f9;
}

.optimization-category .category-header {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.optimization-category .category-header:hover {
    background: #f5f5f5;
}

.optimization-category .category-header .dashicons {
    color: #4CAF50;
    font-size: 16px;
}

.optimization-category .category-header h4 {
    margin: 0;
    flex: 1;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

.optimization-category .category-count {
    background: #4CAF50;
    color: white;
    border-radius: 12px;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.optimization-category .category-content {
    padding: 12px 16px;
    background: white;
}

.optimization-category .loading-placeholder {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #666;
    font-style: italic;
}

.optimization-category .loading-placeholder .dashicons {
    animation: spin 1s linear infinite;
}

.optimization-opportunity {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-bottom: 8px;
    background: white;
    transition: all 0.3s ease;
}

.optimization-opportunity:hover {
    border-color: #4CAF50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.optimization-opportunity .opportunity-icon {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
    flex-shrink: 0;
}

.optimization-opportunity .opportunity-content {
    flex: 1;
}

.optimization-opportunity .opportunity-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
    font-size: 14px;
}

.optimization-opportunity .opportunity-description {
    color: #666;
    font-size: 13px;
    line-height: 1.3;
    margin-bottom: 8px;
}

.optimization-opportunity .opportunity-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
}

.optimization-opportunity .opportunity-actions {
    display: flex;
    gap: 6px;
}

.optimization-opportunity .apply-optimization-fix {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.optimization-opportunity .apply-optimization-fix:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.optimization-opportunity .apply-optimization-fix:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.optimization-opportunity .apply-optimization-fix .dashicons {
    font-size: 12px;
}

.optimization-opportunity .manual-fix-required {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: #f0f0f0;
    color: #666;
    border-radius: 3px;
    font-size: 11px;
    font-style: italic;
}

.optimization-opportunity .manual-fix-required .dashicons {
    font-size: 12px;
    color: #999;
}

/* Optimization Opportunity States */
.optimization-opportunity.processing {
    background: #fff8e1;
    border-color: #ffc107;
}

.optimization-opportunity.processing .opportunity-icon {
    background: linear-gradient(135deg, #fff8e1, #ffecb3);
    color: #f57c00;
}

.optimization-opportunity.completed {
    background: #e8f5e8;
    border-color: #4CAF50;
    opacity: 0.8;
}

.optimization-opportunity.completed .opportunity-icon {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
}

.optimization-opportunity.completed .opportunity-title {
    text-decoration: line-through;
    color: #666;
}

.optimization-opportunity.failed {
    background: #ffebee;
    border-color: #f44336;
}

.optimization-opportunity.failed .opportunity-icon {
    background: linear-gradient(135deg, #ffebee, #ffcdd2);
    color: #c62828;
}

.optimization-opportunity .opportunity-impact {
    display: flex;
    gap: 12px;
    font-size: 12px;
}

.optimization-opportunity .impact-badge {
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 500;
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.impact-badge.high {
    background: #ffebee;
    color: #c62828;
}

.impact-badge.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.impact-badge.low {
    background: #e8f5e8;
    color: #2e7d32;
}

/* Apply All Button - Enhanced Styling */
#apply-all-optimizations {
    background: #4CAF50 !important;
    border-color: #4CAF50 !important;
    color: white !important;
    font-weight: 600 !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    border-radius: 6px !important;
    transition: all 0.3s ease;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
    min-height: 32px !important;
}

#apply-all-optimizations:hover {
    background: #45a049 !important;
    border-color: #45a049 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

#apply-all-optimizations:disabled {
    background: #ccc !important;
    border-color: #ccc !important;
    transform: none;
    box-shadow: none;
}

#apply-all-optimizations .dashicons {
    color: white !important;
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
}

/* Card Actions Spacing - Enhanced */
.card-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* Scan for Opportunities Button - Enhanced Styling */
#scan-optimization-opportunities {
    padding: 8px 16px !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    border-radius: 6px !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
    min-height: 32px !important;
    font-weight: 500 !important;
}

#scan-optimization-opportunities .dashicons {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
}

/* Optimization Opportunities Container */
.optimization-opportunities-content {
    max-height: 400px;
    overflow-y: auto;
}

.optimization-category {
    margin-bottom: 16px;
}

.optimization-category:last-child {
    margin-bottom: 0;
}

/* No Opportunities Message */
.no-opportunities {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.no-opportunities p {
    margin: 0;
    font-size: 13px;
}

/* User Feedback Section */
.user-feedback-section .feedback-tabs {
    margin-top: 20px;
}

.user-feedback-section .feedback-tab-nav {
    display: flex;
    gap: 4px;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 20px;
}

.user-feedback-section .feedback-tab-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: #666;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    font-weight: 500;
}

.user-feedback-section .feedback-tab-btn:hover {
    background: #f5f5f5;
    color: #4CAF50;
}

.user-feedback-section .feedback-tab-btn.active {
    color: #4CAF50;
    border-bottom-color: #4CAF50;
    background: #f8fff8;
}

.user-feedback-section .feedback-tab-btn .dashicons {
    font-size: 16px;
}

.user-feedback-section .feedback-tab-content {
    min-height: 300px;
}

.user-feedback-section .feedback-tab-panel {
    display: none;
}

.user-feedback-section .feedback-tab-panel.active {
    display: block;
}

/* Experience Rating */
.experience-rating h4 {
    margin-bottom: 16px;
    color: #2c3e50;
}

.rating-stars {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
}

.rating-stars .star {
    font-size: 32px;
    color: #ddd;
    cursor: pointer;
    transition: color 0.3s ease;
    user-select: none;
}

.rating-stars .star:hover,
.rating-stars .star.active {
    color: #ffc107;
}

.rating-feedback textarea {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    margin-bottom: 12px;
    resize: vertical;
}

/* Performance Feedback */
.performance-metrics {
    display: grid;
    gap: 16px;
    margin-bottom: 20px;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.metric-item label {
    min-width: 200px;
    font-weight: 500;
    color: #2c3e50;
}

.metric-item select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.metric-item .metric-value {
    flex: 1;
    padding: 8px 12px;
    background: #f9f9f9;
    border-radius: 4px;
    color: #2c3e50;
}

/* Form Styling */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
}

/* Feedback History */
.feedback-history {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.feedback-history h4 {
    margin-bottom: 16px;
    color: #2c3e50;
}

.feedback-history-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 12px;
    background: #f9f9f9;
}

.feedback-history-item .feedback-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #4CAF50;
    color: white;
    flex-shrink: 0;
}

.feedback-history-item .feedback-content {
    flex: 1;
}

.feedback-history-item .feedback-type {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.feedback-history-item .feedback-date {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.feedback-history-item .feedback-summary {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

/* Loading States */
.loading-placeholder {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 20px;
    color: #666;
    font-style: italic;
    justify-content: center;
}

.loading-placeholder .dashicons {
    animation: spin 1s linear infinite;
}

/* Stop spinning animation for ready states */
.loading-placeholder .dashicons.ready {
    animation: none;
}

/* Keyframe for spinning animation */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .feedback-tab-nav {
        flex-wrap: wrap;
    }

    .feedback-tab-btn {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }

    .metric-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .metric-item label {
        min-width: auto;
        margin-bottom: 6px;
    }
}

/* Recent Fixes Sections Styling - Enhanced Specificity */
.redco-card.recent-fixes-section,
.redco-sidebar-section.recent-fixes-section {
    background: #fff !important;
    border: 1px solid #e1e1e1 !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

.recent-fixes-section h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recent-fixes-section h3 .dashicons {
    color: #4CAF50;
    font-size: 18px;
    width: 18px;
    height: 18px;
    vertical-align: middle;
}

/* Recent Fixes List */
.recent-fixes-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.recent-fixes-list li {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.recent-fixes-list li:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.recent-fixes-list .dashicons {
    color: #4CAF50;
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-top: 2px;
    flex-shrink: 0;
}

.recent-fix-content {
    flex: 1;
    min-width: 0;
}

.recent-fix-time {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
}

.recent-fix-details {
    font-size: 13px;
    color: #333;
    line-height: 1.4;
}

.recent-fix-backup {
    font-size: 11px;
    color: #4CAF50;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.recent-fix-backup .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
    margin-top: 0;
}

.recent-fix-backup a {
    color: #4CAF50;
    text-decoration: none;
}

.recent-fix-backup a:hover {
    text-decoration: underline;
}

/* No fixes message */
.no-recent-fixes {
    text-align: center;
    padding: 30px 20px;
    color: #666;
    font-style: italic;
}

.no-recent-fixes .dashicons {
    font-size: 24px;
    color: #ccc;
    margin-bottom: 10px;
    display: block;
}

/* Loading state */
.recent-fixes-loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.recent-fixes-loading .dashicons {
    animation: spin 1s linear infinite;
    color: #4CAF50;
    font-size: 18px;
    margin-right: 8px;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Spinning animation for refresh buttons and loading states */
.dashicons.spin,
.dashicons-update {
    animation: spin 1s linear infinite !important;
}

/* Refresh button styling - Enhanced */
.refresh-recent-fixes,
button.refresh-recent-fixes,
.card-actions .refresh-recent-fixes {
    background: #4CAF50 !important;
    color: white !important;
    border: none !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    cursor: pointer !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 4px !important;
    margin-top: 0 !important;
    transition: background-color 0.2s ease !important;
    text-decoration: none !important;
}

.refresh-recent-fixes:hover,
button.refresh-recent-fixes:hover,
.card-actions .refresh-recent-fixes:hover {
    background: #45a049 !important;
    color: white !important;
}

.refresh-recent-fixes .dashicons,
button.refresh-recent-fixes .dashicons,
.card-actions .refresh-recent-fixes .dashicons {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
    color: white !important;
}

/* Dashicons Alignment - Global Fix for All Buttons */
.diagnostic-autofix-content .dashicons,
.redco-module-tab .dashicons,
.diagnostic-professional .dashicons,
.redco-card .dashicons,
.redco-sidebar-section .dashicons,
button .dashicons,
.button .dashicons,
.action-button .dashicons,
.btn .dashicons {
    vertical-align: middle !important;
    line-height: 1 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-right: 6px !important;
    margin-left: 0 !important;
    flex-shrink: 0 !important;
}

/* Specific button alignment fixes */
.button-block .dashicons,
.button-primary .dashicons,
.button-secondary .dashicons {
    margin-right: 8px !important;
    margin-left: 0 !important;
}

/* Header action buttons */
.header-actions .dashicons {
    margin-right: 6px !important;
}

/* Sidebar button alignment */
.sidebar-actions .dashicons,
.redco-sidebar-section .button .dashicons {
    margin-right: 6px !important;
    margin-left: 0 !important;
}

/* Issue List Items - Updated Layout */
.issue-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 15px;
    border: 1px solid #e1e1e1;
    border-radius: 6px;
    margin-bottom: 10px;
    background: #fff;
    position: relative;
}

/* Issue content takes most space */
.issue-item .issue-content {
    flex: 1;
    min-width: 0;
    padding-right: 120px; /* Space for right-aligned button */
}

/* Right-aligned action buttons */
.issue-actions-right {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.issue-actions-right .action-button {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    transition: all 0.2s ease;
}

.issue-actions-right .action-button.primary {
    background: #4CAF50;
    color: white;
}

.issue-actions-right .action-button.primary:hover {
    background: #45a049;
}

.issue-actions-right .toggle-how-to-solve {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    transition: all 0.2s ease;
}

.issue-actions-right .toggle-how-to-solve:hover {
    background: #e9ecef;
    color: #333;
}

.issue-actions-right .dashicons {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
}

.issue-item .dashicons {
    margin-top: 2px;
    flex-shrink: 0;
}

.issue-item.critical .dashicons {
    color: #dc3545;
}

.issue-item.high .dashicons {
    color: #fd7e14;
}

.issue-item.medium .dashicons {
    color: #ffc107;
}

.issue-item.low .dashicons {
    color: #28a745;
}

/* Action Buttons - General (for non-issue buttons) */
.action-button:not(.issue-actions-right .action-button) {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.action-button:not(.issue-actions-right .action-button) .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.action-button.primary:not(.issue-actions-right .action-button) {
    background: #4CAF50;
    color: white;
}

.action-button.primary:not(.issue-actions-right .action-button):hover {
    background: #45a049;
}

.action-button.secondary:not(.issue-actions-right .action-button) {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #dee2e6;
}

.action-button.secondary:not(.issue-actions-right .action-button):hover {
    background: #e9ecef;
}

.action-button.danger:not(.issue-actions-right .action-button) {
    background: #dc3545;
    color: white;
}

.action-button.danger:not(.issue-actions-right .action-button):hover {
    background: #c82333;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
}

.status-indicator.success {
    background: #d4edda;
    color: #155724;
}

.status-indicator.success .dashicons {
    color: #28a745;
}

.status-indicator.error {
    background: #f8d7da;
    color: #721c24;
}

.status-indicator.error .dashicons {
    color: #dc3545;
}

.status-indicator.warning {
    background: #fff3cd;
    color: #856404;
}

.status-indicator.warning .dashicons {
    color: #ffc107;
}

.status-indicator.info {
    background: #d1ecf1;
    color: #0c5460;
}

.status-indicator.info .dashicons {
    color: #17a2b8;
}

/* How to Solve Tips */
.how-to-solve-tip {
    margin-top: 10px;
    padding: 12px;
    background: #f8f9fa;
    border-left: 4px solid #4CAF50;
    border-radius: 0 4px 4px 0;
    font-size: 13px;
    line-height: 1.5;
}

.how-to-solve-tip h5 {
    margin: 0 0 8px 0;
    color: #4CAF50;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.how-to-solve-tip h5 .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.how-to-solve-tip ol,
.how-to-solve-tip ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.how-to-solve-tip li {
    margin-bottom: 4px;
}

.how-to-solve-tip a {
    color: #4CAF50;
    text-decoration: none;
}

.how-to-solve-tip a:hover {
    text-decoration: underline;
}

.how-to-solve-tip code {
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

/* Toggle for How to Solve Tips */
.toggle-how-to-solve {
    background: none;
    border: none;
    color: #4CAF50;
    cursor: pointer;
    font-size: 12px;
    text-decoration: underline;
    padding: 0;
    margin-top: 8px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.toggle-how-to-solve:hover {
    color: #45a049;
}

.toggle-how-to-solve .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    transition: transform 0.2s ease;
}

.toggle-how-to-solve.expanded .dashicons {
    transform: rotate(180deg);
}

/* Loading content for how to solve tips */
.loading-content {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-style: italic;
}

.loading-content .dashicons.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* No Issues Found Styling */
.no-issues-found {
    text-align: center;
    padding: 40px 20px;
}

.success-message {
    max-width: 500px;
    margin: 0 auto;
}

.success-message .dashicons {
    font-size: 48px;
    color: #4CAF50;
    margin-bottom: 20px;
    display: block;
}

.success-message h4 {
    color: #4CAF50;
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.success-message p {
    color: #666;
    font-size: 16px;
    line-height: 1.6;
    margin: 0 0 15px 0;
}

.success-message .scan-info {
    font-size: 14px;
    color: #888;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e1e1;
}

.success-message .scan-info strong {
    color: #333;
}

/* MAXIMUM COMPACT LAYOUT OPTIMIZATIONS */

/* Reduce card padding and margins */
.redco-card {
    margin-bottom: 8px !important;
    border-radius: 6px !important;
}

.redco-card .card-header {
    padding: 20px 24px !important;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0 !important;
}

.redco-card .card-content {
    padding: 12px 14px !important;
}

.redco-card .card-header h3 {
    font-size: 1.05em !important;
    margin: 0 !important;
    line-height: 1.2 !important;
}

.redco-card .card-actions {
    gap: 6px !important;
}

.redco-card .card-actions .button {
    padding: 5px 10px !important;
    font-size: 12px !important;
    line-height: 1.2 !important;
}

/* Maximum compact issue items */
.issue-item {
    padding: 10px 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
    display: flex !important;
    align-items: flex-start !important;
    gap: 10px !important;
}

.issue-item:last-child {
    border-bottom: none !important;
}

.issue-item .dashicons {
    font-size: 15px !important;
    margin-top: 1px !important;
}

.issue-content {
    flex: 1 !important;
    min-width: 0 !important;
}

.issue-title {
    font-size: 13px !important;
    font-weight: 600 !important;
    margin-bottom: 2px !important;
    line-height: 1.2 !important;
}

.issue-description {
    font-size: 12px !important;
    color: #666 !important;
    margin-bottom: 4px !important;
    line-height: 1.2 !important;
}

.issue-meta {
    display: flex !important;
    gap: 6px !important;
    align-items: center !important;
    flex-wrap: wrap !important;
}

.issue-meta span {
    font-size: 10px !important;
    padding: 1px 5px !important;
    border-radius: 3px !important;
    line-height: 1.2 !important;
}

.issue-actions-right {
    flex-shrink: 0 !important;
}

.issue-actions-right .button,
.issue-actions-right .action-button {
    padding: 5px 8px !important;
    font-size: 11px !important;
    line-height: 1.2 !important;
}

/* MAXIMUM COMPACT RECENT FIXES SECTION */
.fix-date {
    font-size: 11px !important;
    color: #666 !important;
    line-height: 1.2 !important;
}

.fix-count {
    font-size: 11px !important;
    font-weight: 500 !important;
    line-height: 1.2 !important;
}

.fix-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 4px !important;
}

.fix-item {
    padding: 6px 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

.fix-item:last-child {
    border-bottom: none !important;
}

.fix-backup {
    font-size: 10px !important;
    color: #888 !important;
    line-height: 1.2 !important;
}

/* Maximum compact sidebar */
.redco-content-sidebar {
    width: 260px !important;
    flex-shrink: 0 !important;
}

.redco-content-sidebar .redco-card {
    margin-bottom: 8px !important;
}

.sidebar-stats {
    display: flex !important;
    flex-direction: column !important;
    gap: 6px !important;
}

.sidebar-stat {
    padding: 6px 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

.sidebar-stat:last-child {
    border-bottom: none !important;
}

.sidebar-stat .stat-label {
    font-size: 11px !important;
    color: #666 !important;
    margin-bottom: 2px !important;
    line-height: 1.2 !important;
}

.sidebar-stat .stat-value {
    font-size: 12px !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
}

.sidebar-actions {
    display: flex !important;
    flex-direction: column !important;
    gap: 6px !important;
}

.sidebar-actions .button {
    padding: 6px 10px !important;
    font-size: 12px !important;
    line-height: 1.2 !important;
}

/* Maximum compact emergency controls */
.emergency-controls {
    display: flex !important;
    flex-direction: column !important;
    gap: 6px !important;
}

.emergency-controls .description {
    font-size: 10px !important;
    line-height: 1.2 !important;
    margin-top: 4px !important;
}

/* Maximum compact last scan info */
.last-scan-info {
    margin-top: 8px !important;
    padding-top: 8px !important;
    border-top: 1px solid #f0f0f0 !important;
}

.last-scan-info p {
    font-size: 12px !important;
    margin: 0 !important;
    line-height: 1.2 !important;
}

/* Additional compact optimizations */
.no-issues-found {
    padding: 16px !important;
}

.no-issues-found h4 {
    font-size: 1.1em !important;
    margin-bottom: 6px !important;
    line-height: 1.2 !important;
}

.no-issues-found p {
    font-size: 12px !important;
    line-height: 1.3 !important;
    margin-bottom: 6px !important;
}

.scan-info {
    font-size: 11px !important;
    line-height: 1.2 !important;
}

/* Compact show more issues button */
.show-more-issues {
    padding: 8px 0 !important;
    text-align: center !important;
}

.show-more-issues .button {
    font-size: 11px !important;
    padding: 4px 8px !important;
    line-height: 1.2 !important;
}

/* Compact how to solve tips */
.how-to-solve-tip {
    margin-top: 6px !important;
    padding: 8px !important;
    background: #f8f9fa !important;
    border-radius: 4px !important;
    border-left: 3px solid #4CAF50 !important;
}

.how-to-solve-tip h5 {
    font-size: 12px !important;
    margin-bottom: 4px !important;
    line-height: 1.2 !important;
}

.how-to-solve-tip ol,
.how-to-solve-tip ul {
    margin: 4px 0 !important;
    padding-left: 16px !important;
}

.how-to-solve-tip li {
    font-size: 11px !important;
    line-height: 1.3 !important;
    margin-bottom: 2px !important;
}

.how-to-solve-tip p {
    font-size: 11px !important;
    line-height: 1.3 !important;
    margin: 4px 0 !important;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Placeholder loading animation for recent fixes */
.recent-fixes-placeholder {
    padding: 16px;
}

.placeholder-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding: 8px 0;
}

.placeholder-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: 50%;
    animation: placeholder-shimmer 1.5s infinite;
}

.placeholder-content {
    flex: 1;
}

.placeholder-line {
    height: 12px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: 6px;
    margin-bottom: 6px;
    animation: placeholder-shimmer 1.5s infinite;
}

.placeholder-line.short {
    width: 60%;
}

@keyframes placeholder-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* PERFORMANCE FIX: Loading indicators for metrics */
.loading-metric {
    position: relative;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.loading-metric::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #e0e0e0;
    border-top: 2px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

/* ===== PHASE 1 ENHANCEMENT STYLES ===== */

/* Tiered Fix System */
.redco-tier-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 15px;
}

.redco-tier-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #f9f9f9;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    color: #333;
}

.redco-tier-tab:hover {
    background: #f0f0f0;
    border-color: #999;
}

.redco-tier-tab.active {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.redco-tier-tab .tier-icon {
    font-size: 16px;
}

.redco-tier-tab .tier-name {
    font-weight: 600;
}

.redco-tier-tab .tier-count {
    background: rgba(255,255,255,0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.redco-tier-tab.active .tier-count {
    background: rgba(255,255,255,0.3);
}

/* Category Filters */
.redco-category-filters {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
}

.redco-category-filters label {
    font-weight: 600;
    color: #666;
}

.redco-category-filters select {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

/* Tier Indicators */
.tier-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    color: white;
    z-index: 10;
}

.tier-indicator.tier-safe {
    background: #28a745;
}

.tier-indicator.tier-moderate {
    background: #ffc107;
    color: #333;
}

.tier-indicator.tier-advanced {
    background: #dc3545;
}

/* Safety Indicators */
.safety-indicators {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 10px;
}

.safety-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 3px 8px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 12px;
    font-size: 11px;
    color: #666;
}

.safety-badge.backup-required {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}

.safety-badge.reversible {
    background: #e8f5e8;
    border-color: #4caf50;
    color: #388e3c;
}

.safety-badge.warnings {
    background: #fff3e0;
    border-color: #ff9800;
    color: #f57c00;
}

/* Modal Styles */
.redco-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.redco-modal .modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 800px;
    width: 90%;
    max-height: 90%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.redco-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
    background: #f9f9f9;
}

.redco-modal .modal-header h2 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.redco-modal .modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.redco-modal .modal-close:hover {
    color: #333;
}

.redco-modal .modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.redco-modal .modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    background: #f9f9f9;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Preview System */
.preview-tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.preview-tab {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.preview-tab:hover {
    background: #f0f0f0;
}

.preview-tab.active {
    border-bottom-color: #0073aa;
    color: #0073aa;
    font-weight: 600;
}

.preview-panel {
    display: none;
}

.preview-panel.active {
    display: block;
}

.before-after-comparison {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 20px;
    align-items: center;
}

.comparison-section {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #f9f9f9;
}

.comparison-section h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
}

.comparison-arrow {
    font-size: 24px;
    color: #0073aa;
    font-weight: bold;
}

/* Scheduling System */
.schedule-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.schedule-option {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.schedule-option:hover {
    border-color: #0073aa;
    background: #f8f9fa;
}

.schedule-option input[type="radio"] {
    margin: 0;
    margin-top: 2px;
}

.schedule-option .option-content {
    flex: 1;
}

.schedule-option .option-content strong {
    display: block;
    margin-bottom: 4px;
    color: #333;
}

.schedule-option .option-content small {
    color: #666;
    font-size: 13px;
}

.custom-datetime {
    margin-top: 15px;
}

.custom-datetime input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.notification-settings,
.rollback-settings {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.notification-settings h3,
.rollback-settings h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
}

.notification-settings label,
.rollback-settings label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.notification-settings input[type="email"],
.rollback-settings select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 8px;
}

/* Enhanced Button Styles */
.issue-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.issue-actions .button {
    font-size: 12px;
    padding: 6px 12px;
    height: auto;
    line-height: 1.4;
}

.preview-fix {
    background: #f0f0f0;
    border-color: #ccc;
    color: #333;
}

.preview-fix:hover {
    background: #e0e0e0;
    border-color: #999;
}

.schedule-fix {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.schedule-fix:hover {
    background: #ffeaa7;
    border-color: #ffb300;
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-tier-tabs {
        flex-wrap: wrap;
    }

    .redco-tier-tab {
        flex: 1;
        min-width: 120px;
    }

    .before-after-comparison {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .comparison-arrow {
        text-align: center;
        transform: rotate(90deg);
    }

    .redco-modal .modal-content {
        width: 95%;
        margin: 20px;
    }
}
