/**
 * Phase 3: Conservative JavaScript Enhancements
 * Safe improvements that enhance existing functionality
 * RedCo Optimizer Plugin
 */

(function($) {
    'use strict';

    // Namespace for Phase 3 enhancements
    window.RedcoPhase3 = window.RedcoPhase3 || {};

    /**
     * Enhanced Auto-Save with Visual Feedback
     */
    RedcoPhase3.AutoSave = {
        init: function() {
            this.setupAutoSave();
            this.setupVisualFeedback();
        },

        setupAutoSave: function() {
            let saveTimeout;
            const saveDelay = 2000; // 2 seconds

            $(document).on('change input', 'input[data-auto-save], select[data-auto-save], textarea[data-auto-save]', function() {
                const $field = $(this);
                const $form = $field.closest('form');
                
                clearTimeout(saveTimeout);
                
                // Show saving indicator
                RedcoPhase3.UI.showSaveIndicator($field, 'saving');
                
                saveTimeout = setTimeout(function() {
                    RedcoPhase3.AutoSave.performSave($field, $form);
                }, saveDelay);
            });
        },

        performSave: function($field, $form) {
            const formData = new FormData($form[0]);
            formData.append('action', 'redco_auto_save_setting');
            formData.append('nonce', redcoAjax.nonce);

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        RedcoPhase3.UI.showSaveIndicator($field, 'success');
                    } else {
                        RedcoPhase3.UI.showSaveIndicator($field, 'error');
                    }
                },
                error: function() {
                    RedcoPhase3.UI.showSaveIndicator($field, 'error');
                }
            });
        },

        setupVisualFeedback: function() {
            // Add save indicators to auto-save fields
            $('input[data-auto-save], select[data-auto-save], textarea[data-auto-save]').each(function() {
                const $field = $(this);
                if (!$field.siblings('.save-indicator').length) {
                    $field.after('<span class="save-indicator"></span>');
                }
            });
        }
    };

    /**
     * Enhanced UI Helpers
     */
    RedcoPhase3.UI = {
        showSaveIndicator: function($field, state) {
            const $indicator = $field.siblings('.save-indicator');
            
            $indicator.removeClass('saving success error').addClass(state);
            
            let content = '';
            switch(state) {
                case 'saving':
                    content = '<span class="dashicons dashicons-update-alt"></span> Saving...';
                    break;
                case 'success':
                    content = '<span class="dashicons dashicons-yes-alt"></span> Saved';
                    break;
                case 'error':
                    content = '<span class="dashicons dashicons-warning"></span> Error';
                    break;
            }
            
            $indicator.html(content);
            
            if (state === 'success') {
                setTimeout(function() {
                    $indicator.fadeOut();
                }, 2000);
            }
        },

        showNotification: function(message, type) {
            // CRITICAL SECURITY FIX: Safely create notification to prevent XSS
            const $notification = $('<div class="redco-notification"></div>');
            $notification.addClass(type);
            $notification.text(message); // Use .text() to prevent XSS

            $('body').append($notification);
            
            setTimeout(function() {
                $notification.addClass('show');
            }, 100);
            
            setTimeout(function() {
                $notification.removeClass('show');
                setTimeout(function() {
                    $notification.remove();
                }, 300);
            }, 3000);
        },

        addLoadingState: function($element) {
            if (!$element.find('.loading-overlay').length) {
                $element.css('position', 'relative').append(
                    '<div class="loading-overlay"><div class="loading-spinner"></div></div>'
                );
            }
        },

        removeLoadingState: function($element) {
            $element.find('.loading-overlay').remove();
        }
    };

    /**
     * Enhanced Form Validation
     */
    RedcoPhase3.Validation = {
        init: function() {
            this.setupRealTimeValidation();
            this.setupFormSubmission();
        },

        setupRealTimeValidation: function() {
            $(document).on('blur', 'input[required], select[required], textarea[required]', function() {
                RedcoPhase3.Validation.validateField($(this));
            });

            $(document).on('input', 'input[type="email"], input[type="url"], input[type="number"]', function() {
                RedcoPhase3.Validation.validateField($(this));
            });
        },

        validateField: function($field) {
            const value = $field.val();
            const type = $field.attr('type');
            const required = $field.prop('required');
            
            let isValid = true;
            let message = '';
            
            if (required && !value) {
                isValid = false;
                message = 'This field is required';
            } else if (type === 'email' && value && !this.isValidEmail(value)) {
                isValid = false;
                message = 'Please enter a valid email address';
            } else if (type === 'url' && value && !this.isValidUrl(value)) {
                isValid = false;
                message = 'Please enter a valid URL';
            } else if (type === 'number' && value) {
                const min = parseFloat($field.attr('min'));
                const max = parseFloat($field.attr('max'));
                const numValue = parseFloat(value);
                
                if (isNaN(numValue)) {
                    isValid = false;
                    message = 'Please enter a valid number';
                } else if (!isNaN(min) && numValue < min) {
                    isValid = false;
                    message = 'Value must be at least ' + min;
                } else if (!isNaN(max) && numValue > max) {
                    isValid = false;
                    message = 'Value must be no more than ' + max;
                }
            }
            
            this.updateFieldValidation($field, isValid, message);
            return isValid;
        },

        updateFieldValidation: function($field, isValid, message) {
            $field.removeClass('form-field-error form-field-success');
            $field.siblings('.field-feedback').remove();
            
            if (!isValid) {
                $field.addClass('form-field-error');
                if (message) {
                    $field.after('<div class="field-feedback error">' + message + '</div>');
                }
            } else if ($field.val()) {
                $field.addClass('form-field-success');
            }
        },

        setupFormSubmission: function() {
            $(document).on('submit', 'form[data-validate]', function(e) {
                const $form = $(this);
                let isValid = true;
                
                $form.find('input[required], select[required], textarea[required]').each(function() {
                    if (!RedcoPhase3.Validation.validateField($(this))) {
                        isValid = false;
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    RedcoPhase3.UI.showNotification('Please fix the errors before submitting', 'error');
                    return false;
                }
            });
        },

        isValidEmail: function(email) {
            const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        },

        isValidUrl: function(url) {
            try {
                new URL(url);
                return true;
            } catch {
                return false;
            }
        }
    };

    /**
     * Enhanced Accessibility
     */
    RedcoPhase3.Accessibility = {
        init: function() {
            this.setupKeyboardNavigation();
            this.setupSkipLinks();
            this.setupFocusManagement();
        },

        setupKeyboardNavigation: function() {
            // Enhanced keyboard navigation for custom elements
            $(document).on('keydown', '.redco-btn, .module-toggle', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    $(this).click();
                }
            });
        },

        setupSkipLinks: function() {
            // Add skip link if it doesn't exist
            if (!$('.skip-link').length) {
                $('body').prepend('<a href="#main-content" class="skip-link">Skip to main content</a>');
            }

            $('.skip-link').on('click', function(e) {
                e.preventDefault();
                const target = $($(this).attr('href'));
                if (target.length) {
                    target.focus();
                }
            });
        },

        setupFocusManagement: function() {
            // Improve focus management for modals and dynamic content
            $(document).on('shown.bs.modal', '.modal', function() {
                $(this).find('input, button, select, textarea').first().focus();
            });

            // Trap focus in modals
            $(document).on('keydown', '.modal', function(e) {
                if (e.key === 'Tab') {
                    const $modal = $(this);
                    const $focusable = $modal.find('input, button, select, textarea, a[href]').filter(':visible');
                    const $first = $focusable.first();
                    const $last = $focusable.last();

                    if (e.shiftKey && document.activeElement === $first[0]) {
                        e.preventDefault();
                        $last.focus();
                    } else if (!e.shiftKey && document.activeElement === $last[0]) {
                        e.preventDefault();
                        $first.focus();
                    }
                }
            });
        }
    };

    /**
     * Enhanced Statistics Display
     */
    RedcoPhase3.Stats = {
        init: function() {
            this.setupStatUpdates();
        },

        setupStatUpdates: function() {
            // Add animation to stat updates
            $(document).on('redco:stats:updated', function(e, stats) {
                $.each(stats, function(key, value) {
                    const $statElement = $('[data-stat="' + key + '"]');
                    if ($statElement.length) {
                        $statElement.addClass('stat-updated');
                        setTimeout(function() {
                            $statElement.removeClass('stat-updated');
                        }, 1000);
                    }
                });
            });
        }
    };

    /**
     * Initialize all enhancements when document is ready
     */
    $(document).ready(function() {
        // Only initialize if we're on a RedCo Optimizer page
        if ($('.redco-optimizer-admin, .redco-module-tab').length) {
            RedcoPhase3.AutoSave.init();
            RedcoPhase3.Validation.init();
            RedcoPhase3.Accessibility.init();
            RedcoPhase3.Stats.init();
        }
    });

})(jQuery);
