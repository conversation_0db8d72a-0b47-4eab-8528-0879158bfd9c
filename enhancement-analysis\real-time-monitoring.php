<?php
/**
 * Real-Time Monitoring & Alerting System for Redco Optimizer
 * 
 * Continuous performance monitoring with intelligent alerting
 */

class Redco_Real_Time_Monitor {
    
    private $monitoring_intervals = array(
        'critical' => 300,    // 5 minutes
        'high' => 900,        // 15 minutes
        'medium' => 1800,     // 30 minutes
        'low' => 3600         // 1 hour
    );
    
    private $alert_thresholds = array(
        'page_load_time' => array('warning' => 3.0, 'critical' => 5.0),
        'ttfb' => array('warning' => 600, 'critical' => 1000),
        'error_rate' => array('warning' => 0.01, 'critical' => 0.05),
        'uptime' => array('warning' => 0.99, 'critical' => 0.95),
        'core_web_vitals' => array(
            'lcp' => array('warning' => 2.5, 'critical' => 4.0),
            'fid' => array('warning' => 100, 'critical' => 300),
            'cls' => array('warning' => 0.1, 'critical' => 0.25)
        )
    );
    
    /**
     * Initialize real-time monitoring
     */
    public function init_monitoring() {
        // Schedule monitoring tasks
        if (!wp_next_scheduled('redco_real_time_monitor')) {
            wp_schedule_event(time(), 'redco_5min', 'redco_real_time_monitor');
        }
        
        // Register custom cron interval
        add_filter('cron_schedules', array($this, 'add_cron_intervals'));
        
        // Hook monitoring functions
        add_action('redco_real_time_monitor', array($this, 'run_monitoring_cycle'));
        add_action('redco_performance_alert', array($this, 'send_performance_alert'), 10, 2);
        
        // AJAX endpoints for real-time dashboard
        add_action('wp_ajax_redco_get_live_metrics', array($this, 'ajax_get_live_metrics'));
        add_action('wp_ajax_redco_get_alert_history', array($this, 'ajax_get_alert_history'));
    }
    
    /**
     * Add custom cron intervals
     */
    public function add_cron_intervals($schedules) {
        $schedules['redco_5min'] = array(
            'interval' => 300,
            'display' => __('Every 5 Minutes', 'redco-optimizer')
        );
        
        return $schedules;
    }
    
    /**
     * Run monitoring cycle
     */
    public function run_monitoring_cycle() {
        $start_time = microtime(true);
        
        // Get monitoring configuration
        $config = get_option('redco_monitoring_config', $this->get_default_monitoring_config());
        
        if (!$config['enabled']) {
            return;
        }
        
        // Collect metrics
        $metrics = $this->collect_real_time_metrics();
        
        // Store metrics
        $this->store_metrics($metrics);
        
        // Check thresholds and trigger alerts
        $this->check_alert_thresholds($metrics);
        
        // Update monitoring status
        $this->update_monitoring_status($start_time);
        
        // Cleanup old data
        $this->cleanup_old_metrics();
    }
    
    /**
     * Collect real-time performance metrics
     */
    private function collect_real_time_metrics() {
        $metrics = array(
            'timestamp' => time(),
            'page_load_time' => $this->measure_page_load_time(),
            'ttfb' => $this->measure_ttfb(),
            'memory_usage' => $this->get_memory_usage(),
            'database_performance' => $this->measure_database_performance(),
            'error_rate' => $this->calculate_error_rate(),
            'uptime_status' => $this->check_uptime_status(),
            'core_web_vitals' => $this->measure_core_web_vitals(),
            'server_metrics' => $this->collect_server_metrics(),
            'user_experience' => $this->collect_user_experience_metrics()
        );
        
        return $metrics;
    }
    
    /**
     * Measure page load time for key pages
     */
    private function measure_page_load_time() {
        $key_pages = array(
            'homepage' => home_url(),
            'admin' => admin_url(),
            'sample_post' => $this->get_sample_post_url()
        );
        
        $load_times = array();
        
        foreach ($key_pages as $page_type => $url) {
            $start_time = microtime(true);
            
            $response = wp_remote_get($url, array(
                'timeout' => 10,
                'user-agent' => 'Redco Monitor Bot',
                'sslverify' => false
            ));
            
            $end_time = microtime(true);
            
            if (!is_wp_error($response)) {
                $load_times[$page_type] = round(($end_time - $start_time) * 1000); // milliseconds
            } else {
                $load_times[$page_type] = null;
            }
        }
        
        return $load_times;
    }
    
    /**
     * Measure Time to First Byte
     */
    private function measure_ttfb() {
        $url = home_url();
        $start_time = microtime(true);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        curl_exec($ch);
        $ttfb = curl_getinfo($ch, CURLINFO_STARTTRANSFER_TIME);
        curl_close($ch);
        
        return round($ttfb * 1000); // Convert to milliseconds
    }
    
    /**
     * Get current memory usage
     */
    private function get_memory_usage() {
        return array(
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => $this->parse_memory_limit(ini_get('memory_limit')),
            'percentage' => round((memory_get_usage(true) / $this->parse_memory_limit(ini_get('memory_limit'))) * 100, 2)
        );
    }
    
    /**
     * Measure database performance
     */
    private function measure_database_performance() {
        global $wpdb;
        
        $start_time = microtime(true);
        
        // Test query
        $wpdb->get_results("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'publish'");
        
        $query_time = microtime(true) - $start_time;
        
        return array(
            'query_time' => round($query_time * 1000, 2), // milliseconds
            'queries_count' => $wpdb->num_queries,
            'slow_queries' => $this->detect_slow_queries()
        );
    }
    
    /**
     * Calculate error rate from logs
     */
    private function calculate_error_rate() {
        // This would analyze error logs or use a logging system
        // For now, return a mock calculation
        
        $error_log_path = ini_get('error_log');
        if (!$error_log_path || !file_exists($error_log_path)) {
            return 0;
        }
        
        // Count recent errors (last hour)
        $recent_errors = 0;
        $total_requests = 100; // This would come from access logs
        
        return $total_requests > 0 ? $recent_errors / $total_requests : 0;
    }
    
    /**
     * Check uptime status
     */
    private function check_uptime_status() {
        $response = wp_remote_head(home_url(), array(
            'timeout' => 5,
            'sslverify' => false
        ));
        
        return array(
            'status' => !is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200,
            'response_code' => is_wp_error($response) ? 0 : wp_remote_retrieve_response_code($response),
            'response_time' => $this->measure_ttfb()
        );
    }
    
    /**
     * Measure Core Web Vitals in real-time
     */
    private function measure_core_web_vitals() {
        // This would use Real User Monitoring (RUM) data
        // For now, return estimated values based on current performance
        
        $page_load = $this->measure_page_load_time();
        $avg_load = array_sum(array_filter($page_load)) / count(array_filter($page_load));
        
        return array(
            'lcp' => $avg_load * 0.7, // Estimated LCP
            'fid' => min(100, $avg_load * 0.1), // Estimated FID
            'cls' => 0.05, // Would need real measurement
            'measurement_method' => 'estimated'
        );
    }
    
    /**
     * Collect server metrics
     */
    private function collect_server_metrics() {
        return array(
            'php_version' => PHP_VERSION,
            'server_load' => function_exists('sys_getloadavg') ? sys_getloadavg()[0] : null,
            'disk_usage' => $this->get_disk_usage(),
            'active_plugins' => count(get_option('active_plugins', array())),
            'theme' => get_template()
        );
    }
    
    /**
     * Check alert thresholds and trigger alerts
     */
    private function check_alert_thresholds($metrics) {
        $alerts = array();
        
        // Check page load time
        $avg_load_time = array_sum(array_filter($metrics['page_load_time'])) / count(array_filter($metrics['page_load_time']));
        if ($avg_load_time > $this->alert_thresholds['page_load_time']['critical'] * 1000) {
            $alerts[] = array(
                'type' => 'critical',
                'metric' => 'page_load_time',
                'value' => $avg_load_time,
                'threshold' => $this->alert_thresholds['page_load_time']['critical'] * 1000,
                'message' => sprintf('Page load time is %dms (threshold: %dms)', $avg_load_time, $this->alert_thresholds['page_load_time']['critical'] * 1000)
            );
        }
        
        // Check TTFB
        if ($metrics['ttfb'] > $this->alert_thresholds['ttfb']['critical']) {
            $alerts[] = array(
                'type' => 'critical',
                'metric' => 'ttfb',
                'value' => $metrics['ttfb'],
                'threshold' => $this->alert_thresholds['ttfb']['critical'],
                'message' => sprintf('TTFB is %dms (threshold: %dms)', $metrics['ttfb'], $this->alert_thresholds['ttfb']['critical'])
            );
        }
        
        // Check memory usage
        if ($metrics['memory_usage']['percentage'] > 90) {
            $alerts[] = array(
                'type' => 'warning',
                'metric' => 'memory_usage',
                'value' => $metrics['memory_usage']['percentage'],
                'threshold' => 90,
                'message' => sprintf('Memory usage is %d%% (threshold: 90%%)', $metrics['memory_usage']['percentage'])
            );
        }
        
        // Check uptime
        if (!$metrics['uptime_status']['status']) {
            $alerts[] = array(
                'type' => 'critical',
                'metric' => 'uptime',
                'value' => 0,
                'threshold' => 1,
                'message' => 'Site is down or unreachable'
            );
        }
        
        // Process alerts
        foreach ($alerts as $alert) {
            $this->process_alert($alert);
        }
    }
    
    /**
     * Process and send alerts
     */
    private function process_alert($alert) {
        // Store alert in database
        $this->store_alert($alert);
        
        // Check if we should send notification (avoid spam)
        if ($this->should_send_alert_notification($alert)) {
            do_action('redco_performance_alert', $alert, $this->get_alert_context());
        }
    }
    
    /**
     * Send performance alert
     */
    public function send_performance_alert($alert, $context) {
        $config = get_option('redco_monitoring_config');
        
        if (!$config['alerts']['enabled']) {
            return;
        }
        
        // Email notification
        if ($config['alerts']['email']['enabled']) {
            $this->send_email_alert($alert, $context);
        }
        
        // Slack notification
        if ($config['alerts']['slack']['enabled']) {
            $this->send_slack_alert($alert, $context);
        }
        
        // Webhook notification
        if ($config['alerts']['webhook']['enabled']) {
            $this->send_webhook_alert($alert, $context);
        }
    }
    
    /**
     * AJAX: Get live metrics for dashboard
     */
    public function ajax_get_live_metrics() {
        check_ajax_referer('redco_monitoring_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $metrics = $this->get_latest_metrics();
        
        wp_send_json_success($metrics);
    }
    
    /**
     * Get default monitoring configuration
     */
    private function get_default_monitoring_config() {
        return array(
            'enabled' => true,
            'monitoring_interval' => 300, // 5 minutes
            'metrics_retention_days' => 30,
            'alerts' => array(
                'enabled' => true,
                'email' => array(
                    'enabled' => true,
                    'recipients' => array(get_option('admin_email'))
                ),
                'slack' => array(
                    'enabled' => false,
                    'webhook_url' => ''
                ),
                'webhook' => array(
                    'enabled' => false,
                    'url' => ''
                )
            ),
            'thresholds' => $this->alert_thresholds
        );
    }
}
