<?php
/**
 * Comprehensive Performance Audit Tool for Redco Optimizer
 * 
 * This tool performs a detailed analysis of the current configuration
 * and provides specific recommendations for improving PageSpeed scores.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Performance_Audit {
    
    private $audit_results = array();
    private $recommendations = array();
    private $current_scores = array();
    
    /**
     * Run comprehensive performance audit
     */
    public function run_audit() {
        echo "<div class='redco-performance-audit'>";
        echo "<h1>🔍 Redco Optimizer Performance Audit</h1>";
        
        // 1. Current Configuration Analysis
        $this->analyze_current_configuration();
        
        // 2. Module Effectiveness Analysis
        $this->analyze_module_effectiveness();
        
        // 3. PageSpeed Insights Analysis
        $this->analyze_pagespeed_scores();
        
        // 4. Core Web Vitals Analysis
        $this->analyze_core_web_vitals();
        
        // 5. Conflict Detection
        $this->detect_conflicts();
        
        // 6. Generate Recommendations
        $this->generate_recommendations();
        
        // 7. Display Results
        $this->display_audit_results();
        
        echo "</div>";
    }
    
    /**
     * Analyze current configuration
     */
    private function analyze_current_configuration() {
        echo "<h2>📊 Current Configuration Analysis</h2>";
        
        // Get enabled modules
        $options = get_option('redco_optimizer_options', array());
        $enabled_modules = isset($options['modules_enabled']) ? $options['modules_enabled'] : array();
        
        echo "<h3>Enabled Modules (" . count($enabled_modules) . "):</h3>";
        echo "<ul>";
        foreach ($enabled_modules as $module) {
            $module_name = ucwords(str_replace('-', ' ', $module));
            echo "<li>✅ {$module_name}</li>";
        }
        echo "</ul>";
        
        // Check critical modules
        $critical_modules = array(
            'page-cache' => 'Page Cache',
            'css-js-minifier' => 'CSS/JS Minifier',
            'lazy-load' => 'Lazy Load Images',
            'critical-resource-optimizer' => 'Critical Resource Optimizer',
            'smart-webp-conversion' => 'Smart WebP Conversion'
        );
        
        echo "<h3>Critical Module Status:</h3>";
        foreach ($critical_modules as $module_key => $module_name) {
            $status = in_array($module_key, $enabled_modules) ? "✅ ENABLED" : "❌ DISABLED";
            $impact = in_array($module_key, $enabled_modules) ? "" : " (High Impact on PageSpeed)";
            echo "<p><strong>{$module_name}:</strong> {$status}{$impact}</p>";
        }
        
        // Analyze module settings
        $this->analyze_module_settings($enabled_modules);
    }
    
    /**
     * Analyze module settings for optimization
     */
    private function analyze_module_settings($enabled_modules) {
        echo "<h3>Module Configuration Analysis:</h3>";
        
        foreach ($enabled_modules as $module) {
            $settings = get_option("redco_optimizer_{$module}", array());
            
            switch ($module) {
                case 'page-cache':
                    $expiration = $settings['expiration'] ?? 21600;
                    if ($expiration < 3600) {
                        $this->recommendations[] = array(
                            'priority' => 'high',
                            'module' => 'Page Cache',
                            'issue' => 'Cache expiration too short',
                            'recommendation' => 'Increase cache expiration to at least 1 hour (3600 seconds)',
                            'current' => $expiration . ' seconds',
                            'suggested' => '21600 seconds (6 hours)'
                        );
                    }
                    break;
                    
                case 'css-js-minifier':
                    $minify_css = $settings['minify_css'] ?? true;
                    $minify_js = $settings['minify_js'] ?? true;
                    if (!$minify_css || !$minify_js) {
                        $this->recommendations[] = array(
                            'priority' => 'high',
                            'module' => 'CSS/JS Minifier',
                            'issue' => 'Minification not fully enabled',
                            'recommendation' => 'Enable both CSS and JS minification for maximum impact',
                            'current' => 'CSS: ' . ($minify_css ? 'enabled' : 'disabled') . ', JS: ' . ($minify_js ? 'enabled' : 'disabled'),
                            'suggested' => 'Enable both CSS and JS minification'
                        );
                    }
                    break;
                    
                case 'lazy-load':
                    $threshold = $settings['threshold'] ?? 200;
                    if ($threshold > 300) {
                        $this->recommendations[] = array(
                            'priority' => 'medium',
                            'module' => 'Lazy Load',
                            'issue' => 'Lazy load threshold too high',
                            'recommendation' => 'Reduce threshold to 200px for better LCP scores',
                            'current' => $threshold . 'px',
                            'suggested' => '200px'
                        );
                    }
                    break;
            }
        }
    }
    
    /**
     * Analyze module effectiveness
     */
    private function analyze_module_effectiveness() {
        echo "<h2>⚡ Module Effectiveness Analysis</h2>";
        
        // Check if modules are actually working
        $effectiveness_tests = array(
            'page-cache' => $this->test_page_cache_effectiveness(),
            'css-js-minifier' => $this->test_minification_effectiveness(),
            'lazy-load' => $this->test_lazy_load_effectiveness(),
            'smart-webp-conversion' => $this->test_webp_effectiveness()
        );
        
        foreach ($effectiveness_tests as $module => $result) {
            $status = $result['working'] ? "✅ WORKING" : "❌ NOT WORKING";
            $module_name = ucwords(str_replace('-', ' ', $module));
            echo "<p><strong>{$module_name}:</strong> {$status}</p>";
            
            if (!$result['working'] && !empty($result['issue'])) {
                echo "<p style='color: #d63638; margin-left: 20px;'>Issue: {$result['issue']}</p>";
                
                $this->recommendations[] = array(
                    'priority' => 'critical',
                    'module' => $module_name,
                    'issue' => $result['issue'],
                    'recommendation' => $result['fix'] ?? 'Check module configuration and re-enable',
                    'current' => 'Not functioning',
                    'suggested' => 'Fix and verify functionality'
                );
            }
        }
    }
    
    /**
     * Test page cache effectiveness
     */
    private function test_page_cache_effectiveness() {
        if (!redco_is_module_enabled('page-cache')) {
            return array('working' => false, 'issue' => 'Module not enabled');
        }
        
        // Check if cache directory exists and is writable
        $cache_dir = redco_get_cache_dir();
        if (!is_dir($cache_dir) || !is_writable($cache_dir)) {
            return array(
                'working' => false, 
                'issue' => 'Cache directory not writable',
                'fix' => 'Check file permissions for cache directory'
            );
        }
        
        // Check if cache files exist
        $cache_files = glob($cache_dir . '*.html');
        if (empty($cache_files)) {
            return array(
                'working' => false, 
                'issue' => 'No cache files found',
                'fix' => 'Visit your website to generate cache files'
            );
        }
        
        return array('working' => true);
    }
    
    /**
     * Test minification effectiveness
     */
    private function test_minification_effectiveness() {
        if (!redco_is_module_enabled('css-js-minifier')) {
            return array('working' => false, 'issue' => 'Module not enabled');
        }
        
        // Check if minified files exist
        $cache_dir = redco_get_cache_dir() . 'minified/';
        if (!is_dir($cache_dir)) {
            return array(
                'working' => false, 
                'issue' => 'Minified cache directory not found',
                'fix' => 'Visit your website to generate minified files'
            );
        }
        
        $minified_files = array_merge(
            glob($cache_dir . '*.css'),
            glob($cache_dir . '*.js')
        );
        
        if (empty($minified_files)) {
            return array(
                'working' => false, 
                'issue' => 'No minified files found',
                'fix' => 'Clear cache and visit your website to generate minified files'
            );
        }
        
        return array('working' => true);
    }
    
    /**
     * Test lazy load effectiveness
     */
    private function test_lazy_load_effectiveness() {
        if (!redco_is_module_enabled('lazy-load')) {
            return array('working' => false, 'issue' => 'Module not enabled');
        }
        
        // This would require frontend testing - assume working if enabled
        return array('working' => true);
    }
    
    /**
     * Test WebP effectiveness
     */
    private function test_webp_effectiveness() {
        if (!redco_is_module_enabled('smart-webp-conversion')) {
            return array('working' => false, 'issue' => 'Module not enabled');
        }
        
        // Check server WebP support
        if (!function_exists('imagewebp')) {
            return array(
                'working' => false, 
                'issue' => 'Server does not support WebP',
                'fix' => 'Contact hosting provider to enable GD library with WebP support'
            );
        }
        
        return array('working' => true);
    }
    
    /**
     * Analyze PageSpeed scores
     */
    private function analyze_pagespeed_scores() {
        echo "<h2>📈 PageSpeed Insights Analysis</h2>";
        
        // Check if API key is configured
        $performance_options = get_option('redco_optimizer_performance', array());
        $api_key = isset($performance_options['pagespeed_api_key']) ? $performance_options['pagespeed_api_key'] : '';
        
        if (empty($api_key)) {
            echo "<p style='color: #d63638;'>❌ PageSpeed Insights API key not configured</p>";
            echo "<p>Configure your API key to get real PageSpeed scores and detailed recommendations.</p>";
            
            $this->recommendations[] = array(
                'priority' => 'high',
                'module' => 'PageSpeed API',
                'issue' => 'API key not configured',
                'recommendation' => 'Add Google PageSpeed Insights API key for accurate scoring',
                'current' => 'No API key',
                'suggested' => 'Get free API key from Google Cloud Console'
            );
            return;
        }
        
        echo "<p>✅ API key configured - Running PageSpeed tests...</p>";
        
        // Test both mobile and desktop
        $this->test_pagespeed_scores($api_key);
    }
    
    /**
     * Test PageSpeed scores
     */
    private function test_pagespeed_scores($api_key) {
        $site_url = home_url();
        $strategies = array('mobile', 'desktop');
        
        foreach ($strategies as $strategy) {
            echo "<h3>" . ucfirst($strategy) . " PageSpeed Score:</h3>";
            
            $api_url = "https://www.googleapis.com/pagespeedonline/v5/runPagespeed?url=" . urlencode($site_url) . "&key=" . urlencode($api_key) . "&strategy=" . $strategy . "&category=performance";
            
            $response = wp_remote_get($api_url, array('timeout' => 60));
            
            if (is_wp_error($response)) {
                echo "<p style='color: #d63638;'>❌ Failed to get {$strategy} score: " . $response->get_error_message() . "</p>";
                continue;
            }
            
            $data = json_decode(wp_remote_retrieve_body($response), true);
            
            if (!isset($data['lighthouseResult']['categories']['performance']['score'])) {
                echo "<p style='color: #d63638;'>❌ Invalid response from PageSpeed API</p>";
                continue;
            }
            
            $score = round($data['lighthouseResult']['categories']['performance']['score'] * 100);
            $this->current_scores[$strategy] = $score;
            
            $color = $score >= 90 ? '#00a32a' : ($score >= 50 ? '#dba617' : '#d63638');
            echo "<p style='color: {$color}; font-size: 18px; font-weight: bold;'>{$score}/100</p>";
            
            // Analyze score and add recommendations
            if ($score < 90) {
                $this->analyze_pagespeed_opportunities($data, $strategy);
            }
        }
    }
    
    /**
     * Analyze PageSpeed opportunities
     */
    private function analyze_pagespeed_opportunities($data, $strategy) {
        if (!isset($data['lighthouseResult']['audits'])) {
            return;
        }
        
        $audits = $data['lighthouseResult']['audits'];
        $opportunities = array();
        
        // Key opportunities that Redco can address
        $redco_opportunities = array(
            'unused-css-rules' => 'CSS/JS Minifier can help remove unused CSS',
            'render-blocking-resources' => 'Critical Resource Optimizer can fix this',
            'unminified-css' => 'Enable CSS minification in CSS/JS Minifier module',
            'unminified-javascript' => 'Enable JS minification in CSS/JS Minifier module',
            'offscreen-images' => 'Lazy Load Images module can fix this',
            'uses-webp-images' => 'Smart WebP Conversion module can fix this',
            'efficient-animated-content' => 'Smart WebP Conversion can convert GIFs to WebP',
            'uses-optimized-images' => 'Smart WebP Conversion can optimize images'
        );
        
        foreach ($redco_opportunities as $audit_key => $solution) {
            if (isset($audits[$audit_key]) && isset($audits[$audit_key]['score']) && $audits[$audit_key]['score'] < 1) {
                $potential_savings = isset($audits[$audit_key]['details']['overallSavingsMs']) ? 
                    round($audits[$audit_key]['details']['overallSavingsMs'] / 1000, 2) : 0;
                
                $opportunities[] = array(
                    'audit' => $audit_key,
                    'title' => $audits[$audit_key]['title'] ?? $audit_key,
                    'solution' => $solution,
                    'savings' => $potential_savings
                );
            }
        }
        
        if (!empty($opportunities)) {
            echo "<h4>{$strategy} Optimization Opportunities:</h4>";
            foreach ($opportunities as $opp) {
                echo "<p>• <strong>{$opp['title']}</strong>";
                if ($opp['savings'] > 0) {
                    echo " (Potential savings: {$opp['savings']}s)";
                }
                echo "<br>&nbsp;&nbsp;Solution: {$opp['solution']}</p>";
                
                $this->recommendations[] = array(
                    'priority' => $opp['savings'] > 1 ? 'high' : 'medium',
                    'module' => 'PageSpeed Optimization',
                    'issue' => $opp['title'],
                    'recommendation' => $opp['solution'],
                    'current' => 'Not optimized',
                    'suggested' => 'Apply recommended solution',
                    'potential_savings' => $opp['savings'] . 's'
                );
            }
        }
    }

    /**
     * Analyze Core Web Vitals
     */
    private function analyze_core_web_vitals() {
        echo "<h2>🎯 Core Web Vitals Analysis</h2>";

        // This would require real Core Web Vitals data
        // For now, provide recommendations based on enabled modules

        $cwv_recommendations = array(
            'LCP (Largest Contentful Paint)' => array(
                'modules' => array('page-cache', 'critical-resource-optimizer', 'smart-webp-conversion'),
                'tips' => array(
                    'Enable page caching for faster server response',
                    'Optimize critical resources to load above-the-fold content faster',
                    'Convert images to WebP for faster loading'
                )
            ),
            'FID (First Input Delay)' => array(
                'modules' => array('css-js-minifier', 'critical-resource-optimizer'),
                'tips' => array(
                    'Minify JavaScript to reduce execution time',
                    'Defer non-critical JavaScript',
                    'Remove unused JavaScript'
                )
            ),
            'CLS (Cumulative Layout Shift)' => array(
                'modules' => array('lazy-load'),
                'tips' => array(
                    'Set image dimensions to prevent layout shifts',
                    'Use proper lazy loading with placeholders',
                    'Avoid inserting content above existing content'
                )
            )
        );

        foreach ($cwv_recommendations as $metric => $data) {
            echo "<h3>{$metric}:</h3>";

            $enabled_modules = get_option('redco_optimizer_options', array())['modules_enabled'] ?? array();
            $relevant_enabled = array_intersect($data['modules'], $enabled_modules);

            echo "<p>Relevant modules enabled: " . count($relevant_enabled) . "/" . count($data['modules']) . "</p>";

            foreach ($data['tips'] as $tip) {
                echo "<p>• {$tip}</p>";
            }
        }
    }

    /**
     * Detect conflicts
     */
    private function detect_conflicts() {
        echo "<h2>⚠️ Conflict Detection</h2>";

        // Check for common plugin conflicts
        $active_plugins = get_option('active_plugins', array());
        $conflicting_plugins = array(
            'w3-total-cache/w3-total-cache.php' => 'W3 Total Cache (may conflict with Page Cache)',
            'wp-super-cache/wp-cache.php' => 'WP Super Cache (may conflict with Page Cache)',
            'wp-rocket/wp-rocket.php' => 'WP Rocket (may conflict with multiple modules)',
            'autoptimize/autoptimize.php' => 'Autoptimize (may conflict with CSS/JS Minifier)',
            'wp-smushit/wp-smush.php' => 'Smush (may conflict with WebP Conversion)',
            'shortpixel-image-optimiser/wp-shortpixel.php' => 'ShortPixel (may conflict with WebP Conversion)'
        );

        $conflicts_found = false;
        foreach ($conflicting_plugins as $plugin_path => $description) {
            if (in_array($plugin_path, $active_plugins)) {
                echo "<p style='color: #d63638;'>❌ {$description}</p>";
                $conflicts_found = true;

                $this->recommendations[] = array(
                    'priority' => 'high',
                    'module' => 'Plugin Conflicts',
                    'issue' => "Conflicting plugin detected: {$description}",
                    'recommendation' => 'Disable conflicting plugin or configure to avoid conflicts',
                    'current' => 'Plugin active',
                    'suggested' => 'Deactivate or configure properly'
                );
            }
        }

        if (!$conflicts_found) {
            echo "<p style='color: #00a32a;'>✅ No major plugin conflicts detected</p>";
        }

        // Check theme conflicts
        $theme = wp_get_theme();
        echo "<p>Active theme: {$theme->get('Name')} v{$theme->get('Version')}</p>";

        // Check for common theme issues
        if (strpos(strtolower($theme->get('Name')), 'divi') !== false) {
            echo "<p style='color: #dba617;'>⚠️ Divi theme detected - may have built-in optimization that conflicts</p>";
        }
    }

    /**
     * Generate comprehensive recommendations
     */
    private function generate_recommendations() {
        // Sort recommendations by priority
        usort($this->recommendations, function($a, $b) {
            $priority_order = array('critical' => 1, 'high' => 2, 'medium' => 3, 'low' => 4);
            return $priority_order[$a['priority']] - $priority_order[$b['priority']];
        });
    }

    /**
     * Display audit results and recommendations
     */
    private function display_audit_results() {
        echo "<h2>📋 Audit Results & Recommendations</h2>";

        if (empty($this->recommendations)) {
            echo "<p style='color: #00a32a; font-size: 18px;'>🎉 Excellent! No critical issues found. Your Redco Optimizer is well configured.</p>";
            return;
        }

        echo "<h3>Priority Recommendations:</h3>";

        $priority_colors = array(
            'critical' => '#d63638',
            'high' => '#dba617',
            'medium' => '#00a32a',
            'low' => '#666'
        );

        foreach ($this->recommendations as $rec) {
            $color = $priority_colors[$rec['priority']];
            $priority_label = strtoupper($rec['priority']);

            echo "<div style='border-left: 4px solid {$color}; padding: 15px; margin: 10px 0; background: #f9f9f9;'>";
            echo "<h4 style='color: {$color}; margin: 0 0 10px 0;'>[{$priority_label}] {$rec['module']}</h4>";
            echo "<p><strong>Issue:</strong> {$rec['issue']}</p>";
            echo "<p><strong>Recommendation:</strong> {$rec['recommendation']}</p>";
            echo "<p><strong>Current:</strong> {$rec['current']}</p>";
            echo "<p><strong>Suggested:</strong> {$rec['suggested']}</p>";

            if (isset($rec['potential_savings'])) {
                echo "<p><strong>Potential Savings:</strong> {$rec['potential_savings']}</p>";
            }
            echo "</div>";
        }

        // Display expected improvements
        $this->display_expected_improvements();
    }

    /**
     * Display expected improvements
     */
    private function display_expected_improvements() {
        echo "<h3>📈 Expected Improvements</h3>";

        $critical_count = count(array_filter($this->recommendations, function($r) { return $r['priority'] === 'critical'; }));
        $high_count = count(array_filter($this->recommendations, function($r) { return $r['priority'] === 'high'; }));

        if ($critical_count > 0) {
            echo "<p>🔴 <strong>Critical Issues:</strong> {$critical_count} - Address these first for maximum impact</p>";
            echo "<p>Expected score improvement: +15-25 points</p>";
        }

        if ($high_count > 0) {
            echo "<p>🟡 <strong>High Priority Issues:</strong> {$high_count} - Significant impact on PageSpeed</p>";
            echo "<p>Expected score improvement: +5-15 points</p>";
        }

        // Current scores summary
        if (!empty($this->current_scores)) {
            echo "<h3>📊 Current vs Target Scores</h3>";
            foreach ($this->current_scores as $strategy => $score) {
                $target = 90;
                $improvement_needed = max(0, $target - $score);
                echo "<p><strong>" . ucfirst($strategy) . ":</strong> {$score}/100 → Target: {$target}/100 (Need +{$improvement_needed} points)</p>";
            }
        }

        echo "<h3>🎯 Action Plan</h3>";
        echo "<ol>";
        echo "<li>Address all CRITICAL issues first</li>";
        echo "<li>Enable missing core modules (Page Cache, CSS/JS Minifier, WebP Conversion)</li>";
        echo "<li>Configure optimal settings for each module</li>";
        echo "<li>Test PageSpeed scores after each major change</li>";
        echo "<li>Address HIGH priority issues for additional improvements</li>";
        echo "</ol>";
    }
}

// Run the audit if accessed with the right parameter
if (isset($_GET['run_performance_audit']) && $_GET['run_performance_audit'] === '1') {
    $audit = new Redco_Performance_Audit();
    $audit->run_audit();
}
?>
