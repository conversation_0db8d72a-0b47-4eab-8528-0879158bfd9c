<?php
/**
 * Phase 2 Validation System for Redco Optimizer
 * 
 * Tests real-time monitoring, Core Web Vitals integration, and advanced analytics
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Phase2_Validator {
    
    private $test_results = array();
    private $start_time;
    
    /**
     * Run comprehensive Phase 2 validation
     */
    public function run_validation() {
        $this->start_time = microtime(true);
        
        echo '<div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0;">';
        echo '<h2 style="color: #495057; margin: 0 0 20px 0;">🚀 Phase 2 Validation Results</h2>';
        
        // Test Real-Time Monitoring System
        $this->test_realtime_monitoring();
        
        // Test Core Web Vitals Integration
        $this->test_core_web_vitals();
        
        // Test Advanced Analytics
        $this->test_advanced_analytics();
        
        // Test Database Schema Updates
        $this->test_phase2_database_schema();
        
        // Test AJAX Endpoints
        $this->test_phase2_ajax_endpoints();
        
        // Test Automated Scheduling
        $this->test_automated_scheduling();
        
        // Test Performance Alerting
        $this->test_performance_alerting();
        
        // Test API Integration
        $this->test_api_integration();
        
        $this->display_summary();
        echo '</div>';
        
        return $this->test_results;
    }
    
    /**
     * Test real-time monitoring system
     */
    private function test_realtime_monitoring() {
        echo '<h3>Real-Time Monitoring Tests</h3>';
        
        // Test monitoring class exists
        $this->run_test('Test monitoring class loading', function() {
            return class_exists('Redco_Realtime_Monitor');
        });
        
        // Test monitoring configuration
        $this->run_test('Test monitoring configuration', function() {
            $config = get_option('redco_monitoring_config', array());
            return is_array($config);
        });
        
        // Test metrics collection
        $this->run_test('Test metrics collection', function() {
            if (class_exists('Redco_Realtime_Monitor')) {
                $monitor = new Redco_Realtime_Monitor();
                $metrics = $monitor->collect_current_metrics();
                return is_array($metrics) && !empty($metrics);
            }
            return false;
        });
        
        // Test performance metrics table
        $this->run_test('Test performance metrics table', function() {
            global $wpdb;
            $table_name = $wpdb->prefix . 'redco_performance_metrics';
            return $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;
        });
    }
    
    /**
     * Test Core Web Vitals integration
     */
    private function test_core_web_vitals() {
        echo '<h3>Core Web Vitals Tests</h3>';
        
        // Test Core Web Vitals class exists
        $this->run_test('Test Core Web Vitals class loading', function() {
            return class_exists('Redco_Core_Web_Vitals');
        });
        
        // Test vitals measurement (local)
        $this->run_test('Test local vitals measurement', function() {
            if (class_exists('Redco_Core_Web_Vitals')) {
                $vitals = new Redco_Core_Web_Vitals();
                $results = $vitals->measure_vitals_locally(home_url());
                return !is_wp_error($results) && isset($results['core_web_vitals']);
            }
            return false;
        });
        
        // Test vitals database table
        $this->run_test('Test Core Web Vitals table', function() {
            global $wpdb;
            $table_name = $wpdb->prefix . 'redco_core_web_vitals';
            return $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;
        });
        
        // Test vitals thresholds
        $this->run_test('Test vitals status calculation', function() {
            if (class_exists('Redco_Core_Web_Vitals')) {
                $vitals = new Redco_Core_Web_Vitals();
                $reflection = new ReflectionClass($vitals);
                $method = $reflection->getMethod('get_vitals_status');
                $method->setAccessible(true);
                
                $status = $method->invoke($vitals, 2.0, 'lcp');
                return in_array($status, array('good', 'needs_improvement', 'poor'));
            }
            return false;
        });
    }
    
    /**
     * Test advanced analytics
     */
    private function test_advanced_analytics() {
        echo '<h3>Advanced Analytics Tests</h3>';
        
        // Test historical data retrieval
        $this->run_test('Test historical metrics retrieval', function() {
            if (class_exists('Redco_Realtime_Monitor')) {
                $monitor = new Redco_Realtime_Monitor();
                $reflection = new ReflectionClass($monitor);
                $method = $reflection->getMethod('get_historical_metrics');
                $method->setAccessible(true);
                
                $historical = $method->invoke($monitor, 24);
                return is_array($historical);
            }
            return false;
        });
        
        // Test performance trends
        $this->run_test('Test performance trend calculation', function() {
            if (class_exists('Redco_Core_Web_Vitals')) {
                $vitals = new Redco_Core_Web_Vitals();
                $reflection = new ReflectionClass($vitals);
                $method = $reflection->getMethod('calculate_vitals_summary');
                $method->setAccessible(true);
                
                $sample_data = array(
                    array('performance_score' => 85, 'lcp_value' => 2.1, 'fid_value' => 95, 'cls_value' => 0.08),
                    array('performance_score' => 88, 'lcp_value' => 2.0, 'fid_value' => 90, 'cls_value' => 0.07)
                );
                
                $summary = $method->invoke($vitals, $sample_data);
                return isset($summary['trend']) && isset($summary['avg_performance_score']);
            }
            return false;
        });
        
        // Test alert generation
        $this->run_test('Test performance alert generation', function() {
            if (class_exists('Redco_Realtime_Monitor')) {
                $monitor = new Redco_Realtime_Monitor();
                $reflection = new ReflectionClass($monitor);
                $method = $reflection->getMethod('get_active_alerts');
                $method->setAccessible(true);
                
                $alerts = $method->invoke($monitor);
                return is_array($alerts);
            }
            return false;
        });
    }
    
    /**
     * Test Phase 2 database schema
     */
    private function test_phase2_database_schema() {
        echo '<h3>Database Schema Tests</h3>';
        
        global $wpdb;
        
        // Test performance metrics table structure
        $this->run_test('Test performance metrics table structure', function() use ($wpdb) {
            $table_name = $wpdb->prefix . 'redco_performance_metrics';
            $columns = $wpdb->get_results("DESCRIBE {$table_name}");
            
            $required_columns = array('id', 'timestamp', 'metric_type', 'metric_name', 'metric_value');
            $existing_columns = array_column($columns, 'Field');
            
            foreach ($required_columns as $column) {
                if (!in_array($column, $existing_columns)) {
                    return false;
                }
            }
            return true;
        });
        
        // Test Core Web Vitals table structure
        $this->run_test('Test Core Web Vitals table structure', function() use ($wpdb) {
            $table_name = $wpdb->prefix . 'redco_core_web_vitals';
            $columns = $wpdb->get_results("DESCRIBE {$table_name}");
            
            $required_columns = array('id', 'url', 'strategy', 'timestamp', 'performance_score', 'lcp_value', 'fid_value', 'cls_value');
            $existing_columns = array_column($columns, 'Field');
            
            foreach ($required_columns as $column) {
                if (!in_array($column, $existing_columns)) {
                    return false;
                }
            }
            return true;
        });
    }
    
    /**
     * Test Phase 2 AJAX endpoints
     */
    private function test_phase2_ajax_endpoints() {
        echo '<h3>AJAX Endpoints Tests</h3>';
        
        $required_endpoints = array(
            'wp_ajax_redco_get_live_metrics',
            'wp_ajax_redco_start_monitoring',
            'wp_ajax_redco_stop_monitoring',
            'wp_ajax_redco_test_core_web_vitals',
            'wp_ajax_redco_get_vitals_history',
            'wp_ajax_redco_save_pagespeed_api_key',
            'wp_ajax_redco_get_vitals_recommendations'
        );
        
        $this->run_test('Test Phase 2 AJAX endpoints registration', function() use ($required_endpoints) {
            global $wp_filter;
            
            foreach ($required_endpoints as $endpoint) {
                if (!isset($wp_filter[$endpoint])) {
                    return false;
                }
            }
            return true;
        });
    }
    
    /**
     * Test automated scheduling
     */
    private function test_automated_scheduling() {
        echo '<h3>Automated Scheduling Tests</h3>';
        
        // Test cron hooks registration
        $this->run_test('Test monitoring cron hooks', function() {
            global $wp_filter;
            
            $required_hooks = array(
                'redco_collect_metrics',
                'redco_check_alerts',
                'redco_test_core_web_vitals_cron'
            );
            
            foreach ($required_hooks as $hook) {
                if (!isset($wp_filter[$hook])) {
                    return false;
                }
            }
            return true;
        });
        
        // Test cron interval registration
        $this->run_test('Test custom cron intervals', function() {
            $schedules = wp_get_schedules();
            return isset($schedules['redco_monitoring_interval']) || isset($schedules['redco_5min']);
        });
    }
    
    /**
     * Test performance alerting
     */
    private function test_performance_alerting() {
        echo '<h3>Performance Alerting Tests</h3>';
        
        // Test alert configuration
        $this->run_test('Test alert configuration storage', function() {
            $config = get_option('redco_monitoring_config', array());
            return is_array($config);
        });
        
        // Test alert message generation
        $this->run_test('Test alert message generation', function() {
            if (class_exists('Redco_Realtime_Monitor')) {
                $monitor = new Redco_Realtime_Monitor();
                $reflection = new ReflectionClass($monitor);
                $method = $reflection->getMethod('get_alert_message');
                $method->setAccessible(true);
                
                $message = $method->invoke($monitor, 'page_load_time', array('value' => 3000));
                return !empty($message) && is_string($message);
            }
            return false;
        });
    }
    
    /**
     * Test API integration
     */
    private function test_api_integration() {
        echo '<h3>API Integration Tests</h3>';
        
        // Test PageSpeed API configuration
        $this->run_test('Test PageSpeed API configuration', function() {
            $api_key = get_option('redco_pagespeed_api_key', '');
            return is_string($api_key); // Can be empty, but should be a string
        });
        
        // Test API URL construction
        $this->run_test('Test API URL construction', function() {
            if (class_exists('Redco_Core_Web_Vitals')) {
                $vitals = new Redco_Core_Web_Vitals();
                $reflection = new ReflectionClass($vitals);
                $property = $reflection->getProperty('api_endpoint');
                $property->setAccessible(true);
                
                $endpoint = $property->getValue($vitals);
                return filter_var($endpoint, FILTER_VALIDATE_URL) !== false;
            }
            return false;
        });
        
        // Test local measurement fallback
        $this->run_test('Test local measurement fallback', function() {
            if (class_exists('Redco_Core_Web_Vitals')) {
                $vitals = new Redco_Core_Web_Vitals();
                $results = $vitals->measure_vitals_locally(home_url());
                return !is_wp_error($results) && isset($results['source']) && $results['source'] === 'local_measurement';
            }
            return false;
        });
    }
    
    /**
     * Run individual test
     */
    private function run_test($test_name, $test_function) {
        $start_time = microtime(true);
        
        try {
            $result = $test_function();
            $execution_time = round((microtime(true) - $start_time) * 1000, 2);
            
            $status = $result ? 'Passed' : 'Failed';
            $status_color = $result ? '#28a745' : '#dc3545';
            $status_icon = $result ? '✅' : '❌';
            
            echo '<div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; margin: 4px 0; background: ' . ($result ? '#d4edda' : '#f8d7da') . '; border-radius: 4px;">';
            echo '<span>' . $status_icon . ' ' . esc_html($test_name) . '</span>';
            echo '<span style="color: ' . $status_color . '; font-weight: bold;">' . $status . '</span>';
            echo '<span style="color: #6c757d; font-size: 0.9em;">' . $execution_time . 'ms</span>';
            echo '</div>';
            
            $this->test_results[] = array(
                'test' => $test_name,
                'status' => $status,
                'execution_time' => $execution_time,
                'passed' => $result
            );
            
        } catch (Exception $e) {
            $execution_time = round((microtime(true) - $start_time) * 1000, 2);
            
            echo '<div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; margin: 4px 0; background: #f8d7da; border-radius: 4px;">';
            echo '<span>❌ ' . esc_html($test_name) . '</span>';
            echo '<span style="color: #dc3545; font-weight: bold;">Error</span>';
            echo '<span style="color: #6c757d; font-size: 0.9em;">' . $execution_time . 'ms</span>';
            echo '</div>';
            echo '<div style="color: #dc3545; font-size: 0.9em; margin-left: 20px;">Error: ' . esc_html($e->getMessage()) . '</div>';
            
            $this->test_results[] = array(
                'test' => $test_name,
                'status' => 'Error',
                'execution_time' => $execution_time,
                'passed' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Display validation summary
     */
    private function display_summary() {
        $total_tests = count($this->test_results);
        $passed_tests = count(array_filter($this->test_results, function($test) {
            return $test['passed'];
        }));
        
        $success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 2) : 0;
        $total_time = round((microtime(true) - $this->start_time) * 1000, 2);
        
        $overall_status = $success_rate == 100 ? 'Passed' : ($success_rate >= 80 ? 'Partial' : 'Failed');
        $status_color = $success_rate == 100 ? '#28a745' : ($success_rate >= 80 ? '#ffc107' : '#dc3545');
        
        echo '<div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid ' . $status_color . ';">';
        echo '<h3 style="margin: 0 0 15px 0; color: ' . $status_color . ';">Phase 2 Validation Results</h3>';
        echo '<p><strong>Overall Status:</strong> <span style="color: ' . $status_color . ';">' . $overall_status . '</span></p>';
        echo '<p><strong>Success Rate:</strong> ' . $success_rate . '% (' . $passed_tests . '/' . $total_tests . ' tests passed)</p>';
        echo '<p><strong>Total Execution Time:</strong> ' . $total_time . 'ms</p>';
        echo '</div>';
    }
    
    /**
     * Display validation results
     */
    public function display_validation_results() {
        // Results are displayed inline during run_validation()
        return $this->test_results;
    }
}
