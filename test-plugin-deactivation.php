<?php
/**
 * CRITICAL TEST: Plugin Deactivation Test
 * 
 * This script helps test if the 500 error is caused by the Redco Optimizer plugin
 * by temporarily deactivating it and checking if WordPress loads
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🧪 REDCO OPTIMIZER DEACTIVATION TEST\n";
echo "===================================\n\n";

// Find WordPress root
$wp_root = dirname(__FILE__);
while (!file_exists($wp_root . '/wp-config.php') && $wp_root !== '/') {
    $wp_root = dirname($wp_root);
}

if (!file_exists($wp_root . '/wp-config.php')) {
    echo "❌ WordPress installation not found.\n";
    echo "Please run this script from within your WordPress installation.\n";
    exit(1);
}

echo "✅ WordPress found at: {$wp_root}\n";

// Load WordPress
define('WP_USE_THEMES', false);
define('ABSPATH', $wp_root . '/');

try {
    require_once ABSPATH . 'wp-config.php';
    require_once ABSPATH . 'wp-includes/functions.php';
    require_once ABSPATH . 'wp-includes/plugin.php';
    
    echo "✅ WordPress loaded successfully\n\n";
    
} catch (Exception $e) {
    echo "❌ Failed to load WordPress: " . $e->getMessage() . "\n";
    exit(1);
}

// Check current plugin status
echo "Current Plugin Status:\n";
echo "---------------------\n";

$active_plugins = get_option('active_plugins', array());
$redco_plugin = 'redco-optimizer/redco-optimizer.php';
$redco_is_active = in_array($redco_plugin, $active_plugins);

echo "Active plugins count: " . count($active_plugins) . "\n";
echo "Redco Optimizer status: " . ($redco_is_active ? "ACTIVE" : "INACTIVE") . "\n\n";

if ($redco_is_active) {
    echo "🔧 DEACTIVATING REDCO OPTIMIZER...\n";
    echo "=================================\n";
    
    // Remove from active plugins
    $updated_plugins = array_diff($active_plugins, array($redco_plugin));
    
    // Update the option
    $result = update_option('active_plugins', $updated_plugins);
    
    if ($result) {
        echo "✅ Redco Optimizer deactivated successfully\n";
        echo "📝 Plugin has been temporarily deactivated\n\n";
        
        echo "🧪 TEST INSTRUCTIONS:\n";
        echo "====================\n";
        echo "1. Try accessing your WordPress admin now: {$wp_root}/wp-admin/\n";
        echo "2. If it loads successfully, the 500 error was caused by Redco Optimizer\n";
        echo "3. If it still shows 500 error, the issue is elsewhere\n\n";
        
        echo "🔄 TO REACTIVATE REDCO OPTIMIZER:\n";
        echo "=================================\n";
        echo "Option 1: Use WordPress admin (if accessible):\n";
        echo "  - Go to Plugins page\n";
        echo "  - Find Redco Optimizer\n";
        echo "  - Click Activate\n\n";
        echo "Option 2: Run this script again (it will reactivate automatically)\n\n";
        
    } else {
        echo "❌ Failed to deactivate plugin\n";
        echo "You may need to deactivate it manually through WordPress admin\n";
    }
    
} else {
    echo "🔄 REACTIVATING REDCO OPTIMIZER...\n";
    echo "==================================\n";
    
    // Check if plugin file exists
    $plugin_file = WP_PLUGIN_DIR . '/' . $redco_plugin;
    if (file_exists($plugin_file)) {
        // Add to active plugins
        $updated_plugins = $active_plugins;
        $updated_plugins[] = $redco_plugin;
        
        // Update the option
        $result = update_option('active_plugins', $updated_plugins);
        
        if ($result) {
            echo "✅ Redco Optimizer reactivated successfully\n";
            echo "📝 Plugin is now active again\n\n";
            
            echo "🧪 TEST RESULTS:\n";
            echo "================\n";
            echo "1. Try accessing your WordPress admin now: {$wp_root}/wp-admin/\n";
            echo "2. If you get 500 error again, the issue is with Redco Optimizer\n";
            echo "3. Check the debug-500-error.php script for detailed error analysis\n\n";
            
        } else {
            echo "❌ Failed to reactivate plugin\n";
        }
    } else {
        echo "❌ Plugin file not found: {$plugin_file}\n";
        echo "The plugin may have been deleted or moved\n";
    }
}

// Show current status
echo "📊 CURRENT STATUS:\n";
echo "==================\n";

$current_active = get_option('active_plugins', array());
$current_redco_active = in_array($redco_plugin, $current_active);

echo "Total active plugins: " . count($current_active) . "\n";
echo "Redco Optimizer: " . ($current_redco_active ? "ACTIVE" : "INACTIVE") . "\n";

if (count($current_active) > 0) {
    echo "\nCurrently active plugins:\n";
    foreach ($current_active as $plugin) {
        echo "  - {$plugin}\n";
    }
}

echo "\n🎯 NEXT STEPS:\n";
echo "==============\n";

if ($current_redco_active) {
    echo "1. Test WordPress admin access\n";
    echo "2. If 500 error persists, run debug-500-error.php for detailed analysis\n";
    echo "3. Check server error logs\n";
    echo "4. Consider running the plugin in safe mode\n";
} else {
    echo "1. Test WordPress admin access\n";
    echo "2. If it works, the issue is with Redco Optimizer\n";
    echo "3. If it still fails, check other plugins or WordPress core\n";
    echo "4. Run this script again to reactivate Redco Optimizer when ready\n";
}

echo "\n✅ DEACTIVATION TEST COMPLETE\n";

?>
