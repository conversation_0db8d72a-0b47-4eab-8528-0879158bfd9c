<?php
/**
 * Redco Optimizer Dashboard Diagnostic Test
 * 
 * Run this file to test dashboard components
 */

// WordPress environment
if (!defined('ABSPATH')) {
    // Try to find WordPress
    $wp_load_paths = array(
        '../../../wp-load.php',
        '../../wp-load.php',
        '../wp-load.php',
        'wp-load.php'
    );
    
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once($path);
            break;
        }
    }
    
    if (!defined('ABSPATH')) {
        die('WordPress not found. Please run this file from your WordPress installation.');
    }
}

// Only allow admin users
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Redco Optimizer Dashboard Diagnostic</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
    .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
    .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
    .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// Test 1: Check if Redco Optimizer is active
echo "<div class='test-section'>";
echo "<h2>Test 1: Plugin Status</h2>";
if (class_exists('Redco_Optimizer')) {
    echo "<div class='success'>✅ Redco Optimizer class found</div>";
} else {
    echo "<div class='error'>❌ Redco Optimizer class not found</div>";
}

if (class_exists('Redco_Admin_UI')) {
    echo "<div class='success'>✅ Redco Admin UI class found</div>";
} else {
    echo "<div class='error'>❌ Redco Admin UI class not found</div>";
}
echo "</div>";

// Test 2: Check options
echo "<div class='test-section'>";
echo "<h2>Test 2: Plugin Options</h2>";
$general_options = get_option('redco_optimizer_options', array());
$performance_options = get_option('redco_optimizer_performance', array());

echo "<h3>General Options:</h3>";
echo "<pre>" . print_r($general_options, true) . "</pre>";

echo "<h3>Performance Options:</h3>";
echo "<pre>" . print_r($performance_options, true) . "</pre>";
echo "</div>";

// Test 3: Test performance metrics
echo "<div class='test-section'>";
echo "<h2>Test 3: Performance Metrics</h2>";
try {
    if (class_exists('Redco_Admin_UI')) {
        $admin_ui = new Redco_Admin_UI();
        
        // Use reflection to access private method
        $reflection = new ReflectionClass($admin_ui);
        $method = $reflection->getMethod('get_performance_metrics');
        $method->setAccessible(true);
        
        $performance_data = $method->invoke($admin_ui);
        
        echo "<div class='success'>✅ Performance metrics calculated successfully</div>";
        echo "<pre>" . print_r($performance_data, true) . "</pre>";
    } else {
        echo "<div class='error'>❌ Cannot test performance metrics - Admin UI class not found</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error calculating performance metrics: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 4: Check JavaScript dependencies
echo "<div class='test-section'>";
echo "<h2>Test 4: JavaScript Files</h2>";
$js_files = array(
    'assets/js/redco-admin-consolidated.js',
    'assets/js/admin-scripts.js',
    'assets/js/redco-init.js'
);

foreach ($js_files as $js_file) {
    if (file_exists($js_file)) {
        echo "<div class='success'>✅ Found: $js_file</div>";
    } else {
        echo "<div class='error'>❌ Missing: $js_file</div>";
    }
}
echo "</div>";

// Test 5: Check CSS files
echo "<div class='test-section'>";
echo "<h2>Test 5: CSS Files</h2>";
$css_files = array(
    'assets/css/admin-styles.css',
    'assets/css/redco-admin.css'
);

foreach ($css_files as $css_file) {
    if (file_exists($css_file)) {
        echo "<div class='success'>✅ Found: $css_file</div>";
    } else {
        echo "<div class='error'>❌ Missing: $css_file</div>";
    }
}
echo "</div>";

// Test 6: Check WordPress environment
echo "<div class='test-section'>";
echo "<h2>Test 6: WordPress Environment</h2>";
echo "<div class='info'>WordPress Version: " . get_bloginfo('version') . "</div>";
echo "<div class='info'>PHP Version: " . PHP_VERSION . "</div>";
echo "<div class='info'>Memory Limit: " . ini_get('memory_limit') . "</div>";
echo "<div class='info'>Max Execution Time: " . ini_get('max_execution_time') . "</div>";

// Check if WP_DEBUG is enabled
if (defined('WP_DEBUG') && WP_DEBUG) {
    echo "<div class='warning'>⚠️ WP_DEBUG is enabled</div>";
} else {
    echo "<div class='info'>WP_DEBUG is disabled</div>";
}
echo "</div>";

// Test 7: Check database queries
echo "<div class='test-section'>";
echo "<h2>Test 7: Database Queries</h2>";
global $wpdb;
$query_count = get_num_queries();
echo "<div class='info'>Current query count: $query_count</div>";

if ($query_count > 50) {
    echo "<div class='warning'>⚠️ High number of database queries detected</div>";
} else {
    echo "<div class='success'>✅ Database query count is normal</div>";
}
echo "</div>";

// Test 8: Memory usage
echo "<div class='test-section'>";
echo "<h2>Test 8: Memory Usage</h2>";
$memory_usage = round(memory_get_peak_usage(true) / 1024 / 1024, 1);
echo "<div class='info'>Peak memory usage: {$memory_usage} MB</div>";

if ($memory_usage > 128) {
    echo "<div class='warning'>⚠️ High memory usage detected</div>";
} else {
    echo "<div class='success'>✅ Memory usage is normal</div>";
}
echo "</div>";

echo "<div class='test-section info'>";
echo "<h2>Next Steps</h2>";
echo "<p>1. Check browser console for JavaScript errors</p>";
echo "<p>2. Check Network tab for failed requests</p>";
echo "<p>3. Enable WP_DEBUG and check error logs</p>";
echo "<p>4. Share any error messages you find</p>";
echo "</div>";
?>
