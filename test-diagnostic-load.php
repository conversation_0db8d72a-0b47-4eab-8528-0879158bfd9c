<?php
/**
 * CRITICAL FIX: Test Script for Diagnostic Module Loading
 * 
 * This script tests if the diagnostic module can be loaded without errors
 * Run this to validate the Internal Server Error fix
 */

// Simulate WordPress environment constants
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

if (!defined('REDCO_OPTIMIZER_PLUGIN_DIR')) {
    define('REDCO_OPTIMIZER_PLUGIN_DIR', dirname(__FILE__) . '/');
}

if (!defined('REDCO_OPTIMIZER_VERSION')) {
    define('REDCO_OPTIMIZER_VERSION', '1.0.0');
}

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🧪 Testing Diagnostic Module Loading...\n";
echo "=====================================\n";

// Test 1: Check if trait file exists and is readable
echo "Test 1: Checking trait file...\n";
$trait_file = 'modules/diagnostic-autofix/class-diagnostic-helpers.php';
if (file_exists($trait_file)) {
    echo "✅ Trait file exists: {$trait_file}\n";
    if (is_readable($trait_file)) {
        echo "✅ Trait file is readable\n";
    } else {
        echo "❌ Trait file is not readable\n";
        exit(1);
    }
} else {
    echo "❌ Trait file not found: {$trait_file}\n";
    exit(1);
}

// Test 2: Check if API endpoints file exists
echo "\nTest 2: Checking API endpoints file...\n";
$api_file = 'includes/class-api-endpoints.php';
if (file_exists($api_file)) {
    echo "✅ API endpoints file exists: {$api_file}\n";
} else {
    echo "❌ API endpoints file not found: {$api_file}\n";
    exit(1);
}

// Test 3: Check if main diagnostic file exists
echo "\nTest 3: Checking main diagnostic file...\n";
$main_file = 'modules/diagnostic-autofix/class-diagnostic-autofix.php';
if (file_exists($main_file)) {
    echo "✅ Main diagnostic file exists: {$main_file}\n";
} else {
    echo "❌ Main diagnostic file not found: {$main_file}\n";
    exit(1);
}

// Test 4: Check for syntax errors by parsing files
echo "\nTest 4: Checking for PHP syntax errors...\n";

$files_to_check = array(
    $api_file,
    $trait_file,
    $main_file
);

foreach ($files_to_check as $file) {
    echo "Checking syntax: {$file}...\n";
    
    // Use php -l to check syntax
    $output = array();
    $return_code = 0;
    
    exec("php -l \"{$file}\" 2>&1", $output, $return_code);
    
    if ($return_code === 0) {
        echo "✅ Syntax OK: {$file}\n";
    } else {
        echo "❌ Syntax Error in {$file}:\n";
        echo implode("\n", $output) . "\n";
        exit(1);
    }
}

// Test 5: Try to include files in correct order
echo "\nTest 5: Testing file inclusion order...\n";

try {
    // Mock WordPress functions that might be called
    if (!function_exists('get_bloginfo')) {
        function get_bloginfo($show = '') {
            return '6.0';
        }
    }
    
    if (!function_exists('__')) {
        function __($text, $domain = 'default') {
            return $text;
        }
    }
    
    if (!function_exists('wp_convert_hr_to_bytes')) {
        function wp_convert_hr_to_bytes($value) {
            return *********; // 256MB
        }
    }
    
    if (!function_exists('home_url')) {
        function home_url() {
            return 'http://localhost';
        }
    }
    
    if (!function_exists('includes_url')) {
        function includes_url() {
            return 'http://localhost/wp-includes/';
        }
    }
    
    if (!function_exists('content_url')) {
        function content_url() {
            return 'http://localhost/wp-content/';
        }
    }
    
    if (!function_exists('get_template_directory_uri')) {
        function get_template_directory_uri() {
            return 'http://localhost/wp-content/themes/test/';
        }
    }
    
    // Include API endpoints first
    echo "Including API endpoints...\n";
    require_once $api_file;
    echo "✅ API endpoints loaded successfully\n";
    
    // Include trait file
    echo "Including trait file...\n";
    require_once $trait_file;
    echo "✅ Trait loaded successfully\n";
    
    echo "\n🎉 ALL TESTS PASSED!\n";
    echo "The diagnostic module should now load without Internal Server Error.\n";
    echo "\nNext steps:\n";
    echo "1. Clear all caches (WordPress, browser, server)\n";
    echo "2. Try accessing the diagnostic page again\n";
    echo "3. Check for any remaining JavaScript errors in browser console\n";
    echo "4. Run RedcoSingleFixTest.runAllTests() to validate single fix functionality\n";
    
} catch (ParseError $e) {
    echo "❌ Parse Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    exit(1);
} catch (Error $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    exit(1);
}
?>
