/**
 * CRITICAL FIX: Centralized Debug Utility System
 * Replaces scattered console.log() statements with conditional debug logging
 * Only activates in development environments
 */

(function() {
    'use strict';

    // Check if we're in development environment
    const isDevelopment = (
        window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1' ||
        window.location.hostname.includes('.local') ||
        window.location.hostname.includes('.dev') ||
        (typeof redcoConfig !== 'undefined' && redcoConfig.debug === true)
    );

    /**
     * Centralized Debug System
     */
    window.redcoDebug = {
        enabled: isDevelopment,
        
        /**
         * Log debug information (replaces console.log)
         */
        log: function(...args) {
            if (!this.enabled) return;
            console.log('[Redco Debug]', ...args);
        },

        /**
         * Log warnings (replaces console.warn)
         */
        warn: function(...args) {
            if (!this.enabled) return;
            console.warn('[Redco Warning]', ...args);
        },

        /**
         * Log errors (replaces console.error)
         */
        error: function(...args) {
            if (!this.enabled) return;
            console.error('[Redco Error]', ...args);
        },

        /**
         * Log performance metrics
         */
        performance: function(label, data) {
            if (!this.enabled) return;
            console.log(`[Redco Performance] ${label}:`, data);
        },

        /**
         * Log AJAX requests/responses
         */
        ajax: function(action, data, response) {
            if (!this.enabled) return;
            console.group(`[Redco AJAX] ${action}`);
            console.log('Request Data:', data);
            if (response) {
                console.log('Response:', response);
            }
            console.groupEnd();
        },

        /**
         * Log timing information
         */
        time: function(label) {
            if (!this.enabled) return;
            console.time(`[Redco Timer] ${label}`);
        },

        timeEnd: function(label) {
            if (!this.enabled) return;
            console.timeEnd(`[Redco Timer] ${label}`);
        },

        /**
         * Enable/disable debug mode programmatically
         */
        enable: function() {
            this.enabled = true;
            this.log('Debug mode enabled');
        },

        disable: function() {
            this.log('Debug mode disabled');
            this.enabled = false;
        },

        /**
         * Get debug status
         */
        isEnabled: function() {
            return this.enabled;
        }
    };

    // Initialize debug mode indicator
    if (window.redcoDebug.enabled) {
        window.redcoDebug.log('Debug mode active - development environment detected');
    }

})();
