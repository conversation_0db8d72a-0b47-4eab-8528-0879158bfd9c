/**
 * Redco Optimizer Debug Utilities
 * Provides debugging functionality for development and troubleshooting
 */

(function($) {
    'use strict';

    /**
     * Debug Utilities Module
     */
    window.redcoDebug = {
        
        /**
         * Configuration
         */
        enabled: false,
        logLevel: 'info',
        maxLogs: 100,
        logs: [],
        
        /**
         * Initialize debug utilities
         */
        init: function() {
            // Check if debug mode is enabled
            this.enabled = (typeof redcoAjax !== 'undefined' && 
                           redcoAjax.settings && 
                           redcoAjax.settings.debugMode) || 
                          (typeof console !== 'undefined' && 
                           window.location.search.includes('debug=1'));
            
            if (this.enabled) {
                this.log('Debug utilities initialized');
                this.setupDebugPanel();
            }
        },
        
        /**
         * Log a message
         */
        log: function(message, data) {
            if (!this.enabled) return;
            
            const timestamp = new Date().toISOString();
            const logEntry = {
                timestamp: timestamp,
                level: 'info',
                message: message,
                data: data || null
            };
            
            this.addLog(logEntry);
            
            if (typeof console !== 'undefined' && console.log) {
                if (data) {
                    console.log('[Redco Debug]', message, data);
                } else {
                    console.log('[Redco Debug]', message);
                }
            }
        },
        
        /**
         * Log an error
         */
        error: function(message, data) {
            if (!this.enabled) return;
            
            const timestamp = new Date().toISOString();
            const logEntry = {
                timestamp: timestamp,
                level: 'error',
                message: message,
                data: data || null
            };
            
            this.addLog(logEntry);
            
            if (typeof console !== 'undefined' && console.error) {
                if (data) {
                    console.error('[Redco Error]', message, data);
                } else {
                    console.error('[Redco Error]', message);
                }
            }
        },
        
        /**
         * Log a warning
         */
        warn: function(message, data) {
            if (!this.enabled) return;
            
            const timestamp = new Date().toISOString();
            const logEntry = {
                timestamp: timestamp,
                level: 'warning',
                message: message,
                data: data || null
            };
            
            this.addLog(logEntry);
            
            if (typeof console !== 'undefined' && console.warn) {
                if (data) {
                    console.warn('[Redco Warning]', message, data);
                } else {
                    console.warn('[Redco Warning]', message);
                }
            }
        },
        
        /**
         * Add log entry to internal storage
         */
        addLog: function(logEntry) {
            this.logs.push(logEntry);
            
            // Keep only the most recent logs
            if (this.logs.length > this.maxLogs) {
                this.logs.shift();
            }
            
            // Update debug panel if it exists
            this.updateDebugPanel();
        },
        
        /**
         * Setup debug panel (only in debug mode)
         */
        setupDebugPanel: function() {
            if (!this.enabled || $('#redco-debug-panel').length > 0) return;
            
            const debugPanel = $(`
                <div id="redco-debug-panel" style="
                    position: fixed;
                    bottom: 10px;
                    right: 10px;
                    width: 300px;
                    max-height: 200px;
                    background: #fff;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    z-index: 9999;
                    font-size: 12px;
                    display: none;
                ">
                    <div style="
                        background: #0073aa;
                        color: white;
                        padding: 5px 10px;
                        font-weight: bold;
                        cursor: pointer;
                    " id="redco-debug-header">
                        Redco Debug Console
                        <span style="float: right;">▼</span>
                    </div>
                    <div id="redco-debug-content" style="
                        max-height: 150px;
                        overflow-y: auto;
                        padding: 5px;
                        font-family: monospace;
                    "></div>
                    <div style="
                        padding: 5px;
                        border-top: 1px solid #eee;
                        text-align: center;
                    ">
                        <button id="redco-debug-clear" style="
                            background: #dc3232;
                            color: white;
                            border: none;
                            padding: 2px 8px;
                            border-radius: 2px;
                            cursor: pointer;
                            font-size: 11px;
                        ">Clear</button>
                        <button id="redco-debug-export" style="
                            background: #00a32a;
                            color: white;
                            border: none;
                            padding: 2px 8px;
                            border-radius: 2px;
                            cursor: pointer;
                            font-size: 11px;
                            margin-left: 5px;
                        ">Export</button>
                    </div>
                </div>
            `);
            
            $('body').append(debugPanel);
            
            // Toggle panel visibility
            $('#redco-debug-header').on('click', function() {
                const content = $('#redco-debug-content');
                const arrow = $(this).find('span');
                if (content.is(':visible')) {
                    content.hide();
                    arrow.text('▶');
                } else {
                    content.show();
                    arrow.text('▼');
                }
            });
            
            // Clear logs
            $('#redco-debug-clear').on('click', function() {
                window.redcoDebug.clearLogs();
            });
            
            // Export logs
            $('#redco-debug-export').on('click', function() {
                window.redcoDebug.exportLogs();
            });
            
            // Show panel after a delay
            setTimeout(function() {
                $('#redco-debug-panel').fadeIn();
            }, 1000);
        },
        
        /**
         * Update debug panel content
         */
        updateDebugPanel: function() {
            if (!this.enabled) return;
            
            const content = $('#redco-debug-content');
            if (content.length === 0) return;
            
            const recentLogs = this.logs.slice(-10); // Show last 10 logs
            let html = '';
            
            recentLogs.forEach(function(log) {
                const time = new Date(log.timestamp).toLocaleTimeString();
                const levelColor = log.level === 'error' ? '#dc3232' : 
                                  log.level === 'warning' ? '#ffb900' : '#0073aa';
                
                html += `<div style="margin-bottom: 2px; color: ${levelColor};">
                    [${time}] ${log.message}
                </div>`;
            });
            
            content.html(html);
            content.scrollTop(content[0].scrollHeight);
        },
        
        /**
         * Clear all logs
         */
        clearLogs: function() {
            this.logs = [];
            this.updateDebugPanel();
            this.log('Debug logs cleared');
        },
        
        /**
         * Export logs as JSON
         */
        exportLogs: function() {
            const data = {
                timestamp: new Date().toISOString(),
                logs: this.logs,
                userAgent: navigator.userAgent,
                url: window.location.href
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'redco-debug-logs.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.log('Debug logs exported');
        },
        
        /**
         * Get all logs
         */
        getLogs: function() {
            return this.logs.slice(); // Return copy
        },
        
        /**
         * Check if debug is enabled
         */
        isEnabled: function() {
            return this.enabled;
        }
    };

    // Auto-initialize when DOM is ready
    $(document).ready(function() {
        window.redcoDebug.init();
    });

})(jQuery);
