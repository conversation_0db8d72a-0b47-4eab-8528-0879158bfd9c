<?php
/**
 * Diagnostic Helper Methods for Redco Optimizer
 *
 * Helper methods for the Diagnostic & Auto-Fix module
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Include API endpoints configuration
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-api-endpoints.php';

trait Redco_Diagnostic_Helpers {

    /**
     * Get latest WordPress version
     */
    private function get_latest_wordpress_version() {
        $version_check = get_site_transient('update_core');
        if (isset($version_check->updates[0]->version)) {
            return $version_check->updates[0]->version;
        }
        return get_bloginfo('version'); // Fallback to current version
    }

    /**
     * Detect plugin conflicts
     */
    private function detect_plugin_conflicts() {
        $conflicts = array();
        $active_plugins = get_option('active_plugins', array());

        // Known conflicting plugins
        $known_conflicts = array(
            'wp-rocket/wp-rocket.php' => array(
                'name' => 'WP Rocket',
                'description' => 'WP Rocket conflicts with Redco Optimizer caching features',
                'severity' => 'high',
                'auto_fixable' => false,
                'impact' => 'Duplicate caching and potential performance issues',
                'recommendation' => 'Disable WP Rocket or Redco Optimizer page cache',
                'fix_action' => 'resolve_caching_conflict'
            ),
            'w3-total-cache/w3-total-cache.php' => array(
                'name' => 'W3 Total Cache',
                'description' => 'W3 Total Cache may conflict with Redco Optimizer',
                'severity' => 'medium',
                'auto_fixable' => false,
                'impact' => 'Potential caching conflicts',
                'recommendation' => 'Choose one caching solution',
                'fix_action' => 'resolve_caching_conflict'
            ),
            'wp-super-cache/wp-cache.php' => array(
                'name' => 'WP Super Cache',
                'description' => 'WP Super Cache conflicts with page caching',
                'severity' => 'medium',
                'auto_fixable' => false,
                'impact' => 'Duplicate caching functionality',
                'recommendation' => 'Disable one caching plugin',
                'fix_action' => 'resolve_caching_conflict'
            )
        );

        foreach ($active_plugins as $plugin) {
            if (isset($known_conflicts[$plugin])) {
                $conflict = $known_conflicts[$plugin];
                $conflict['plugin'] = $plugin;
                $conflicts[] = $conflict;
            }
        }

        // Check for multiple optimization plugins
        $optimization_plugins = array();
        foreach ($active_plugins as $plugin) {
            $plugin_data = get_plugin_data(WP_PLUGIN_DIR . '/' . $plugin);
            if (stripos($plugin_data['Description'], 'optimization') !== false ||
                stripos($plugin_data['Description'], 'cache') !== false ||
                stripos($plugin_data['Description'], 'minify') !== false) {
                $optimization_plugins[] = $plugin_data['Name'];
            }
        }

        if (count($optimization_plugins) > 2) { // Including Redco Optimizer
            $conflicts[] = array(
                'plugin' => 'multiple_optimization',
                'name' => 'Multiple Optimization Plugins',
                'description' => 'Multiple optimization plugins detected: ' . implode(', ', $optimization_plugins),
                'severity' => 'medium',
                'auto_fixable' => false,
                'impact' => 'Potential conflicts and reduced performance',
                'recommendation' => 'Use only one comprehensive optimization plugin',
                'fix_action' => 'resolve_plugin_conflicts'
            );
        }

        return $conflicts;
    }

    /**
     * Scan theme performance issues
     */
    private function scan_theme_performance() {
        $issues = array();
        $theme = wp_get_theme();

        // Check for outdated theme
        if ($theme->get('Version')) {
            $theme_updates = get_site_transient('update_themes');
            if (isset($theme_updates->response[$theme->get_stylesheet()])) {
                $issues[] = array(
                    'id' => 'outdated_theme',
                    'title' => __('Outdated Theme', 'redco-optimizer'),
                    'description' => sprintf(__('Theme %s has an update available.', 'redco-optimizer'), $theme->get('Name')),
                    'severity' => 'medium',
                    'category' => 'wordpress',
                    'auto_fixable' => false,
                    'impact' => 'Security vulnerabilities and missing performance improvements',
                    'recommendation' => 'Update theme to latest version',
                    'fix_action' => 'update_theme'
                );
            }
        }

        // Check for jQuery dependency in theme
        if ($this->theme_loads_jquery_in_header()) {
            $issues[] = array(
                'id' => 'jquery_in_header',
                'title' => __('jQuery Loaded in Header', 'redco-optimizer'),
                'description' => __('Theme loads jQuery in header, blocking page rendering.', 'redco-optimizer'),
                'severity' => 'medium',
                'category' => 'wordpress',
                'auto_fixable' => true,
                'impact' => 'Render-blocking JavaScript',
                'recommendation' => 'Move jQuery to footer',
                'fix_action' => 'move_jquery_to_footer'
            );
        }

        return $issues;
    }

    /**
     * Scan WordPress configuration issues
     */
    private function scan_wp_config_issues() {
        $issues = array();

        // Check if WP_DEBUG is enabled in production
        if (defined('WP_DEBUG') && WP_DEBUG && !$this->is_development_environment()) {
            $issues[] = array(
                'id' => 'debug_enabled_production',
                'title' => __('Debug Mode Enabled in Production', 'redco-optimizer'),
                'description' => __('WP_DEBUG is enabled on a production site.', 'redco-optimizer'),
                'severity' => 'high',
                'category' => 'wordpress',
                'auto_fixable' => true,
                'impact' => 'Performance overhead and security risks',
                'recommendation' => 'Disable WP_DEBUG in production',
                'fix_action' => 'disable_debug_mode'
            );
        }

        // Check for missing wp-config optimizations
        if (!defined('WP_CACHE') || !WP_CACHE) {
            $issues[] = array(
                'id' => 'wp_cache_disabled',
                'title' => __('WP_CACHE Not Enabled', 'redco-optimizer'),
                'description' => __('WP_CACHE constant is not defined or disabled.', 'redco-optimizer'),
                'severity' => 'medium',
                'category' => 'wordpress',
                'auto_fixable' => true,
                'impact' => 'Caching plugins may not work optimally',
                'recommendation' => 'Enable WP_CACHE in wp-config.php',
                'fix_action' => 'enable_wp_cache'
            );
        }

        // Check memory limit
        $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
        if ($memory_limit < 256 * 1024 * 1024) { // 256MB
            $issues[] = array(
                'id' => 'low_memory_limit',
                'title' => __('Low PHP Memory Limit', 'redco-optimizer'),
                'description' => sprintf(__('PHP memory limit is %s. Recommended: 256MB+.', 'redco-optimizer'), ini_get('memory_limit')),
                'severity' => 'medium',
                'category' => 'wordpress',
                'auto_fixable' => true,
                'impact' => 'Potential memory errors and slower performance',
                'recommendation' => 'Increase PHP memory limit',
                'fix_action' => 'increase_memory_limit'
            );
        }

        return $issues;
    }

    /**
     * Get database size
     */
    private function get_database_size() {
        global $wpdb;

        $result = $wpdb->get_var("
            SELECT SUM(data_length + index_length)
            FROM information_schema.tables
            WHERE table_schema = '{$wpdb->dbname}'
        ");

        return $result ? (int) $result : 0;
    }

    /**
     * Detect slow queries
     */
    private function detect_slow_queries() {
        global $wpdb;
        $slow_queries = array();

        // Enable query logging temporarily
        if (!defined('SAVEQUERIES')) {
            define('SAVEQUERIES', true);
        }

        // Analyze recent queries if available
        if (isset($wpdb->queries) && is_array($wpdb->queries)) {
            foreach ($wpdb->queries as $query) {
                if (isset($query[1]) && $query[1] > 1.0) { // Queries taking more than 1 second
                    $slow_queries[] = array(
                        'sql' => $query[0],
                        'time' => $query[1],
                        'stack' => isset($query[2]) ? $query[2] : ''
                    );
                }
            }
        }

        return $slow_queries;
    }

    /**
     * Get autoload data size
     */
    private function get_autoload_size() {
        global $wpdb;

        $result = $wpdb->get_var("
            SELECT SUM(LENGTH(option_value))
            FROM {$wpdb->options}
            WHERE autoload = 'yes'
        ");

        return $result ? (int) $result : 0;
    }

    /**
     * Get average DOM size
     */
    private function get_average_dom_size() {
        // Sample homepage to estimate DOM size
        $homepage_url = home_url();
        $response = wp_remote_get($homepage_url, array('timeout' => 10));

        if (is_wp_error($response)) {
            return 0;
        }

        $html = wp_remote_retrieve_body($response);
        $dom_count = substr_count($html, '<') - substr_count($html, '<!--');

        return max(0, $dom_count);
    }

    /**
     * Scan image optimization issues
     */
    private function scan_image_optimization() {
        $issues = array();

        // Check for large images in media library
        $large_images = $this->get_large_images();
        if (!empty($large_images)) {
            $issues[] = array(
                'id' => 'large_images',
                'title' => __('Large Unoptimized Images', 'redco-optimizer'),
                'description' => sprintf(__('%d images larger than 1MB found in media library.', 'redco-optimizer'), count($large_images)),
                'severity' => 'medium',
                'category' => 'frontend',
                'auto_fixable' => false,
                'impact' => 'Slower page loads and increased bandwidth usage',
                'recommendation' => 'Optimize images before uploading',
                'fix_action' => 'optimize_images'
            );
        }

        // Check for missing WebP support
        if (!$this->is_webp_supported()) {
            $issues[] = array(
                'id' => 'no_webp_support',
                'title' => __('WebP Images Not Supported', 'redco-optimizer'),
                'description' => __('Server does not support WebP image format.', 'redco-optimizer'),
                'severity' => 'low',
                'category' => 'frontend',
                'auto_fixable' => false,
                'impact' => 'Missing opportunity for better image compression',
                'recommendation' => 'Enable WebP support on server',
                'fix_action' => 'enable_webp'
            );
        }

        return $issues;
    }

    /**
     * Detect render-blocking resources (ENHANCED FOR FIX PERSISTENCE)
     */
    private function detect_render_blocking_resources() {
        $blocking_resources = array();

        // Check if render blocking optimization is already applied
        $optimization_settings = get_option('redco_render_blocking_optimization');
        if ($optimization_settings && isset($optimization_settings['enabled']) && $optimization_settings['enabled']) {
            // Optimization is enabled, check if it's working properly
            $htaccess_file = ABSPATH . '.htaccess';
            if (file_exists($htaccess_file)) {
                $content = file_get_contents($htaccess_file);
                if (strpos($content, 'Render Blocking Resource Optimization') !== false) {
                    // Optimization is properly configured, don't report render-blocking issues
                    return $blocking_resources; // Return empty array
                }
            }
        }

        // Sample homepage to detect render-blocking resources
        $homepage_url = home_url();
        $response = wp_remote_get($homepage_url, array(
            'timeout' => 10,
            'headers' => array(
                'Cache-Control' => 'no-cache',
                'Pragma' => 'no-cache'
            )
        ));

        if (is_wp_error($response)) {
            return $blocking_resources;
        }

        $html = wp_remote_retrieve_body($response);

        // Find CSS files in head without media queries or async
        preg_match_all('/<link[^>]*rel=["\']stylesheet["\'][^>]*>/i', $html, $css_matches);
        foreach ($css_matches[0] as $css_link) {
            if (strpos($css_link, 'media=') === false || strpos($css_link, 'media="all"') !== false) {
                if (preg_match('/href=["\']([^"\']+)["\']/', $css_link, $href_match)) {
                    // Skip if this resource is already optimized
                    if (!$this->is_resource_optimized($href_match[1], 'css')) {
                        $blocking_resources[] = array(
                            'type' => 'css',
                            'url' => $href_match[1]
                        );
                    }
                }
            }
        }

        // Find JavaScript files without async/defer
        preg_match_all('/<script[^>]*src=[^>]*>/i', $html, $js_matches);
        foreach ($js_matches[0] as $js_script) {
            if (strpos($js_script, 'async') === false && strpos($js_script, 'defer') === false) {
                if (preg_match('/src=["\']([^"\']+)["\']/', $js_script, $src_match)) {
                    // Skip if this resource is already optimized
                    if (!$this->is_resource_optimized($src_match[1], 'js')) {
                        $blocking_resources[] = array(
                            'type' => 'js',
                            'url' => $src_match[1]
                        );
                    }
                }
            }
        }

        return $blocking_resources;
    }

    /**
     * Check if a resource is already optimized
     */
    private function is_resource_optimized($url, $type) {
        // Check if render blocking optimization is enabled
        $optimization_settings = get_option('redco_render_blocking_optimization');
        if (!$optimization_settings || !isset($optimization_settings['enabled']) || !$optimization_settings['enabled']) {
            return false;
        }

        // Check if this is a WordPress core resource that should be optimized
        $wp_includes_url = includes_url();
        $wp_content_url = content_url();

        if (strpos($url, $wp_includes_url) !== false || strpos($url, $wp_content_url) !== false) {
            // This is a WordPress resource that should be optimized
            return true;
        }

        // Check if this is a theme/plugin resource
        $theme_url = get_template_directory_uri();
        if (strpos($url, $theme_url) !== false) {
            return true;
        }

        return false;
    }

    /**
     * Scan third-party scripts
     */
    private function scan_third_party_scripts() {
        $issues = array();

        // Sample homepage to detect third-party scripts
        $homepage_url = home_url();
        $response = wp_remote_get($homepage_url, array('timeout' => 10));

        if (is_wp_error($response)) {
            return $issues;
        }

        $html = wp_remote_retrieve_body($response);

        // Common third-party domains
        $third_party_domains = array(
            'google-analytics.com',
            'googletagmanager.com',
            'facebook.net',
            'doubleclick.net',
            'googlesyndication.com'
        );

        $third_party_count = 0;
        foreach ($third_party_domains as $domain) {
            $third_party_count += substr_count($html, $domain);
        }

        if ($third_party_count > 5) {
            $issues[] = array(
                'id' => 'many_third_party_scripts',
                'title' => __('Many Third-Party Scripts', 'redco-optimizer'),
                'description' => sprintf(__('%d third-party script references found.', 'redco-optimizer'), $third_party_count),
                'severity' => 'medium',
                'category' => 'frontend',
                'auto_fixable' => false,
                'impact' => 'Slower page loads and reduced performance',
                'recommendation' => 'Minimize third-party scripts and load them asynchronously',
                'fix_action' => 'optimize_third_party_scripts'
            );
        }

        return $issues;
    }

    /**
     * Measure Time to First Byte (TTFB)
     */
    private function measure_ttfb() {
        $start_time = microtime(true);
        $response = wp_remote_get(home_url(), array('timeout' => 10));
        $end_time = microtime(true);

        if (is_wp_error($response)) {
            return 0;
        }

        return round(($end_time - $start_time) * 1000); // Convert to milliseconds
    }

    /**
     * Check if compression is enabled (ENHANCED FOR FIX PERSISTENCE)
     */
    private function is_compression_enabled() {
        // CRITICAL FIX: Check .htaccess file directly first for immediate verification
        $htaccess_file = ABSPATH . '.htaccess';
        if (file_exists($htaccess_file)) {
            $content = file_get_contents($htaccess_file);
            // If compression rules exist in .htaccess, consider it enabled
            if (strpos($content, 'mod_deflate') !== false ||
                strpos($content, 'GZIP Compression') !== false) {
                return true;
            }
        }

        // Fallback to remote check (may be cached)
        $response = wp_remote_get(home_url(), array(
            'timeout' => 10,
            'headers' => array(
                'Cache-Control' => 'no-cache',
                'Pragma' => 'no-cache'
            )
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $headers = wp_remote_retrieve_headers($response);
        return isset($headers['content-encoding']) &&
               (strpos($headers['content-encoding'], 'gzip') !== false ||
                strpos($headers['content-encoding'], 'br') !== false);
    }

    /**
     * Check if proper cache headers are set (ENHANCED FOR FIX PERSISTENCE)
     */
    private function has_proper_cache_headers() {
        // CRITICAL FIX: Check .htaccess file directly first for immediate verification
        $htaccess_file = ABSPATH . '.htaccess';
        if (file_exists($htaccess_file)) {
            $content = file_get_contents($htaccess_file);
            // If cache header rules exist in .htaccess, consider them enabled
            if (strpos($content, 'mod_expires') !== false ||
                strpos($content, 'Browser Caching') !== false ||
                strpos($content, 'ExpiresActive') !== false) {
                return true;
            }
        }

        // Fallback to remote check (may be cached)
        $test_url = includes_url('js/jquery/jquery.min.js');
        $response = wp_remote_get($test_url, array(
            'timeout' => 10,
            'headers' => array(
                'Cache-Control' => 'no-cache',
                'Pragma' => 'no-cache'
            )
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $headers = wp_remote_retrieve_headers($response);
        return isset($headers['cache-control']) || isset($headers['expires']);
    }

    /**
     * Check security headers
     */
    private function check_security_headers() {
        $response = wp_remote_get(home_url(), array('timeout' => 10));

        if (is_wp_error($response)) {
            return array();
        }

        $headers = wp_remote_retrieve_headers($response);

        return array(
            'X-Content-Type-Options' => array(
                'present' => isset($headers['x-content-type-options']),
                'affects_performance' => false,
                'description' => 'Prevents MIME type sniffing',
                'performance_impact' => 'Minimal impact',
                'recommendation' => 'Add X-Content-Type-Options: nosniff header'
            ),
            'X-Frame-Options' => array(
                'present' => isset($headers['x-frame-options']),
                'affects_performance' => false,
                'description' => 'Prevents clickjacking attacks',
                'performance_impact' => 'No performance impact',
                'recommendation' => 'Add X-Frame-Options: SAMEORIGIN header'
            ),
            'Strict-Transport-Security' => array(
                'present' => isset($headers['strict-transport-security']),
                'affects_performance' => true,
                'description' => 'Forces HTTPS connections',
                'performance_impact' => 'Improves security and SEO',
                'recommendation' => 'Add HSTS header for HTTPS sites'
            )
        );
    }

    /**
     * Check if theme loads jQuery in header
     */
    private function theme_loads_jquery_in_header() {
        $homepage_url = home_url();
        $response = wp_remote_get($homepage_url, array('timeout' => 10));

        if (is_wp_error($response)) {
            return false;
        }

        $html = wp_remote_retrieve_body($response);

        // Check for jQuery in head section
        $head_section = '';
        if (preg_match('/<head[^>]*>(.*?)<\/head>/is', $html, $matches)) {
            $head_section = $matches[1];
        }

        return strpos($head_section, 'jquery') !== false;
    }

    /**
     * Check if this is a development environment
     */
    private function is_development_environment() {
        $dev_indicators = array(
            'localhost',
            '127.0.0.1',
            '.local',
            '.dev',
            'staging',
            'test'
        );

        $site_url = home_url();
        foreach ($dev_indicators as $indicator) {
            if (strpos($site_url, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get large images from media library
     */
    private function get_large_images() {
        $large_images = array();

        $attachments = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => 50,
            'post_status' => 'inherit'
        ));

        foreach ($attachments as $attachment) {
            $file_path = get_attached_file($attachment->ID);
            if ($file_path && file_exists($file_path)) {
                $file_size = filesize($file_path);
                if ($file_size > 1024 * 1024) { // 1MB
                    $large_images[] = array(
                        'id' => $attachment->ID,
                        'size' => $file_size,
                        'url' => wp_get_attachment_url($attachment->ID)
                    );
                }
            }
        }

        return $large_images;
    }

    /**
     * Check if WebP is supported
     */
    private function is_webp_supported() {
        return function_exists('imagewebp') && function_exists('imagecreatefromwebp');
    }

    /**
     * Check database health
     */
    private function check_database_health() {
        global $wpdb;
        $score = 100;

        // Check database size
        $db_size = $this->get_database_size();
        if ($db_size > 500 * 1024 * 1024) { // 500MB
            $score -= 30;
        } elseif ($db_size > 100 * 1024 * 1024) { // 100MB
            $score -= 15;
        }

        // Check autoload size
        $autoload_size = $this->get_autoload_size();
        if ($autoload_size > 2 * 1024 * 1024) { // 2MB
            $score -= 25;
        } elseif ($autoload_size > 1024 * 1024) { // 1MB
            $score -= 10;
        }

        // Check for slow queries
        $slow_queries = $this->detect_slow_queries();
        if (count($slow_queries) > 5) {
            $score -= 20;
        } elseif (count($slow_queries) > 0) {
            $score -= 10;
        }

        return max(0, $score);
    }

    /**
     * Check plugin health
     */
    private function check_plugin_health() {
        $score = 100;
        $active_plugins = get_option('active_plugins', array());

        // Too many plugins
        if (count($active_plugins) > 30) {
            $score -= 30;
        } elseif (count($active_plugins) > 20) {
            $score -= 15;
        }

        // Check for plugin conflicts
        $conflicts = $this->detect_plugin_conflicts();
        $score -= count($conflicts) * 10;

        // Check for outdated plugins
        $plugin_updates = get_site_transient('update_plugins');
        if (isset($plugin_updates->response) && count($plugin_updates->response) > 5) {
            $score -= 15;
        }

        return max(0, $score);
    }

    /**
     * Check server health
     */
    private function check_server_health() {
        $score = 100;

        // Check TTFB
        $ttfb = $this->measure_ttfb();
        if ($ttfb > 1000) {
            $score -= 40;
        } elseif ($ttfb > 600) {
            $score -= 20;
        }

        // Check PHP version
        if (version_compare(PHP_VERSION, '8.0', '<')) {
            $score -= 20;
        } elseif (version_compare(PHP_VERSION, '7.4', '<')) {
            $score -= 40;
        }

        // Check memory limit
        $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
        if ($memory_limit < 256 * 1024 * 1024) {
            $score -= 15;
        }

        return max(0, $score);
    }

    /**
     * Check optimization health
     */
    private function check_optimization_health() {
        $score = 100;

        // Check caching
        if (!$this->is_caching_enabled()) {
            $score -= 30;
        }

        // Check compression
        if (!$this->is_compression_enabled()) {
            $score -= 20;
        }

        // Check minification
        if (!$this->is_minification_enabled()) {
            $score -= 20;
        }

        // Check image optimization
        if (!$this->is_image_optimization_enabled()) {
            $score -= 15;
        }

        // Check cache headers
        if (!$this->has_proper_cache_headers()) {
            $score -= 15;
        }

        return max(0, $score);
    }

    /**
     * Check if caching is enabled (ENHANCED FOR FIX PERSISTENCE)
     */
    private function is_caching_enabled() {
        // Check if Redco page cache module is enabled
        if (redco_is_module_enabled('page-cache')) {
            return true;
        }

        // CRITICAL FIX: Check wp-config.php file directly for WP_CACHE
        $wp_config_file = ABSPATH . 'wp-config.php';
        if (file_exists($wp_config_file)) {
            $content = file_get_contents($wp_config_file);
            // Check for WP_CACHE definition in file (more reliable than defined() check)
            $wp_cache_patterns = array(
                "/define\s*\(\s*['\"]WP_CACHE['\"]\s*,\s*true\s*\)/i",
                "/define\s*\(\s*['\"]WP_CACHE['\"]\s*,\s*1\s*\)/i"
            );

            foreach ($wp_cache_patterns as $pattern) {
                if (preg_match($pattern, $content)) {
                    return true;
                }
            }
        }

        // Fallback to runtime check (may not reflect recent changes)
        return defined('WP_CACHE') && WP_CACHE;
    }

    /**
     * Check if minification is enabled
     */
    private function is_minification_enabled() {
        return redco_is_module_enabled('css-js-minifier');
    }

    /**
     * Check if image optimization is enabled
     */
    private function is_image_optimization_enabled() {
        return redco_is_module_enabled('lazy-load') ||
               $this->is_webp_supported();
    }

    /**
     * Check for heavy scripts
     */
    private function has_heavy_scripts() {
        $homepage_url = home_url();
        $response = wp_remote_get($homepage_url, array('timeout' => 10));

        if (is_wp_error($response)) {
            return false;
        }

        $html = wp_remote_retrieve_body($response);

        // Count script tags
        $script_count = substr_count($html, '<script');

        // Check for heavy libraries
        $heavy_libraries = array('jquery-ui', 'bootstrap', 'foundation');
        foreach ($heavy_libraries as $library) {
            if (strpos($html, $library) !== false) {
                return true;
            }
        }

        return $script_count > 10;
    }

    /**
     * Check for layout shift issues
     */
    private function has_layout_shift_issues() {
        $homepage_url = home_url();
        $response = wp_remote_get($homepage_url, array('timeout' => 10));

        if (is_wp_error($response)) {
            return false;
        }

        $html = wp_remote_retrieve_body($response);

        // Check for images without dimensions
        preg_match_all('/<img[^>]*>/i', $html, $img_matches);
        $images_without_dimensions = 0;

        foreach ($img_matches[0] as $img_tag) {
            if (strpos($img_tag, 'width=') === false || strpos($img_tag, 'height=') === false) {
                $images_without_dimensions++;
            }
        }

        return $images_without_dimensions > 3;
    }

    /**
     * Check module configurations
     */
    private function check_module_configurations() {
        $issues = array();

        // Check Page Cache configuration
        if (redco_is_module_enabled('page-cache')) {
            $cache_expiration = redco_get_module_option('page-cache', 'expiration', 3600);
            if ($cache_expiration < 3600) { // Less than 1 hour
                $issues[] = array(
                    'id' => 'short_cache_expiration',
                    'title' => __('Short Cache Expiration', 'redco-optimizer'),
                    'description' => sprintf(__('Page cache expiration is set to %d seconds. Recommended: 3600+ seconds.', 'redco-optimizer'), $cache_expiration),
                    'severity' => 'medium',
                    'category' => 'modules',
                    'auto_fixable' => true,
                    'impact' => 'Frequent cache regeneration reduces performance',
                    'recommendation' => 'Increase cache expiration to at least 1 hour',
                    'fix_action' => 'optimize_cache_expiration'
                );
            }
        }

        // Check Critical Resource Optimizer configuration
        if (redco_is_module_enabled('critical-resource-optimizer')) {
            $measure_performance = redco_get_module_option('critical-resource-optimizer', 'measure_performance', false);
            if ($measure_performance) {
                $issues[] = array(
                    'id' => 'performance_measurement_enabled',
                    'title' => __('Performance Measurement Enabled', 'redco-optimizer'),
                    'description' => __('Performance measurement is enabled, which adds JavaScript overhead.', 'redco-optimizer'),
                    'severity' => 'medium',
                    'category' => 'modules',
                    'auto_fixable' => true,
                    'impact' => 'Additional JavaScript execution time',
                    'recommendation' => 'Disable performance measurement in production',
                    'fix_action' => 'disable_performance_measurement'
                );
            }
        }

        return $issues;
    }

    /**
     * Calculate health score based on issues
     */
    private function calculate_health_score($issues) {
        if (empty($issues)) {
            return 100;
        }

        $total_penalty = 0;
        foreach ($issues as $issue) {
            switch ($issue['severity']) {
                case 'critical':
                    $total_penalty += 20;
                    break;
                case 'high':
                    $total_penalty += 10;
                    break;
                case 'medium':
                    $total_penalty += 5;
                    break;
                case 'low':
                    $total_penalty += 2;
                    break;
            }
        }

        return max(0, 100 - $total_penalty);
    }

    /**
     * Calculate performance score based on issues
     */
    private function calculate_performance_score($issues) {
        if (empty($issues)) {
            return 100;
        }

        $performance_issues = array_filter($issues, function($issue) {
            return in_array($issue['category'], array('frontend', 'server', 'database'));
        });

        if (empty($performance_issues)) {
            return 95; // Minor deduction for non-performance issues
        }

        $total_penalty = 0;
        foreach ($performance_issues as $issue) {
            switch ($issue['severity']) {
                case 'critical':
                    $total_penalty += 25;
                    break;
                case 'high':
                    $total_penalty += 15;
                    break;
                case 'medium':
                    $total_penalty += 8;
                    break;
                case 'low':
                    $total_penalty += 3;
                    break;
            }
        }

        return max(0, 100 - $total_penalty);
    }

    /**
     * Generate recommendations based on scan results
     */
    private function generate_recommendations($results) {
        $recommendations = array();

        if ($results['health_score'] < 60) {
            $recommendations[] = array(
                'priority' => 'critical',
                'title' => 'Critical Health Issues Detected',
                'description' => 'Your site has critical health issues that need immediate attention.',
                'action' => 'Fix critical issues first, then run a comprehensive scan.'
            );
        }

        if ($results['performance_score'] < 70) {
            $recommendations[] = array(
                'priority' => 'high',
                'title' => 'Performance Optimization Needed',
                'description' => 'Your site performance can be significantly improved.',
                'action' => 'Enable page caching, lazy loading, and CSS/JS minification.'
            );
        }

        $auto_fixable_count = count(array_filter($results['issues'], function($issue) {
            return $issue['auto_fixable'];
        }));

        if ($auto_fixable_count > 0) {
            $recommendations[] = array(
                'priority' => 'medium',
                'title' => 'Auto-Fixable Issues Available',
                'description' => sprintf('%d issues can be automatically fixed.', $auto_fixable_count),
                'action' => 'Use the Auto-Fix feature to resolve these issues quickly.'
            );
        }

        return $recommendations;
    }

    /**
     * Scan PageSpeed issues (if API key is configured)
     */
    private function scan_pagespeed_issues() {
        $api_key = $this->settings['pagespeed_api_key'];
        if (empty($api_key)) {
            return array('issues' => array(), 'score' => 0);
        }

        $url = home_url();
        $api_url = Redco_API_Endpoints::get_pagespeed_url($url, $api_key, 'mobile', array('performance'));

        $response = wp_remote_get($api_url, array(
            'timeout' => Redco_API_Endpoints::get_timeout('pagespeed'),
            'headers' => Redco_API_Endpoints::get_default_headers()
        ));

        if (is_wp_error($response)) {
            return array('issues' => array(), 'score' => 0);
        }

        $data = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($data['lighthouseResult'])) {
            return array('issues' => array(), 'score' => 0);
        }

        $lighthouse = $data['lighthouseResult'];
        $score = round($lighthouse['categories']['performance']['score'] * 100);

        $issues = array();

        // Extract PageSpeed-specific issues
        if (isset($lighthouse['audits'])) {
            $critical_audits = array(
                'first-contentful-paint',
                'largest-contentful-paint',
                'cumulative-layout-shift',
                'first-input-delay'
            );

            foreach ($critical_audits as $audit_id) {
                if (isset($lighthouse['audits'][$audit_id]) &&
                    $lighthouse['audits'][$audit_id]['score'] < 0.9) {

                    $audit = $lighthouse['audits'][$audit_id];
                    $issues[] = array(
                        'id' => 'pagespeed_' . $audit_id,
                        'title' => $audit['title'],
                        'description' => $audit['description'],
                        'severity' => $audit['score'] < 0.5 ? 'critical' : 'high',
                        'category' => 'pagespeed',
                        'auto_fixable' => false,
                        'impact' => 'Poor Core Web Vitals score',
                        'recommendation' => isset($audit['details']['summary']['text']) ? $audit['details']['summary']['text'] : 'Optimize for better performance',
                        'fix_action' => 'optimize_' . str_replace('-', '_', $audit_id)
                    );
                }
            }
        }

        return array('issues' => $issues, 'score' => $score);
    }

    /**
     * Get latest WordPress version
     */
    private function get_latest_wp_version() {
        return $this->get_latest_wordpress_version();
    }

    /**
     * Get inactive plugins
     */
    private function get_inactive_plugins() {
        $all_plugins = get_plugins();
        $active_plugins = get_option('active_plugins', array());

        $inactive_plugins = array();
        foreach ($all_plugins as $plugin_file => $plugin_data) {
            if (!in_array($plugin_file, $active_plugins)) {
                $inactive_plugins[] = $plugin_file;
            }
        }

        return $inactive_plugins;
    }

    /**
     * Get total post revisions count
     */
    private function get_total_post_revisions() {
        global $wpdb;
        return (int) $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'revision'");
    }

    /**
     * Get spam comment count
     */
    private function get_spam_comment_count() {
        global $wpdb;
        return (int) $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_approved = 'spam'");
    }

    /**
     * Calculate health trend
     */
    private function calculate_health_trend() {
        $history = get_option('redco_diagnostic_health_history', array());

        if (count($history) < 2) {
            return 0; // No trend data
        }

        $recent_scores = array_slice($history, -5); // Last 5 scores
        $first_score = reset($recent_scores);
        $last_score = end($recent_scores);

        return $last_score - $first_score;
    }

    /**
     * Calculate Core Web Vitals score
     */
    private function calculate_core_vitals_score() {
        // This would integrate with real Core Web Vitals measurement
        // For now, return a calculated score based on performance factors
        $base_score = 85;

        // Adjust based on server response time
        $ttfb = $this->measure_ttfb();
        if ($ttfb > 600) {
            $base_score -= 15;
        }

        // Adjust based on optimization status
        if (!$this->is_compression_enabled()) {
            $base_score -= 10;
        }

        if (!$this->has_proper_cache_headers()) {
            $base_score -= 5;
        }

        return max(0, $base_score);
    }


}
