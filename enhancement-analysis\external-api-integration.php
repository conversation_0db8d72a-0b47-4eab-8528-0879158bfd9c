<?php
/**
 * External API Integration Manager for Redco Optimizer
 * 
 * Integrates with multiple performance testing APIs for comprehensive analysis
 */

class Redco_External_API_Manager {
    
    private $api_configs = array(
        'google_pagespeed' => array(
            'endpoint' => 'https://www.googleapis.com/pagespeed/v5/runPagespeed',
            'rate_limit' => 100, // requests per day
            'cache_duration' => 3600 // 1 hour
        ),
        'gtmetrix' => array(
            'endpoint' => 'https://gtmetrix.com/api/2.0/reports',
            'rate_limit' => 20, // requests per day for free tier
            'cache_duration' => 7200 // 2 hours
        ),
        'webpagetest' => array(
            'endpoint' => 'https://www.webpagetest.org/runtest.php',
            'rate_limit' => 200, // requests per day
            'cache_duration' => 3600 // 1 hour
        ),
        'pingdom' => array(
            'endpoint' => 'https://api.pingdom.com/api/3.1/checks',
            'rate_limit' => 1000, // requests per month
            'cache_duration' => 1800 // 30 minutes
        )
    );
    
    /**
     * Comprehensive multi-API performance analysis
     */
    public function run_comprehensive_analysis($url, $apis = array('google_pagespeed', 'gtmetrix')) {
        $results = array();
        
        foreach ($apis as $api) {
            if (!isset($this->api_configs[$api])) {
                continue;
            }
            
            // Check rate limits and cache
            if ($this->can_make_api_call($api)) {
                $results[$api] = $this->call_api($api, $url);
                $this->update_rate_limit_counter($api);
            } else {
                $results[$api] = $this->get_cached_result($api, $url);
            }
        }
        
        return $this->aggregate_api_results($results);
    }
    
    /**
     * Google PageSpeed Insights Integration
     */
    private function call_google_pagespeed($url, $api_key) {
        $endpoint = add_query_arg(array(
            'url' => $url,
            'key' => $api_key,
            'strategy' => 'mobile',
            'category' => array('performance', 'accessibility', 'best-practices', 'seo')
        ), $this->api_configs['google_pagespeed']['endpoint']);
        
        $response = wp_remote_get($endpoint, array('timeout' => 60));
        
        if (is_wp_error($response)) {
            return array('error' => $response->get_error_message());
        }
        
        $data = json_decode(wp_remote_retrieve_body($response), true);
        
        return $this->parse_pagespeed_results($data);
    }
    
    /**
     * GTmetrix API Integration
     */
    private function call_gtmetrix($url, $api_key, $username) {
        $auth = base64_encode($username . ':' . $api_key);
        
        // Start test
        $start_response = wp_remote_post($this->api_configs['gtmetrix']['endpoint'], array(
            'headers' => array(
                'Authorization' => 'Basic ' . $auth,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode(array(
                'url' => $url,
                'location' => 2, // Vancouver, Canada
                'browser' => 3   // Chrome
            )),
            'timeout' => 30
        ));
        
        if (is_wp_error($start_response)) {
            return array('error' => $start_response->get_error_message());
        }
        
        $start_data = json_decode(wp_remote_retrieve_body($start_response), true);
        $test_id = $start_data['data']['id'];
        
        // Poll for results
        $max_attempts = 30;
        $attempt = 0;
        
        do {
            sleep(10); // Wait 10 seconds between polls
            $attempt++;
            
            $result_response = wp_remote_get(
                $this->api_configs['gtmetrix']['endpoint'] . '/' . $test_id,
                array(
                    'headers' => array('Authorization' => 'Basic ' . $auth),
                    'timeout' => 30
                )
            );
            
            if (!is_wp_error($result_response)) {
                $result_data = json_decode(wp_remote_retrieve_body($result_response), true);
                
                if ($result_data['data']['state'] === 'completed') {
                    return $this->parse_gtmetrix_results($result_data);
                }
            }
            
        } while ($attempt < $max_attempts);
        
        return array('error' => 'Test timeout');
    }
    
    /**
     * WebPageTest API Integration
     */
    private function call_webpagetest($url, $api_key) {
        // Start test
        $start_endpoint = add_query_arg(array(
            'url' => $url,
            'k' => $api_key,
            'f' => 'json',
            'location' => 'Dulles:Chrome',
            'runs' => 3,
            'fvonly' => 1
        ), $this->api_configs['webpagetest']['endpoint']);
        
        $start_response = wp_remote_get($start_endpoint, array('timeout' => 30));
        
        if (is_wp_error($start_response)) {
            return array('error' => $start_response->get_error_message());
        }
        
        $start_data = json_decode(wp_remote_retrieve_body($start_response), true);
        
        if ($start_data['statusCode'] !== 200) {
            return array('error' => $start_data['statusText']);
        }
        
        $test_id = $start_data['data']['testId'];
        
        // Poll for results
        $max_attempts = 60;
        $attempt = 0;
        
        do {
            sleep(15); // Wait 15 seconds between polls
            $attempt++;
            
            $result_endpoint = "https://www.webpagetest.org/jsonResult.php?test={$test_id}";
            $result_response = wp_remote_get($result_endpoint, array('timeout' => 30));
            
            if (!is_wp_error($result_response)) {
                $result_data = json_decode(wp_remote_retrieve_body($result_response), true);
                
                if ($result_data['statusCode'] === 200) {
                    return $this->parse_webpagetest_results($result_data);
                }
            }
            
        } while ($attempt < $max_attempts);
        
        return array('error' => 'Test timeout');
    }
    
    /**
     * Parse Google PageSpeed results
     */
    private function parse_pagespeed_results($data) {
        if (!isset($data['lighthouseResult'])) {
            return array('error' => 'Invalid PageSpeed response');
        }
        
        $lighthouse = $data['lighthouseResult'];
        
        return array(
            'performance_score' => $lighthouse['categories']['performance']['score'] * 100,
            'accessibility_score' => $lighthouse['categories']['accessibility']['score'] * 100,
            'best_practices_score' => $lighthouse['categories']['best-practices']['score'] * 100,
            'seo_score' => $lighthouse['categories']['seo']['score'] * 100,
            'metrics' => array(
                'first_contentful_paint' => $lighthouse['audits']['first-contentful-paint']['numericValue'],
                'largest_contentful_paint' => $lighthouse['audits']['largest-contentful-paint']['numericValue'],
                'first_input_delay' => $lighthouse['audits']['max-potential-fid']['numericValue'],
                'cumulative_layout_shift' => $lighthouse['audits']['cumulative-layout-shift']['numericValue'],
                'speed_index' => $lighthouse['audits']['speed-index']['numericValue'],
                'time_to_interactive' => $lighthouse['audits']['interactive']['numericValue']
            ),
            'opportunities' => $this->extract_pagespeed_opportunities($lighthouse['audits']),
            'diagnostics' => $this->extract_pagespeed_diagnostics($lighthouse['audits'])
        );
    }
    
    /**
     * Parse GTmetrix results
     */
    private function parse_gtmetrix_results($data) {
        $report = $data['data'];
        
        return array(
            'gtmetrix_grade' => $report['gtmetrix_grade'],
            'performance_score' => $report['lighthouse_performance_score'],
            'structure_score' => $report['lighthouse_structure_score'],
            'page_load_time' => $report['page_load_time'],
            'page_size' => $report['page_size'],
            'requests' => $report['requests'],
            'metrics' => array(
                'ttfb' => $report['ttfb'],
                'redirect_duration' => $report['redirect_duration'],
                'connect_duration' => $report['connect_duration'],
                'backend_duration' => $report['backend_duration'],
                'first_paint' => $report['first_paint'],
                'dom_interactive' => $report['dom_interactive_time'],
                'dom_content_loaded' => $report['dom_content_loaded_time'],
                'onload_time' => $report['onload_time']
            ),
            'recommendations' => $this->extract_gtmetrix_recommendations($report)
        );
    }
    
    /**
     * Parse WebPageTest results
     */
    private function parse_webpagetest_results($data) {
        $run = $data['data']['runs']['1']['firstView'];
        
        return array(
            'load_time' => $run['loadTime'],
            'ttfb' => $run['TTFB'],
            'start_render' => $run['render'],
            'speed_index' => $run['SpeedIndex'],
            'dom_elements' => $run['domElements'],
            'bytes_in' => $run['bytesIn'],
            'requests' => $run['requests'],
            'metrics' => array(
                'first_byte' => $run['TTFB'],
                'start_render' => $run['render'],
                'first_contentful_paint' => $run['firstContentfulPaint'],
                'speed_index' => $run['SpeedIndex'],
                'largest_contentful_paint' => $run['largestContentfulPaint'],
                'cumulative_layout_shift' => $run['cumulativeLayoutShift'],
                'total_blocking_time' => $run['totalBlockingTime']
            ),
            'breakdown' => array(
                'html' => $run['breakdown']['html'],
                'js' => $run['breakdown']['js'],
                'css' => $run['breakdown']['css'],
                'image' => $run['breakdown']['image'],
                'font' => $run['breakdown']['font']
            )
        );
    }
    
    /**
     * Aggregate results from multiple APIs
     */
    private function aggregate_api_results($results) {
        $aggregated = array(
            'overall_score' => 0,
            'performance_metrics' => array(),
            'recommendations' => array(),
            'api_sources' => array_keys($results)
        );
        
        $score_count = 0;
        $total_score = 0;
        
        foreach ($results as $api => $result) {
            if (isset($result['error'])) {
                continue;
            }
            
            // Aggregate performance scores
            if (isset($result['performance_score'])) {
                $total_score += $result['performance_score'];
                $score_count++;
            }
            
            // Merge recommendations
            if (isset($result['recommendations'])) {
                $aggregated['recommendations'][$api] = $result['recommendations'];
            }
            
            // Store API-specific data
            $aggregated['api_data'][$api] = $result;
        }
        
        if ($score_count > 0) {
            $aggregated['overall_score'] = round($total_score / $score_count);
        }
        
        return $aggregated;
    }
    
    /**
     * Check if API call can be made (rate limiting)
     */
    private function can_make_api_call($api) {
        $today = date('Y-m-d');
        $call_count = get_option("redco_api_calls_{$api}_{$today}", 0);
        
        return $call_count < $this->api_configs[$api]['rate_limit'];
    }
    
    /**
     * Update rate limit counter
     */
    private function update_rate_limit_counter($api) {
        $today = date('Y-m-d');
        $option_name = "redco_api_calls_{$api}_{$today}";
        $current_count = get_option($option_name, 0);
        
        update_option($option_name, $current_count + 1);
    }
    
    /**
     * Get cached API result
     */
    private function get_cached_result($api, $url) {
        $cache_key = "redco_api_cache_{$api}_" . md5($url);
        return get_transient($cache_key);
    }
    
    /**
     * Cache API result
     */
    private function cache_result($api, $url, $result) {
        $cache_key = "redco_api_cache_{$api}_" . md5($url);
        $cache_duration = $this->api_configs[$api]['cache_duration'];
        
        set_transient($cache_key, $result, $cache_duration);
    }
}
