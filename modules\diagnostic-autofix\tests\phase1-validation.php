<?php
/**
 * Phase 1 Enhancement Validation Script
 * 
 * Comprehensive testing for all Phase 1 features while preserving existing functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Phase1_Validator {
    
    private $test_results = array();
    private $existing_functionality_tests = array();
    private $enhancement_tests = array();
    
    /**
     * Run comprehensive Phase 1 validation
     */
    public function run_validation() {
        $this->test_results = array(
            'timestamp' => current_time('mysql'),
            'existing_functionality' => array(),
            'phase1_enhancements' => array(),
            'overall_status' => 'pending',
            'summary' => array()
        );
        
        // Test existing functionality first
        $this->test_existing_functionality();
        
        // Test Phase 1 enhancements
        $this->test_phase1_enhancements();
        
        // Generate summary
        $this->generate_summary();
        
        return $this->test_results;
    }
    
    /**
     * Test existing functionality to ensure no regressions
     */
    private function test_existing_functionality() {
        $tests = array(
            'diagnostic_scan' => 'Test diagnostic scan functionality',
            'auto_fix_button' => 'Test Apply Auto-fixes button',
            'individual_fixes' => 'Test individual fix application',
            'counter_updates' => 'Test fix counter updates',
            'nonce_security' => 'Test nonce security validation',
            'ajax_endpoints' => 'Test existing AJAX endpoints',
            'error_handling' => 'Test existing error handling'
        );
        
        foreach ($tests as $test_key => $test_description) {
            $this->test_results['existing_functionality'][$test_key] = $this->run_existing_test($test_key, $test_description);
        }
    }
    
    /**
     * Test Phase 1 enhancements
     */
    private function test_phase1_enhancements() {
        $tests = array(
            'database_schema' => 'Test database schema updates',
            'tiered_fix_system' => 'Test tiered fix classification',
            'preview_system' => 'Test fix preview functionality',
            'scheduling_system' => 'Test fix scheduling',
            'enhanced_ui' => 'Test enhanced UI components',
            'backup_system' => 'Test enhanced backup system',
            'safety_indicators' => 'Test safety indicators',
            'category_filtering' => 'Test category filtering'
        );
        
        foreach ($tests as $test_key => $test_description) {
            $this->test_results['phase1_enhancements'][$test_key] = $this->run_enhancement_test($test_key, $test_description);
        }
    }
    
    /**
     * Run individual existing functionality test
     */
    private function run_existing_test($test_key, $description) {
        $test_result = array(
            'description' => $description,
            'status' => 'pending',
            'details' => array(),
            'errors' => array(),
            'execution_time' => 0
        );
        
        $start_time = microtime(true);
        
        try {
            switch ($test_key) {
                case 'diagnostic_scan':
                    $test_result = $this->test_diagnostic_scan($test_result);
                    break;
                    
                case 'auto_fix_button':
                    $test_result = $this->test_auto_fix_button($test_result);
                    break;
                    
                case 'individual_fixes':
                    $test_result = $this->test_individual_fixes($test_result);
                    break;
                    
                case 'counter_updates':
                    $test_result = $this->test_counter_updates($test_result);
                    break;
                    
                case 'nonce_security':
                    $test_result = $this->test_nonce_security($test_result);
                    break;
                    
                case 'ajax_endpoints':
                    $test_result = $this->test_ajax_endpoints($test_result);
                    break;
                    
                case 'error_handling':
                    $test_result = $this->test_error_handling($test_result);
                    break;
            }
            
        } catch (Exception $e) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = $e->getMessage();
        }
        
        $test_result['execution_time'] = round((microtime(true) - $start_time) * 1000, 2);
        
        return $test_result;
    }
    
    /**
     * Test diagnostic scan functionality
     */
    private function test_diagnostic_scan($test_result) {
        // Check if diagnostic class exists and is properly initialized
        if (!class_exists('Redco_Diagnostic_AutoFix')) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'Redco_Diagnostic_AutoFix class not found';
            return $test_result;
        }
        
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Test scan method exists
        if (!method_exists($diagnostic, 'run_diagnostic_scan')) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'run_diagnostic_scan method not found';
            return $test_result;
        }
        
        // Test that scan returns expected structure
        $scan_result = $diagnostic->run_diagnostic_scan();
        
        if (!is_array($scan_result) || !isset($scan_result['issues'])) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'Scan result does not have expected structure';
            return $test_result;
        }
        
        $test_result['status'] = 'passed';
        $test_result['details']['issues_found'] = count($scan_result['issues']);
        
        return $test_result;
    }
    
    /**
     * Test auto-fix button functionality
     */
    private function test_auto_fix_button($test_result) {
        // Check if AJAX handler exists
        if (!has_action('wp_ajax_redco_apply_auto_fixes')) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'Auto-fix AJAX handler not registered';
            return $test_result;
        }
        
        // Check if JavaScript file exists
        $js_file = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/assets/diagnostic-autofix.js';
        if (!file_exists($js_file)) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'Diagnostic JavaScript file not found';
            return $test_result;
        }
        
        // Check if button functionality is preserved in JavaScript
        $js_content = file_get_contents($js_file);
        if (strpos($js_content, 'apply-auto-fixes') === false) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'Auto-fix button handler not found in JavaScript';
            return $test_result;
        }
        
        $test_result['status'] = 'passed';
        $test_result['details']['ajax_handler'] = 'registered';
        $test_result['details']['js_file'] = 'exists';
        
        return $test_result;
    }
    
    /**
     * Test Phase 1 enhancement
     */
    private function run_enhancement_test($test_key, $description) {
        $test_result = array(
            'description' => $description,
            'status' => 'pending',
            'details' => array(),
            'errors' => array(),
            'execution_time' => 0
        );
        
        $start_time = microtime(true);
        
        try {
            switch ($test_key) {
                case 'database_schema':
                    $test_result = $this->test_database_schema($test_result);
                    break;
                    
                case 'tiered_fix_system':
                    $test_result = $this->test_tiered_fix_system($test_result);
                    break;
                    
                case 'preview_system':
                    $test_result = $this->test_preview_system($test_result);
                    break;
                    
                case 'scheduling_system':
                    $test_result = $this->test_scheduling_system($test_result);
                    break;
                    
                case 'enhanced_ui':
                    $test_result = $this->test_enhanced_ui($test_result);
                    break;
                    
                case 'backup_system':
                    $test_result = $this->test_backup_system($test_result);
                    break;
            }
            
        } catch (Exception $e) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = $e->getMessage();
        }
        
        $test_result['execution_time'] = round((microtime(true) - $start_time) * 1000, 2);
        
        return $test_result;
    }
    
    /**
     * Test database schema updates
     */
    private function test_database_schema($test_result) {
        global $wpdb;
        
        // Check if new tables exist
        $tables_to_check = array(
            $wpdb->prefix . 'redco_scheduled_fixes',
            $wpdb->prefix . 'redco_fix_history'
        );
        
        foreach ($tables_to_check as $table) {
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'");
            if ($table_exists !== $table) {
                $test_result['status'] = 'failed';
                $test_result['errors'][] = "Table {$table} does not exist";
                return $test_result;
            }
            
            $test_result['details']['tables_created'][] = $table;
        }
        
        // Check database version option
        $db_version = get_option('redco_optimizer_db_version');
        if (version_compare($db_version, REDCO_OPTIMIZER_DB_VERSION, '<')) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'Database version not updated correctly';
            return $test_result;
        }
        
        $test_result['status'] = 'passed';
        $test_result['details']['db_version'] = $db_version;
        
        return $test_result;
    }
    
    /**
     * Test tiered fix system
     */
    private function test_tiered_fix_system($test_result) {
        // Check if tiered fix class exists
        $tiered_file = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/safety/class-tiered-fix-system.php';
        if (!file_exists($tiered_file)) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'Tiered fix system file not found';
            return $test_result;
        }
        
        require_once $tiered_file;
        
        if (!class_exists('Redco_Tiered_Fix_System')) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'Redco_Tiered_Fix_System class not found';
            return $test_result;
        }
        
        // Test AJAX handlers are registered
        if (!has_action('wp_ajax_redco_get_fix_tiers')) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'Tiered fix AJAX handlers not registered';
            return $test_result;
        }
        
        $test_result['status'] = 'passed';
        $test_result['details']['class_loaded'] = true;
        $test_result['details']['ajax_handlers'] = 'registered';
        
        return $test_result;
    }
    
    /**
     * Test enhanced UI components
     */
    private function test_enhanced_ui($test_result) {
        // Check if enhanced UI JavaScript exists
        $enhanced_ui_file = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/assets/ui/enhanced-ui.js';
        if (!file_exists($enhanced_ui_file)) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'Enhanced UI JavaScript file not found';
            return $test_result;
        }
        
        // Check if CSS enhancements are present
        $css_file = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/assets/diagnostic-autofix.css';
        $css_content = file_get_contents($css_file);
        
        if (strpos($css_content, 'PHASE 1 ENHANCEMENT STYLES') === false) {
            $test_result['status'] = 'failed';
            $test_result['errors'][] = 'Phase 1 CSS enhancements not found';
            return $test_result;
        }
        
        $test_result['status'] = 'passed';
        $test_result['details']['enhanced_js'] = 'exists';
        $test_result['details']['enhanced_css'] = 'exists';
        
        return $test_result;
    }
    
    /**
     * Generate validation summary
     */
    private function generate_summary() {
        $total_tests = 0;
        $passed_tests = 0;
        $failed_tests = 0;
        
        // Count existing functionality tests
        foreach ($this->test_results['existing_functionality'] as $test) {
            $total_tests++;
            if ($test['status'] === 'passed') {
                $passed_tests++;
            } else {
                $failed_tests++;
            }
        }
        
        // Count enhancement tests
        foreach ($this->test_results['phase1_enhancements'] as $test) {
            $total_tests++;
            if ($test['status'] === 'passed') {
                $passed_tests++;
            } else {
                $failed_tests++;
            }
        }
        
        $this->test_results['summary'] = array(
            'total_tests' => $total_tests,
            'passed_tests' => $passed_tests,
            'failed_tests' => $failed_tests,
            'success_rate' => $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 2) : 0
        );
        
        // Determine overall status
        if ($failed_tests === 0) {
            $this->test_results['overall_status'] = 'passed';
        } elseif ($passed_tests > $failed_tests) {
            $this->test_results['overall_status'] = 'partial';
        } else {
            $this->test_results['overall_status'] = 'failed';
        }
    }
    
    /**
     * Get validation report
     */
    public function get_validation_report() {
        return $this->test_results;
    }
    
    /**
     * Display validation results
     */
    public function display_validation_results() {
        if (empty($this->test_results)) {
            echo '<div class="notice notice-warning"><p>No validation results available. Run validation first.</p></div>';
            return;
        }
        
        $summary = $this->test_results['summary'];
        $status_class = $this->test_results['overall_status'] === 'passed' ? 'notice-success' : 
                       ($this->test_results['overall_status'] === 'partial' ? 'notice-warning' : 'notice-error');
        
        echo '<div class="notice ' . $status_class . '">';
        echo '<h3>Phase 1 Validation Results</h3>';
        echo '<p><strong>Overall Status:</strong> ' . ucfirst($this->test_results['overall_status']) . '</p>';
        echo '<p><strong>Success Rate:</strong> ' . $summary['success_rate'] . '% (' . $summary['passed_tests'] . '/' . $summary['total_tests'] . ' tests passed)</p>';
        echo '</div>';
        
        // Display detailed results
        echo '<div class="redco-validation-details">';
        echo '<h4>Existing Functionality Tests</h4>';
        $this->display_test_category($this->test_results['existing_functionality']);
        
        echo '<h4>Phase 1 Enhancement Tests</h4>';
        $this->display_test_category($this->test_results['phase1_enhancements']);
        echo '</div>';
    }
    
    /**
     * Display test category results
     */
    private function display_test_category($tests) {
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr><th>Test</th><th>Status</th><th>Details</th><th>Execution Time</th></tr></thead>';
        echo '<tbody>';
        
        foreach ($tests as $test_key => $test) {
            $status_icon = $test['status'] === 'passed' ? '✅' : '❌';
            $details = !empty($test['errors']) ? implode(', ', $test['errors']) : 
                      (!empty($test['details']) ? json_encode($test['details']) : 'No details');
            
            echo '<tr>';
            echo '<td>' . esc_html($test['description']) . '</td>';
            echo '<td>' . $status_icon . ' ' . ucfirst($test['status']) . '</td>';
            echo '<td>' . esc_html($details) . '</td>';
            echo '<td>' . $test['execution_time'] . 'ms</td>';
            echo '</tr>';
        }
        
        echo '</tbody></table>';
    }
}
