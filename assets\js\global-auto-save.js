/**
 * Global Auto-Save System for Redco Optimizer
 * 
 * Comprehensive auto-save functionality with:
 * - Automatic form detection across all plugin pages
 * - Network failure handling with retry mechanisms
 * - Memory-efficient operations
 * - Static UI feedback with accessibility support
 * - Navigation protection
 */

(function($) {
    'use strict';

    // Global auto-save namespace
    window.RedcoGlobalAutoSave = {
        
        // Configuration
        config: {
            saveInterval: 45000, // 45 seconds
            debounceDelay: 3000, // 3 seconds after last change
            maxRetries: 3,
            retryDelay: 2000, // 2 seconds
            batchSize: 5,
            networkTimeout: 15000, // 15 seconds
            memoryThreshold: 0.8 // 80%
        },

        // State management
        state: {
            isInitialized: false,
            saveTimers: new Map(),
            pendingSaves: new Map(),
            retryAttempts: new Map(),
            isOnline: navigator.onLine,
            // hasUnsavedChanges removed - auto-save handles changes immediately
            lastSaveTime: null,
            formData: new Map(),
            toastTimer: null
        },

        // Initialize the global auto-save system
        init: function() {
            if (this.state.isInitialized) {
                return;
            }

            console.log('🔧 Global Auto-Save: Initializing system');

            // Disable old settings-script.js auto-save to prevent conflicts
            this.disableOldAutoSave();

            this.setupFormDetection();
            this.setupEventListeners();
            this.setupNetworkMonitoring();
            this.setupNavigationProtection();
            this.setupPeriodicSave();
            this.setupUI();
            this.loadSavedState();

            this.state.isInitialized = true;
            console.log('🔧 Global Auto-Save: System initialized successfully');
        },

        // Disable old auto-save system to prevent conflicts
        disableOldAutoSave: function() {
            console.log('🔧 Global Auto-Save: Disabling old auto-save system to prevent conflicts');

            // Remove event handlers from old settings-script.js
            $(document).off('change input keyup', '.redco-settings-form input, .redco-settings-form select, .redco-settings-form textarea');

            // Set a flag to indicate new system is active
            window.redcoNewAutoSaveActive = true;

            console.log('🔧 Global Auto-Save: Old auto-save system disabled');
        },

        // Detect and setup auto-save for all forms
        setupFormDetection: function() {
            const self = this;

            console.log('🔧 Global Auto-Save: Starting form detection');

            // Find all Redco Optimizer forms with multiple selectors
            const forms = $('.redco-module-form, form[data-module], .redco-settings-form, form.redco-form');

            console.log('🔧 Global Auto-Save: Found forms', {
                count: forms.length,
                selectors_checked: '.redco-module-form, form[data-module], .redco-settings-form, form.redco-form'
            });

            if (forms.length === 0) {
                console.log('🔧 Global Auto-Save: No forms found with standard selectors, checking page context');

                // If no forms found, check if we're on the right page
                const isRedcoPage = window.location.href.includes('redco-optimizer') ||
                                   $('.redco-optimizer-admin').length > 0 ||
                                   $('.redco-module-tab').length > 0;

                console.log('🔧 Global Auto-Save: Page context check', {
                    url_includes_redco: window.location.href.includes('redco-optimizer'),
                    has_admin_class: $('.redco-optimizer-admin').length > 0,
                    has_module_tab: $('.redco-module-tab').length > 0,
                    is_redco_page: isRedcoPage
                });

                if (isRedcoPage) {
                    console.log('🔧 Global Auto-Save: Scanning all forms for Redco indicators');

                    // Look for any forms that might be Redco forms but missing the right classes
                    $('form').each(function() {
                        const $form = $(this);
                        const action = $form.attr('action') || '';
                        const id = $form.attr('id') || '';
                        const classes = $form.attr('class') || '';

                        console.log('🔧 Global Auto-Save: Checking form', {
                            action: action,
                            id: id,
                            classes: classes
                        });

                        // Check if this looks like a Redco form
                        if (action.includes('redco') || id.includes('redco') || classes.includes('redco')) {
                            console.log('🔧 Global Auto-Save: Found potential Redco form, extracting module');
                            const module = self.extractModuleFromForm($form);
                            if (module) {
                                console.log('🔧 Global Auto-Save: Setting up auto-save for form with module:', module);
                                self.setupFormAutoSave($form, module);
                            } else {
                                console.log('❌ Global Auto-Save: Could not extract module from form');
                            }
                        }
                    });
                }
            } else {
                console.log('🔧 Global Auto-Save: Processing found forms');

                forms.each(function() {
                    const $form = $(this);
                    const module = $form.data('module') || self.extractModuleFromForm($form);

                    console.log('🔧 Global Auto-Save: Processing form', {
                        form_classes: $form.attr('class'),
                        data_module: $form.data('module'),
                        extracted_module: module
                    });

                    if (module) {
                        console.log('🔧 Global Auto-Save: Setting up auto-save for form with module:', module);
                        self.setupFormAutoSave($form, module);
                    } else {
                        console.log('❌ Global Auto-Save: No module found for form');
                    }
                });
            }

            // Setup auto-detection for dynamically loaded forms
            this.setupDynamicFormDetection();
        },

        // Setup auto-save for a specific form
        setupFormAutoSave: function($form, module) {
            const self = this;

            // Universal field detection - find ALL interactive form elements
            const $fields = this.detectFormFields($form);

            // Setup auto-save for detected fields
            $fields.each(function() {
                const $field = $(this);
                const fieldName = $field.attr('name');

                if (fieldName && !$field.hasClass('redco-auto-save-enabled')) {
                    $field.addClass('redco-auto-save-enabled');
                    self.bindFieldEvents($field, module);
                }
            });

            // Store form reference
            this.state.formData.set(module, {
                form: $form,
                fields: $fields,
                lastSaved: null
            });
        },

        // Universal field detection - automatically finds ANY interactive form element
        detectFormFields: function($form) {
            const self = this;
            let $fields = $();

            // Find all elements that could be form fields
            $form.find('*').each(function() {
                const $element = $(this);

                // Check if element is interactive (can trigger change events)
                if (self.isInteractiveFormElement($element)) {
                    $fields = $fields.add($element);
                }
            });

            return $fields;
        },

        // Check if an element is an interactive form element
        isInteractiveFormElement: function($element) {
            const tagName = $element.prop('tagName').toLowerCase();
            const type = $element.attr('type');

            // Standard form elements
            if (tagName === 'input' || tagName === 'select' || tagName === 'textarea') {
                return true;
            }

            // Elements with contenteditable
            if ($element.attr('contenteditable') === 'true') {
                return true;
            }

            // Elements that have a name attribute and can trigger change events
            if ($element.attr('name') && this.canTriggerChangeEvents($element)) {
                return true;
            }

            // Elements with data attributes indicating they're form controls
            if ($element.data('field') || $element.data('setting') || $element.data('value')) {
                return true;
            }

            // Threshold preset buttons (Lazy Load module)
            if ($element.hasClass('threshold-preset')) {
                return true;
            }

            // Threshold sliders (even without name attribute)
            if ($element.hasClass('threshold-slider') || $element.attr('id') === 'threshold-slider') {
                return true;
            }

            // Elements with ARIA roles indicating form controls
            const role = $element.attr('role');
            if (role && ['slider', 'spinbutton', 'textbox', 'combobox', 'listbox', 'checkbox', 'radio', 'switch'].includes(role)) {
                return true;
            }

            return false;
        },

        // Check if element can trigger change events
        canTriggerChangeEvents: function($element) {
            try {
                // Test if element supports addEventListener for change events
                const hasChangeEvent = 'onchange' in $element[0] || 'oninput' in $element[0];

                // Check if element has event handlers that suggest it's interactive
                const hasEventHandlers = $element.data('events') ||
                                        $element.attr('onclick') ||
                                        $element.attr('onchange') ||
                                        $element.attr('oninput');

                return hasChangeEvent || hasEventHandlers;
            } catch (e) {
                return false;
            }
        },

        // Single persistent toast notification system
        showToast: function(message, type, duration) {
            const self = this;
            type = type || 'info';
            duration = duration || 4000;

            // Create toast container if it doesn't exist
            if ($('.redco-toast-container').length === 0) {
                $('body').append('<div class="redco-toast-container" aria-live="polite" aria-label="Auto-save notifications"></div>');
            }

            const $container = $('.redco-toast-container');

            // Check if persistent toast already exists
            let $toast = $container.find('#redco-persistent-toast');

            if ($toast.length === 0) {
                // Create the persistent toast
                $toast = $(`
                    <div class="redco-toast redco-toast-${type}" id="redco-persistent-toast" role="alert" aria-live="assertive">
                        <div class="toast-content">
                            <span class="toast-icon" aria-hidden="true"></span>
                            <span class="toast-message">${message}</span>
                            <button class="toast-close" aria-label="Close notification" type="button">&times;</button>
                        </div>
                    </div>
                `);

                // Add toast to container
                $container.append($toast);

                // Show immediately (no delay)
                $toast.addClass('show');

                // Handle manual close
                $toast.find('.toast-close').on('click', () => {
                    self.hideToast();
                });

            } else {
                // Update existing toast
                this.updateToast($toast, message, type);
            }

            // Clear any existing auto-hide timer
            if (this.state.toastTimer) {
                clearTimeout(this.state.toastTimer);
                this.state.toastTimer = null;
            }

            // Set auto-hide timer only for success/error states (not for saving state)
            if (type === 'success' || type === 'error') {
                this.state.toastTimer = setTimeout(() => {
                    this.hideToast();
                }, duration);
            }
        },

        // Update existing toast with new message and type
        updateToast: function($toast, message, type) {
            // Remove existing type classes
            $toast.removeClass('redco-toast-info redco-toast-success redco-toast-warning redco-toast-error');

            // Add new type class
            $toast.addClass('redco-toast-' + type);

            // Update message
            $toast.find('.toast-message').text(message);

            // Add update animation
            $toast.addClass('updating');
            setTimeout(() => {
                $toast.removeClass('updating');
            }, 200);
        },

        // Hide the persistent toast
        hideToast: function() {
            const $toast = $('#redco-persistent-toast');
            if ($toast.length > 0) {
                $toast.removeClass('show');
                setTimeout(() => {
                    $toast.remove();
                }, 300);
            }

            // Clear timer
            if (this.state.toastTimer) {
                clearTimeout(this.state.toastTimer);
                this.state.toastTimer = null;
            }
        },

        // Universal event binding for any form field
        bindFieldEvents: function($field, module) {
            const self = this;

            // Special handling for threshold controls
            if ($field.hasClass('threshold-preset')) {
                $field.on('click', function(e) {
                    const value = $(this).data('value');
                    // Update the actual threshold input field
                    const $thresholdInput = $('#threshold');
                    if ($thresholdInput.length > 0) {
                        $thresholdInput.val(value).trigger('change');
                    }
                    // Trigger auto-save for this control
                    self.scheduleAutoSave($(this), module);
                });
                return; // Don't bind universal events for preset buttons
            }

            if ($field.hasClass('threshold-slider') || $field.attr('id') === 'threshold-slider') {
                $field.on('input change', function(e) {
                    const value = $(this).val();
                    // Update the actual threshold input field
                    const $thresholdInput = $('#threshold');
                    if ($thresholdInput.length > 0) {
                        $thresholdInput.val(value);
                    }
                    // Trigger auto-save for this control
                    self.scheduleAutoSave($(this), module);
                });
                return; // Don't bind universal events for threshold slider
            }

            // Universal event detection - bind to all possible change events
            const universalEvents = [
                'input', 'change', 'click', 'keyup', 'blur',
                'focus', 'select', 'paste', 'cut'
            ];

            // Bind universal change handler with debouncing
            universalEvents.forEach(eventType => {
                $field.on(eventType, function(e) {
                    // Only trigger auto-save for events that actually change values
                    if (self.isValueChangeEvent(e, $(this))) {
                        self.scheduleAutoSave($(this), module);
                    }
                });
            });

            // Immediate save on blur for critical fields
            $field.on('blur', function() {
                if (self.isCriticalField($(this))) {
                    self.performImmediateSave($(this), module);
                }
            });
        },

        // Check if an event represents a value change
        isValueChangeEvent: function(event, $field) {
            const eventType = event.type;
            const tagName = $field.prop('tagName').toLowerCase();
            const inputType = $field.attr('type');

            // Always trigger for these events
            if (['change', 'input'].includes(eventType)) {
                return true;
            }

            // Click events for checkboxes, radio buttons, and buttons
            if (eventType === 'click') {
                if (inputType === 'checkbox' || inputType === 'radio' ||
                    tagName === 'button' || $field.attr('role') === 'button') {
                    return true;
                }
            }

            // Keyup for text inputs (but debounced)
            if (eventType === 'keyup' &&
                (tagName === 'input' || tagName === 'textarea' || $field.attr('contenteditable'))) {
                return true;
            }

            // Paste/cut events for text fields
            if (['paste', 'cut'].includes(eventType) &&
                (tagName === 'input' || tagName === 'textarea' || $field.attr('contenteditable'))) {
                return true;
            }

            return false;
        },

        // Schedule auto-save with debouncing
        scheduleAutoSave: function($field, module) {
            const fieldKey = $field.attr('name') || $field.attr('id');
            const timerKey = module + '_' + fieldKey;

            // Show toast immediately when field change is detected
            this.showToast('Saving changes...', 'info');

            // Clear existing timer
            if (this.state.saveTimers.has(timerKey)) {
                clearTimeout(this.state.saveTimers.get(timerKey));
            }

            // Set new timer (debouncing happens AFTER toast is shown)
            const timer = setTimeout(() => {
                this.performFieldSave($field, module);
            }, this.config.debounceDelay);

            this.state.saveTimers.set(timerKey, timer);
        },

        // Perform auto-save for a specific field
        performFieldSave: function($field, module) {
            const self = this;
            let fieldName = $field.attr('name');
            const fieldValue = this.getFieldValue($field);

            console.log('🔧 Global Auto-Save: performFieldSave called', {
                field_name: fieldName,
                field_value: fieldValue,
                module: module,
                field_element: $field[0],
                field_tag: $field.prop('tagName'),
                field_type: $field.attr('type'),
                field_classes: $field.attr('class')
            });

            // Special handling for threshold controls that don't have name attributes
            if (!fieldName) {
                if ($field.hasClass('threshold-preset') || $field.hasClass('threshold-slider') || $field.attr('id') === 'threshold-slider') {
                    fieldName = 'settings[threshold]'; // Map to the actual threshold field
                    console.log('🔧 Global Auto-Save: Mapped special control to threshold field');
                } else {
                    console.log('❌ Global Auto-Save: No field name and not a special control, aborting');
                    return; // No field name and not a special control
                }
            }

            // Check if value has changed
            const lastValue = this.getLastSavedValue(module, fieldName);
            if (lastValue === fieldValue) {
                // No change - hide the toast since no save is needed
                console.log('🔧 Global Auto-Save: No change detected, skipping save', {
                    current: fieldValue,
                    last: lastValue
                });
                this.hideToast();
                return;
            }

            console.log('🔧 Global Auto-Save: Value changed, proceeding with save', {
                current: fieldValue,
                last: lastValue,
                module: module,
                field_name: fieldName
            });

            // Toast is already shown from scheduleAutoSave - just proceed with save

            // Prepare save data
            const saveData = {
                action: 'redco_global_auto_save',
                nonce: redcoAjax.global_auto_save_nonce || redcoAjax.nonce,
                module: module,
                field_name: fieldName,
                field_value: fieldValue
            };

            console.log('🔧 Global Auto-Save: Sending AJAX request', saveData);

            // Perform AJAX save
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: saveData,
                timeout: this.config.networkTimeout,
                success: function(response) {
                    // DEBUG: Log response details
                    console.log('📥 Auto-save AJAX Response:', {
                        module: module,
                        field: fieldName,
                        success: response.success,
                        data: response.data,
                        full_response: response
                    });

                    if (response.success) {
                        self.handleSaveSuccess($field, module, fieldName, fieldValue, response.data);
                    } else {
                        console.error('❌ Auto-save failed with response:', response);
                        console.error('❌ Error details:', {
                            message: response.data?.message || 'No message',
                            code: response.data?.code || 'No code',
                            debug: response.data?.debug || 'No debug info'
                        });
                        self.handleSaveError($field, module, fieldName, response.data);
                    }
                },
                error: function(xhr, status, error) {
                    // DEBUG: Log detailed error information
                    console.error('🚨 Auto-save AJAX Error:', {
                        module: module,
                        field: fieldName,
                        status: status,
                        error: error,
                        xhr_status: xhr.status,
                        xhr_response: xhr.responseText,
                        xhr_headers: xhr.getAllResponseHeaders()
                    });

                    self.handleSaveError($field, module, fieldName, {
                        message: error,
                        code: status,
                        xhr_status: xhr.status,
                        xhr_response: xhr.responseText
                    });
                }
            });
        },

        // Handle successful save
        handleSaveSuccess: function($field, module, fieldName, fieldValue, responseData) {
            // Check if this was a queued save
            if (responseData.queued) {
                this.showToast('Save queued for processing due to high memory usage', 'warning', 5000);

                // Schedule status check for queued item
                setTimeout(() => {
                    this.checkQueuedSaveStatus($field, module, fieldName);
                }, 5000);

                return;
            }

            // Store saved value
            this.setLastSavedValue(module, fieldName, fieldValue);

            // Clear retry attempts
            const retryKey = module + '_' + fieldName;
            this.state.retryAttempts.delete(retryKey);

            // Update last save time
            this.state.lastSaveTime = new Date();

            // Show success toast notification
            this.showToast('Settings saved successfully', 'success', 3000);

            // Auto-save complete - no need to track unsaved changes
        },

        // Legacy function - no longer needed with immediate auto-save
        checkAllSavesComplete: function() {
            // No action needed - auto-save handles changes immediately
        },

        // Handle save error with retry logic and memory management
        handleSaveError: function($field, module, fieldName, errorData) {
            const retryKey = module + '_' + fieldName;
            const currentAttempts = this.state.retryAttempts.get(retryKey) || 0;

            // Handle memory limit exceeded errors specially
            if (errorData.code === 'MEMORY_LIMIT_EXCEEDED') {
                this.handleMemoryLimitError($field, module, fieldName, errorData);
                return;
            }

            // Handle queued saves
            if (errorData.code === 'QUEUED_FOR_PROCESSING') {
                this.showToast('Save queued for processing due to high memory usage', 'warning', 5000);
                return;
            }

            if (currentAttempts < this.config.maxRetries) {
                // Increment retry count
                this.state.retryAttempts.set(retryKey, currentAttempts + 1);

                // Show retry toast
                this.showToast(`Retrying save... (${currentAttempts + 1}/${this.config.maxRetries})`, 'warning', 3000);

                // Schedule retry with longer delay for memory issues
                const delay = errorData.code === 'MEMORY_LIMIT_EXCEEDED'
                    ? this.config.retryDelay * 3 * (currentAttempts + 1)
                    : this.config.retryDelay * (currentAttempts + 1);

                setTimeout(() => {
                    this.performFieldSave($field, module);
                }, delay);

            } else {
                // Max retries exceeded
                const errorMessage = errorData.suggestions && errorData.suggestions.length > 0
                    ? errorData.suggestions[0]
                    : 'Failed to save settings. Please try again.';

                this.showToast(errorMessage, 'error', 6000);

                // Clear retry attempts
                this.state.retryAttempts.delete(retryKey);
            }
        },

        // Handle memory limit errors
        handleMemoryLimitError: function($field, module, fieldName, errorData) {
            const message = errorData.suggestions && errorData.suggestions.length > 0
                ? errorData.suggestions[0]
                : 'Save failed due to memory constraints. Please try again.';

            this.showToast(message, 'error', 8000);
        },

        // Legacy functions kept for compatibility (now use toast notifications)
        updateFieldStatus: function($field, status, message) {
            // No longer needed - using toast notifications
        },

        clearFieldStatus: function($field) {
            // No longer needed - using toast notifications
        },

        // Universal field value extraction
        getFieldValue: function($field) {
            const tagName = $field.prop('tagName').toLowerCase();
            const type = $field.attr('type');

            // Handle threshold preset buttons
            if ($field.hasClass('threshold-preset')) {
                return $field.data('value');
            }

            // Handle threshold sliders - get value from associated input
            if ($field.hasClass('threshold-slider') || $field.attr('id') === 'threshold-slider') {
                // Find the associated threshold input field
                const $thresholdInput = $('#threshold');
                if ($thresholdInput.length > 0) {
                    return parseFloat($thresholdInput.val()) || 0;
                }
                return parseFloat($field.val()) || 0;
            }

            // Handle contenteditable elements
            if ($field.attr('contenteditable') === 'true') {
                return $field.html();
            }

            // Handle elements with data-value attribute
            if ($field.data('value') !== undefined) {
                return $field.data('value');
            }

            // Handle checkboxes and radio buttons
            if (type === 'checkbox' || type === 'radio') {
                return $field.is(':checked');
            }

            // Handle multi-select
            if (tagName === 'select' && $field.prop('multiple')) {
                return $field.val() || [];
            }

            // Handle number inputs (convert to number)
            if (type === 'number' || type === 'range') {
                const value = $field.val();
                return value !== '' ? parseFloat(value) || 0 : '';
            }

            // Handle standard form elements
            if (tagName === 'input' || tagName === 'select' || tagName === 'textarea') {
                return $field.val();
            }

            // Handle elements with text content
            if ($field.text() !== '') {
                return $field.text();
            }

            // Fallback to value attribute
            return $field.attr('value') || '';
        },

        // Check if field is critical (needs immediate save)
        isCriticalField: function($field) {
            const criticalTypes = ['email', 'url', 'password'];
            const criticalNames = ['api_key', 'license_key', 'secret'];
            
            const type = $field.attr('type');
            const name = $field.attr('name') || '';
            
            return criticalTypes.includes(type) || 
                   criticalNames.some(critical => name.toLowerCase().includes(critical));
        },

        // Check if field is important (shows global notification)
        isImportantField: function($field) {
            const importantNames = ['enabled', 'active', 'status'];
            const name = $field.attr('name') || '';

            return importantNames.some(important => name.toLowerCase().includes(important));
        },

        // Extract module name from form
        extractModuleFromForm: function($form) {
            console.log('🔧 Global Auto-Save: extractModuleFromForm called', {
                form_classes: $form.attr('class'),
                form_id: $form.attr('id'),
                form_action: $form.attr('action')
            });

            // Try to get from data-module attribute first
            let module = $form.data('module');
            if (module) {
                console.log('🔧 Global Auto-Save: Found module from data-module:', module);
                return module;
            }

            // PRIORITY 1: Check page context first for Global Settings
            const pageContext = this.getPageContext();
            if (pageContext && pageContext.startsWith('global-settings-')) {
                console.log('🔧 Global Auto-Save: Using page context as module (priority):', pageContext);
                return pageContext;
            }

            // PRIORITY 2: Special handling for Global Settings forms
            if ($form.hasClass('redco-settings-form')) {
                console.log('🔧 Global Auto-Save: Form has redco-settings-form class, checking for global settings fields');

                // Check if this is a Global Settings form by looking at form fields
                const hasGlobalSettings = $form.find('input[name^="redco_optimizer_options"], input[name^="redco_optimizer_performance"], input[name^="redco_optimizer_security"], input[name^="redco_optimizer_advanced"], input[name^="redco_optimizer_feedback"], input[name^="redco_optimizer_license"], input[name^="redco_optimizer_addons"]').length > 0;

                console.log('🔧 Global Auto-Save: Global settings fields found:', hasGlobalSettings);
                console.log('🔧 Global Auto-Save: Field breakdown:', {
                    options: $form.find('input[name^="redco_optimizer_options"]').length,
                    performance: $form.find('input[name^="redco_optimizer_performance"]').length,
                    security: $form.find('input[name^="redco_optimizer_security"]').length,
                    advanced: $form.find('input[name^="redco_optimizer_advanced"]').length,
                    feedback: $form.find('input[name^="redco_optimizer_feedback"]').length,
                    license: $form.find('input[name^="redco_optimizer_license"]').length,
                    addons: $form.find('input[name^="redco_optimizer_addons"]').length
                });

                if (hasGlobalSettings) {
                    // Use page context for Global Settings (already checked above, but fallback)
                    if (pageContext && pageContext.startsWith('global-settings-')) {
                        console.log('🔧 Global Auto-Save: Using page context as module:', pageContext);
                        return pageContext;
                    }

                    // Fallback to general global settings
                    console.log('🔧 Global Auto-Save: Using fallback global-settings-general');
                    return 'global-settings-general';
                }
            }

            // PRIORITY 3: Try to get from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const tab = urlParams.get('tab');
            if (tab) {
                console.log('🔧 Global Auto-Save: Found module from URL tab parameter:', tab);
                return tab;
            }

            // PRIORITY 4: Try to get from form action
            const action = $form.attr('action') || '';
            const actionMatch = action.match(/tab=([^&]+)/);
            if (actionMatch) {
                console.log('🔧 Global Auto-Save: Found module from form action:', actionMatch[1]);
                return actionMatch[1];
            }

            // PRIORITY 5: Try to get from form class (but exclude generic "settings")
            const className = $form.attr('class') || '';
            const classMatches = [
                className.match(/redco-(\w+)-form/),
                className.match(/(\w+)-module-form/),
                className.match(/module-(\w+)-form/)
            ];

            for (const match of classMatches) {
                if (match && match[1] !== 'settings') { // Exclude generic "settings"
                    console.log('🔧 Global Auto-Save: Found module from form class:', match[1]);
                    return match[1];
                }
            }

            // PRIORITY 6: Try to get from form ID (but exclude generic "settings")
            const formId = $form.attr('id') || '';
            const idMatches = [
                formId.match(/redco-(\w+)-form/),
                formId.match(/(\w+)-module-form/)
                // REMOVED: formId.match(/(\w+)-settings-form/) - this was causing "settings" extraction
            ];

            for (const match of idMatches) {
                if (match && match[1] !== 'settings') { // Exclude generic "settings"
                    console.log('🔧 Global Auto-Save: Found module from form ID:', match[1]);
                    return match[1];
                }
            }

            // PRIORITY 7: Try to get from page context (non-global-settings)
            if (pageContext) {
                console.log('🔧 Global Auto-Save: Found module from page context:', pageContext);
                return pageContext;
            }

            // Could not extract module from form
            console.log('❌ Global Auto-Save: Could not extract module from form');
            return null;
        },

        // Get page context to determine module
        getPageContext: function() {
            console.log('🔧 Global Auto-Save: getPageContext called');
            console.log('🔧 Global Auto-Save: Current URL:', window.location.href);

            // Check for Global Settings pages first
            if (window.location.href.includes('redco-optimizer-settings')) {
                console.log('🔧 Global Auto-Save: Detected Global Settings page');

                // Determine settings tab context
                const urlParams = new URLSearchParams(window.location.search);
                const settingsTab = urlParams.get('settings_tab') || 'general';

                console.log('🔧 Global Auto-Save: Settings tab from URL:', settingsTab);

                // Map settings tabs to pseudo-modules for auto-save
                const settingsTabMap = {
                    'general': 'global-settings-general',
                    'performance': 'global-settings-performance',
                    'security': 'global-settings-security',
                    'advanced': 'global-settings-advanced',
                    'feedback': 'global-settings-feedback',
                    'license': 'global-settings-license',
                    'addons': 'global-settings-addons'
                };

                const mappedModule = settingsTabMap[settingsTab] || 'global-settings-general';
                console.log('🔧 Global Auto-Save: Mapped module:', mappedModule);
                return mappedModule;
            }

            // Check for module tabs
            const $activeTab = $('.redco-module-tab.active, .nav-tab-active');
            if ($activeTab.length > 0) {
                const module = $activeTab.data('module') || $activeTab.attr('href')?.match(/tab=([^&]+)/)?.[1];
                if (module) {
                    return module;
                }
            }

            // Check for module-specific page indicators
            const moduleIndicators = [
                'page-cache',
                'asset-optimization',
                'lazy-load',
                'heartbeat-control',
                'wordpress-core-tweaks',
                'smart-webp-conversion',
                'cdn-integration',
                'database-cleanup'
            ];

            for (const indicator of moduleIndicators) {
                if (window.location.href.includes(indicator) ||
                    $(`.${indicator}-module, .module-${indicator}, #${indicator}-settings`).length > 0) {
                    return indicator;
                }
            }

            return null;
        },

        // Setup dynamic form detection for AJAX-loaded content
        setupDynamicFormDetection: function() {
            const self = this;

            // Use MutationObserver to detect new forms and fields
            if (window.MutationObserver) {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1) { // Element node
                                const $node = $(node);

                                // Check if the node itself is a form
                                if ($node.is('.redco-module-form, form[data-module], .redco-settings-form')) {
                                    const module = $node.data('module') || self.extractModuleFromForm($node);
                                    if (module) {
                                        self.setupFormAutoSave($node, module);
                                    }
                                }

                                // Check for forms within the node
                                $node.find('.redco-module-form, form[data-module], .redco-settings-form').each(function() {
                                    const $form = $(this);
                                    const module = $form.data('module') || self.extractModuleFromForm($form);
                                    if (module) {
                                        self.setupFormAutoSave($form, module);
                                    }
                                });

                                // Universal field detection for dynamically added fields
                                $node.find('*').each(function() {
                                    const $element = $(this);
                                    const $form = $element.closest('.redco-module-form, form[data-module], .redco-settings-form');

                                    if ($form.length > 0 && !$element.hasClass('redco-auto-save-enabled') &&
                                        self.isInteractiveFormElement($element)) {
                                        const module = $form.data('module') || self.extractModuleFromForm($form);
                                        if (module) {
                                            $element.addClass('redco-auto-save-enabled');
                                            self.bindFieldEvents($element, module);
                                        }
                                    }
                                });
                            }
                        });
                    });
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }
        },

        // Setup event listeners - simplified since auto-save handles changes immediately
        setupEventListeners: function() {
            // No special event listeners needed - auto-save handles changes immediately
            console.log('🔧 Event listeners simplified - auto-save handles changes immediately');
        },

        // Setup network monitoring
        setupNetworkMonitoring: function() {
            const self = this;

            // Monitor online/offline status
            $(window).on('online', function() {
                self.state.isOnline = true;
                self.hideNetworkStatus();
                self.retryFailedSaves();
                // Network connection restored
            });

            $(window).on('offline', function() {
                self.state.isOnline = false;
                self.showNetworkStatus('offline', 'You are currently offline. Changes will be saved when connection is restored.');
                // Network connection lost
            });
        },

        // Navigation protection removed - auto-save handles all changes immediately
        setupNavigationProtection: function() {
            // No navigation protection needed since auto-save handles changes immediately
            console.log('🔧 Navigation protection disabled - auto-save handles all changes');
        },

        // Periodic save removed - auto-save handles changes immediately
        setupPeriodicSave: function() {
            // No periodic save needed - auto-save triggers immediately on change
            console.log('🔧 Periodic save disabled - auto-save handles changes immediately');
        },

        // Setup UI elements
        setupUI: function() {
            // Toast container is created dynamically when needed
            // No need for global notification container anymore
        },

        // Legacy function - now uses toast notifications
        showGlobalNotification: function(message, type) {
            this.showToast(message, type);
        },

        // Show/hide network status
        showNetworkStatus: function(status, message) {
            const $status = $('.redco-network-status');

            $status
                .removeClass('offline reconnected')
                .addClass(status)
                .text(message)
                .show();
        },

        hideNetworkStatus: function() {
            $('.redco-network-status').hide();
        },

        // Legacy function - no longer needed with immediate auto-save
        saveAllPendingChanges: function() {
            // No action needed - auto-save handles changes immediately
            return Promise.resolve();
        },

        // Check if field has unsaved changes
        hasUnsavedChanges: function($field, module) {
            const fieldName = $field.attr('name');
            const currentValue = this.getFieldValue($field);
            const lastValue = this.getLastSavedValue(module, fieldName);
            const hasChanges = currentValue !== lastValue;

            // Debug logging for troubleshooting
            if (hasChanges) {
                console.log('🔧 Field has unsaved changes:', {
                    module: module,
                    fieldName: fieldName,
                    currentValue: currentValue,
                    currentType: typeof currentValue,
                    lastValue: lastValue,
                    lastType: typeof lastValue,
                    comparison: currentValue + ' !== ' + lastValue
                });
            }

            return hasChanges;
        },

        // Get last saved value for field
        getLastSavedValue: function(module, fieldName) {
            const key = module + '_' + fieldName;
            return localStorage.getItem('redco_auto_save_' + key);
        },

        // Set last saved value for field
        setLastSavedValue: function(module, fieldName, value) {
            const key = module + '_' + fieldName;
            localStorage.setItem('redco_auto_save_' + key, value);
        },

        // Load saved state from localStorage
        loadSavedState: function() {
            // Implementation for loading saved state
            // Loading saved auto-save state
        },

        // Perform immediate save for critical fields
        performImmediateSave: function($field, module) {
            // Clear any pending timer
            const fieldKey = $field.attr('name') || $field.attr('id');
            const timerKey = module + '_' + fieldKey;

            if (this.state.saveTimers.has(timerKey)) {
                clearTimeout(this.state.saveTimers.get(timerKey));
                this.state.saveTimers.delete(timerKey);
            }

            // Perform save immediately
            this.performFieldSave($field, module);
        },

        // Promise-based field save for batch operations
        performFieldSavePromise: function($field, module) {
            return new Promise((resolve, reject) => {
                // Implementation would be similar to performFieldSave but return a promise
                resolve();
            });
        },

        // Retry failed saves when network is restored
        retryFailedSaves: function() {
            const self = this;

            // Find all fields with error status and retry
            $('.redco-auto-save-status.error').each(function() {
                const $indicator = $(this);
                const $field = $indicator.siblings('input, select, textarea');

                if ($field.length > 0) {
                    const module = self.getFieldModule($field);
                    if (module) {
                        self.performFieldSave($field, module);
                    }
                }
            });
        },

        // Get module for a field
        getFieldModule: function($field) {
            const $form = $field.closest('.redco-module-form, form[data-module], .redco-settings-form');
            return $form.data('module') || this.extractModuleFromForm($form);
        },

        // Handle memory limit exceeded errors
        handleMemoryLimitError: function($field, module, fieldName, errorData) {
            this.updateFieldStatus($field, 'error', 'Memory limit exceeded');

            // Show detailed error message with suggestions
            let message = 'Unable to save due to memory constraints.';
            if (errorData.suggestions && errorData.suggestions.length > 0) {
                message += ' ' + errorData.suggestions[0];
            }

            this.showGlobalNotification(message, 'error');

            // Memory limit exceeded - debug logging removed for production

            // Clear retry attempts for memory errors
            const retryKey = module + '_' + fieldName;
            this.state.retryAttempts.delete(retryKey);

            // Suggest manual save after delay
            setTimeout(() => {
                this.updateFieldStatus($field, 'warning', 'Try manual save');
            }, 5000);
        },

        // Check status of queued save
        checkQueuedSaveStatus: function($field, module, fieldName) {
            const self = this;

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_check_save_status',
                    nonce: redcoAjax.global_auto_save_nonce || redcoAjax.nonce,
                    module: module
                },
                timeout: this.config.networkTimeout,
                success: function(response) {
                    if (response.success) {
                        // Check if the save has been processed
                        const currentValue = self.getFieldValue($field);
                        const lastSaved = self.getLastSavedValue(module, fieldName);

                        if (currentValue === lastSaved) {
                            // Save has been processed
                            self.updateFieldStatus($field, 'saved', 'Saved');
                            setTimeout(() => {
                                self.clearFieldStatus($field);
                            }, 3000);
                        } else {
                            // Still in queue, check again later
                            setTimeout(() => {
                                self.checkQueuedSaveStatus($field, module, fieldName);
                            }, 10000);
                        }
                    }
                },
                error: function() {
                    // If status check fails, assume it's still processing
                    setTimeout(() => {
                        self.checkQueuedSaveStatus($field, module, fieldName);
                    }, 15000);
                }
            });
        },

        // Duplicate updateFieldStatus function removed - cleanup complete
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Only initialize on Redco Optimizer pages
        console.log('🔧 Global Auto-Save: Checking page for initialization');
        console.log('🔧 Page indicators found:', {
            'redco-optimizer-admin': $('.redco-optimizer-admin').length,
            'redco-module-tab': $('.redco-module-tab').length,
            'redco-settings-form': $('.redco-settings-form').length,
            'current_url': window.location.href
        });

        if ($('.redco-optimizer-admin, .redco-module-tab, .redco-settings-form').length > 0) {
            console.log('🔧 Global Auto-Save: Initializing on Redco page');
            RedcoGlobalAutoSave.init();
        } else {
            console.log('🔧 Global Auto-Save: Not a Redco page, skipping initialization');
        }
    });

})(jQuery);
