<?php
/**
 * Main loader class for Redco Optimizer
 *
 * This class handles the initialization and loading of all plugin components,
 * including modules, admin UI, and core functionality.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Optimizer_Loader {

    /**
     * Available modules
     */
    private $modules = array();

    /**
     * Plugin options
     */
    private $options = array();

    /**
     * Initialize the plugin
     */
    public function init() {
        // Load text domain FIRST to prevent translation loading warnings
        $this->load_textdomain();

        // Load core files
        $this->load_dependencies();

        // Initialize hooks
        $this->init_hooks();

        // Load modules AFTER init hook to ensure text domain is available
        add_action('init', array($this, 'load_modules'), 1);

        // Initialize admin if in admin area
        if (is_admin()) {
            add_action('init', array($this, 'init_admin'), 2);
        }
    }

    /**
     * Load required dependencies (STEP 3: Restore more dependencies)
     */
    private function load_dependencies() {
        // Essential dependencies (working)
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/helpers.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-admin-ui.php';



        // Load additional dependencies
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-license-handler.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-addon-handler.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-setup-wizard.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-progress-tracker.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-progress-processors.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-pagespeed-optimizer.php';

        // CRITICAL FIX: Load extracted admin components
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-admin-settings-manager.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-admin-ajax-handlers.php';
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-global-auto-save-handler.php';


    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('admin_init', array($this, 'check_setup_wizard_redirect'));
    }

    /**
     * Load plugin text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'redco-optimizer',
            false,
            dirname(REDCO_OPTIMIZER_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * Enqueue frontend assets (PERFORMANCE OPTIMIZED)
     */
    public function enqueue_frontend_assets() {
        // Only enqueue if needed by active modules and not in admin
        if (is_admin() || is_customize_preview()) {
            return;
        }

        // PERFORMANCE: Cache enabled modules to avoid repeated database calls
        $cache_key = 'redco_enabled_modules_frontend';
        $enabled_modules = wp_cache_get($cache_key, 'redco_optimizer');

        if ($enabled_modules === false) {
            $enabled_modules = $this->get_enabled_modules();
            wp_cache_set($cache_key, $enabled_modules, 'redco_optimizer', 300); // 5 minutes
        }

        // Only load lazy load script if module is enabled and we're on frontend
        if (in_array('lazy_load', $enabled_modules)) {
            // PERFORMANCE: Add async loading and optimize for Core Web Vitals
            wp_enqueue_script(
                'redco-lazy-load',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/lazy-load.js',
                array(),
                REDCO_OPTIMIZER_VERSION,
                true // Load in footer
            );

            // PERFORMANCE: Add async attribute for non-blocking load
            add_filter('script_loader_tag', array($this, 'add_async_attribute'), 10, 2);
        }
    }

    /**
     * Enqueue admin assets (PERFORMANCE OPTIMIZED)
     */
    public function enqueue_admin_assets($hook) {
        // PERFORMANCE: Early exit for non-plugin pages
        if (strpos($hook, 'redco-optimizer') === false) {
            return;
        }

        // Skip loading main admin assets on setup wizard page
        if (strpos($hook, 'redco-optimizer-setup') !== false) {
            return;
        }

        // PERFORMANCE: Cache file modification times to avoid repeated file system calls
        $cache_key = 'redco_asset_versions';
        $asset_versions = wp_cache_get($cache_key, 'redco_optimizer');

        if ($asset_versions === false) {
            $css_file_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/css/admin-style.css';
            $js_file_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/js/admin-scripts.js';

            $asset_versions = array(
                'css' => REDCO_OPTIMIZER_VERSION . '.' . (file_exists($css_file_path) ? filemtime($css_file_path) : time()),
                'js' => REDCO_OPTIMIZER_VERSION . '.' . (file_exists($js_file_path) ? filemtime($js_file_path) : time())
            );

            wp_cache_set($cache_key, $asset_versions, 'redco_optimizer', 600); // 10 minutes
        }

        // PERFORMANCE: Conditionally load CSS based on page type
        $this->enqueue_optimized_admin_styles($hook, $asset_versions['css']);

        // PERFORMANCE: Extract versions for localization
        $css_version = $asset_versions['css'];
        $js_version = $asset_versions['js'];

        // Enhanced UI styles are now consolidated into admin-style.css

        // Load simplified settings styles and scripts on settings page
        if (strpos($hook, 'redco-optimizer-settings') !== false) {
            // Enqueue simplified settings CSS (card-free design)
            $settings_css_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/css/settings-simplified.css';
            if (file_exists($settings_css_path)) {
                $settings_css_version = REDCO_OPTIMIZER_VERSION . '.' . filemtime($settings_css_path);

                wp_enqueue_style(
                    'redco-optimizer-settings-simplified',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/settings-simplified.css',
                    array(),
                    $settings_css_version
                );
            }

            // settings-clean.js functionality has been consolidated into settings-script.js

            // Enqueue consolidated settings script with comprehensive auto-save
            $main_settings_js_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/js/settings-script.js';
            if (file_exists($main_settings_js_path)) {
                $main_settings_js_version = REDCO_OPTIMIZER_VERSION . '.' . filemtime($main_settings_js_path);

                wp_enqueue_script(
                    'redco-optimizer-settings-script',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/settings-script.js',
                    array('jquery'),
                    $main_settings_js_version,
                    true
                );

                // Ensure the same nonce is available for both scripts
                wp_localize_script('redco-optimizer-settings-script', 'redco_settings', array(
                    'nonce' => wp_create_nonce('redco_settings_nonce'),
                    'global_auto_save_nonce' => wp_create_nonce('redco_global_auto_save_nonce'),
                    'ajaxurl' => admin_url('admin-ajax.php')
                ));
            }


        }

        // Load modules-specific styles on modules page
        if (strpos($hook, 'redco-optimizer-modules') !== false) {
            wp_enqueue_style(
                'redco-optimizer-modules',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/modules-style.css',
                array('redco-admin-style'),
                REDCO_OPTIMIZER_VERSION
            );
        }



        // Load standardized module layout CSS for all module pages
        if (strpos($hook, 'redco-optimizer') !== false) {
            $layout_css_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/css/module-layout-standard.css';
            $layout_css_version = REDCO_OPTIMIZER_VERSION . '.' . filemtime($layout_css_path);

            wp_enqueue_style(
                'redco-module-layout-standard',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/module-layout-standard.css',
                array('redco-admin-style'),
                $layout_css_version
            );

            // Load global auto-save CSS (includes toast notifications)
            $global_auto_save_css_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/css/global-auto-save.css';
            if (file_exists($global_auto_save_css_path)) {
                wp_enqueue_style(
                    'redco-global-auto-save',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/global-auto-save.css',
                    array('redco-admin-style'),
                    REDCO_OPTIMIZER_VERSION . '.' . filemtime($global_auto_save_css_path)
                );
            }
        }

        // Determine JavaScript dependencies based on page
        $js_dependencies = array('jquery');

        // Enqueue Chart.js for Core Web Vitals chart (only on dashboard page)
        if (strpos($hook, 'redco-optimizer') !== false && !strpos($hook, 'redco-optimizer-modules') && !strpos($hook, 'redco-optimizer-settings')) {
            wp_enqueue_script(
                'chart-js',
                'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js',
                array(),
                '3.9.1',
                true
            );
            $js_dependencies[] = 'chart-js';
        }

        // PERFORMANCE: Temporarily disable consolidated JavaScript
        $use_consolidated_js = false; // Disabled until layout issues are resolved

        if ($use_consolidated_js) {
            // Consolidated JS temporarily disabled
            $consolidated_js_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/js/redco-admin-consolidated.js';
            wp_enqueue_script(
                'redco-admin-consolidated',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/redco-admin-consolidated.js',
                $js_dependencies,
                $js_version . '.' . filemtime($consolidated_js_path),
                true
            );
        } else {
            // Load utility libraries
            wp_enqueue_script(
                'redco-ajax-utils',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/redco-ajax-utils.js',
                array('jquery'),
                $js_version,
                true
            );

            wp_enqueue_script(
                'redco-validation-utils',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/redco-validation-utils.js',
                array('jquery'),
                $js_version,
                true
            );

            wp_enqueue_script(
                'redco-progress-utils',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/redco-progress-utils.js',
                array('jquery'),
                $js_version,
                true
            );

            // Load logging utilities first (if needed)
            $logging_js_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/js/redco-logging-utils.js';
            if (file_exists($logging_js_path)) {
                wp_enqueue_script(
                    'redco-logging-utils',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/redco-logging-utils.js',
                    array('jquery'),
                    $js_version,
                    true
                );
            }

            // Use original individual JavaScript files with updated dependencies
            $admin_dependencies = array_merge($js_dependencies, array('redco-ajax-utils', 'redco-validation-utils', 'redco-progress-utils'));
            if (file_exists($logging_js_path)) {
                $admin_dependencies[] = 'redco-logging-utils';
            }

            wp_enqueue_script(
                'redco-admin-scripts',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/admin-scripts.js',
                $admin_dependencies,
                $js_version,
                true
            );

            // Load new global auto-save system
            $global_auto_save_js_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/js/global-auto-save.js';
            if (file_exists($global_auto_save_js_path)) {
                wp_enqueue_script(
                    'redco-global-auto-save',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/global-auto-save.js',
                    array('jquery', 'redco-ajax-utils'),
                    REDCO_OPTIMIZER_VERSION . '.' . filemtime($global_auto_save_js_path),
                    true
                );
            }

















            wp_enqueue_script(
                'redco-performance-monitor-module',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/modules/redco-performance-monitor.js',
                array('jquery', 'redco-ajax-utils'),
                $js_version,
                true
            );

            wp_enqueue_script(
                'redco-ui-utils-module',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/modules/redco-ui-utils.js',
                array('jquery'),
                $js_version,
                true
            );

            // Load initialization script LAST with all dependencies
            wp_enqueue_script(
                'redco-init',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/redco-init.js',
                array('jquery', 'redco-ajax-utils', 'redco-validation-utils', 'redco-progress-utils', 'redco-global-auto-save', 'redco-performance-monitor-module', 'redco-ui-utils-module', 'redco-admin-scripts'),
                $js_version,
                true
            );

            // Phase 3: Conservative JavaScript enhancements
            $phase3_js_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/js/phase3-enhancements.js';
            if (file_exists($phase3_js_path)) {
                wp_enqueue_script(
                    'redco-phase3-enhancements',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/phase3-enhancements.js',
                    array('jquery', 'redco-admin-scripts'),
                    REDCO_OPTIMIZER_VERSION . '.' . filemtime($phase3_js_path),
                    true
                );
            }


        }

        // Debug scripts removed for production

        // Get performance settings for JavaScript
        $performance_options = get_option('redco_optimizer_performance', array());
        $update_interval = isset($performance_options['update_interval']) ? $performance_options['update_interval'] : 30;
        $monitoring_enabled = isset($performance_options['enable_monitoring']) ? $performance_options['enable_monitoring'] : 1;

        // Check if plugin is enabled
        $general_options = get_option('redco_optimizer_options', array());
        $plugin_enabled = isset($general_options['enabled']) ? $general_options['enabled'] : 1;

        // Localize script for AJAX - use original script handle
        $script_handle = 'redco-admin-scripts'; // Always use original for now

        wp_localize_script($script_handle, 'redcoAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('redco_optimizer_nonce'),
            'global_auto_save_nonce' => wp_create_nonce('redco_global_auto_save_nonce'),
            'settings' => array(
                'performanceUpdateInterval' => $update_interval * 1000, // Convert to milliseconds
                'monitoringEnabled' => $monitoring_enabled,
                'pluginEnabled' => $plugin_enabled,
                'autoSave' => true,
                'autoSaveDelay' => 2000, // Reduced from 3000 for better UX
                'showNotifications' => true,
                'autoRefreshStats' => false // Disabled by default for performance
            ),
            'strings' => array(
                'saving' => __('Saving...', 'redco-optimizer'),
                'saved' => __('Settings saved!', 'redco-optimizer'),
                'error' => __('Error saving settings.', 'redco-optimizer'),
                'confirm' => __('Are you sure?', 'redco-optimizer'),
                'success' => __('Success', 'redco-optimizer'),
                'failed' => __('Failed', 'redco-optimizer'),
                'loading' => __('Loading...', 'redco-optimizer'),
                'done' => __('Done', 'redco-optimizer')
            ),

        ));
    }

    /**
     * Load all available modules
     */
    public function load_modules() {
        $modules_dir = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/';

        // Define available modules
        $available_modules = array(
            'page-cache' => array(
                'name' => __('Page Cache', 'redco-optimizer'),
                'description' => __('Enable full page caching for faster load times', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-page-cache.php'
            ),
            'lazy-load' => array(
                'name' => __('Lazy Load Images', 'redco-optimizer'),
                'description' => __('Load images only when they come into view', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-lazy-load.php'
            ),
            'asset-optimization' => array(
                'name' => __('Asset Optimization', 'redco-optimizer'),
                'description' => __('Unified CSS/JS minification with critical resource optimization', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-asset-optimization.php',
                'replaces' => array('css-js-minifier', 'critical-resource-optimizer'),
                'unified' => true
            ),

            'database-cleanup' => array(
                'name' => __('Database Cleanup', 'redco-optimizer'),
                'description' => __('Clean up database from unnecessary data', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-database-cleanup.php'
            ),
            'heartbeat-control' => array(
                'name' => __('Heartbeat Control', 'redco-optimizer'),
                'description' => __('Control WordPress Heartbeat API frequency', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-heartbeat-control.php'
            ),
            'wordpress-core-tweaks' => array(
                'name' => __('WordPress Core Tweaks', 'redco-optimizer'),
                'description' => __('Optimize WordPress core features: emoji removal, version strings, and autosave', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-wordpress-core-tweaks.php'
            ),


            // Pro modules (coming soon)
            'ai-auto-optimizer' => array(
                'name' => __('AI-Based Auto Optimizer', 'redco-optimizer'),
                'description' => __('AI-powered automatic optimization', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            ),
            'ai-image-upscaler' => array(
                'name' => __('AI Image Upscaler', 'redco-optimizer'),
                'description' => __('AI-powered image upscaling and optimization', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            ),
            'cdn-integrations' => array(
                'name' => __('CDN Integrations', 'redco-optimizer'),
                'description' => __('Integrate with popular CDN services', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            ),
            'woocommerce-booster' => array(
                'name' => __('WooCommerce Booster', 'redco-optimizer'),
                'description' => __('Specialized WooCommerce optimizations', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            ),
            'preload-crawler' => array(
                'name' => __('Preload Crawler', 'redco-optimizer'),
                'description' => __('Intelligent page preloading system', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            ),
            'smart-webp-conversion' => array(
                'name' => __('Smart WebP Conversion', 'redco-optimizer'),
                'description' => __('Automatically convert images to WebP format for better performance', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-smart-webp-conversion.php'
            ),
            'cdn-integration' => array(
                'name' => __('CDN Integration', 'redco-optimizer'),
                'description' => __('Integrate with CDN providers for global content delivery', 'redco-optimizer'),
                'type' => 'free',
                'file' => 'class-cdn-integration.php'
            ),
            'role-based-access' => array(
                'name' => __('Role-Based Module Access', 'redco-optimizer'),
                'description' => __('Control module access by user roles', 'redco-optimizer'),
                'type' => 'premium',
                'coming_soon' => true
            )
        );

        $this->modules = apply_filters('redco_optimizer_modules', $available_modules);

        // Load free modules that exist
        foreach ($this->modules as $module_key => $module_data) {
            if ($module_data['type'] === 'free' && !isset($module_data['coming_soon'])) {
                $module_file = $modules_dir . $module_key . '/' . $module_data['file'];
                if (file_exists($module_file)) {
                    try {
                        require_once $module_file;
                    } catch (Exception $e) {
                        // CRITICAL FIX: Log module loading errors for debugging
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("Redco: Module loading exception in {$module_file}: " . $e->getMessage());
                        }
                    } catch (Error $e) {
                        // CRITICAL FIX: Log module loading fatal errors for debugging
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("Redco: Module loading fatal error in {$module_file}: " . $e->getMessage());
                        }
                    }
                }
            }
        }
    }

    /**
     * Initialize admin interface
     */
    public function init_admin() {
        try {
            $admin_ui = new Redco_Optimizer_Admin_UI($this->modules);
            $admin_ui->init();
        } catch (Exception $e) {
            // CRITICAL FIX: Log admin UI errors for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("Redco: Admin UI exception: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
            }
        } catch (Error $e) {
            // CRITICAL FIX: Log admin UI fatal errors for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("Redco: Admin UI fatal error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
            }
        }

        // Initialize admin components
        Redco_Progress_Tracker::init();
        Redco_Progress_Processors::init();

        // Initialize setup wizard
        $setup_wizard = new Redco_Optimizer_Setup_Wizard();

        // Initialize performance analyzer
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-performance-analyzer.php';
        $performance_analyzer = new Redco_Optimizer_Performance_Analyzer();
        $performance_analyzer->init();

        // Initialize PageSpeed diagnostics
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-pagespeed-diagnostics.php';
        $pagespeed_diagnostics = new Redco_Optimizer_PageSpeed_Diagnostics();
        $pagespeed_diagnostics->init();

        // Initialize Module Auditor
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-module-auditor.php';
        $module_auditor = new Redco_Optimizer_Module_Auditor();
        $module_auditor->init();

        // Initialize Module Consolidation Cleanup
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-module-consolidation-cleanup.php';

        // Check for setup wizard redirect
        add_action('admin_init', array($this, 'check_setup_wizard_redirect'));
    }

    /**
     * Check if we should redirect to setup wizard
     */
    public function check_setup_wizard_redirect() {
        // Only redirect on plugin activation
        if (get_transient('redco_optimizer_activation_redirect')) {
            delete_transient('redco_optimizer_activation_redirect');

            // Don't redirect if we're already on the setup page or if setup was completed/skipped
            if (isset($_GET['page']) && $_GET['page'] === 'redco-optimizer-setup') {
                return;
            }

            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-setup-wizard.php';
            $setup_wizard = new Redco_Optimizer_Setup_Wizard();
            if ($setup_wizard->should_show_wizard()) {
                wp_safe_redirect(admin_url('admin.php?page=redco-optimizer-setup'));
                exit;
            }
        }
    }

    /**
     * Get enabled modules
     */
    public function get_enabled_modules() {
        $options = get_option('redco_optimizer_options', array());
        return isset($options['modules_enabled']) ? $options['modules_enabled'] : array();
    }

    /**
     * Get all modules
     */
    public function get_modules() {
        return $this->modules;
    }

    /**
     * PERFORMANCE: Add async attribute to frontend scripts
     */
    public function add_async_attribute($tag, $handle) {
        if ($handle === 'redco-lazy-load') {
            return str_replace(' src', ' async src', $tag);
        }
        return $tag;
    }

    /**
     * PERFORMANCE: Optimized admin CSS loading (TEMPORARILY DISABLED CONSOLIDATION)
     */
    private function enqueue_optimized_admin_styles($hook, $css_version) {
        // TEMPORARILY DISABLE consolidated CSS to fix layout issues
        $use_consolidated = false; // Disabled until layout conflicts are resolved

        if ($use_consolidated) {
            // Consolidated CSS temporarily disabled
            $consolidated_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/css/redco-consolidated.css';
            wp_enqueue_style(
                'redco-consolidated',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/redco-consolidated.css',
                array(),
                $css_version . '.' . filemtime($consolidated_path)
            );
        } else {
            // Use original individual CSS files to preserve layout
            wp_enqueue_style(
                'redco-admin-style',
                REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/admin-style.css',
                array(),
                $css_version
            );

            // Enqueue auto-save loading indicator styles
            $loading_css_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/css/auto-save-loading.css';
            if (file_exists($loading_css_path)) {
                wp_enqueue_style(
                    'redco-auto-save-loading',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/auto-save-loading.css',
                    array('redco-admin-style'),
                    REDCO_OPTIMIZER_VERSION . '.' . filemtime($loading_css_path)
                );
            }

            // PERFORMANCE: Only load module-specific CSS on module pages
            if (strpos($hook, 'redco-optimizer-modules') !== false) {
                wp_enqueue_style(
                    'redco-optimizer-modules',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/modules-style.css',
                    array('redco-admin-style'),
                    REDCO_OPTIMIZER_VERSION
                );
            }

            // PERFORMANCE: Only load module layout CSS when needed
            if (strpos($hook, 'redco-optimizer') !== false && strpos($hook, 'redco-optimizer-setup') === false) {
                $layout_css_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/css/module-layout-standard.css';
                $layout_css_version = REDCO_OPTIMIZER_VERSION . '.' . filemtime($layout_css_path);

                wp_enqueue_style(
                    'redco-module-layout-standard',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/module-layout-standard.css',
                    array('redco-admin-style'),
                    $layout_css_version
                );
            }
        }
    }
}
