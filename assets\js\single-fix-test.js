/**
 * CRITICAL FIX: Single Fix Test Suite
 * Tests the single fix functionality to ensure proper error handling
 * Run this in browser console on the diagnostic page to test the fixes
 */

(function($) {
    'use strict';

    /**
     * Single Fix Test Suite
     */
    window.RedcoSingleFixTest = {
        
        /**
         * Test state
         */
        state: {
            testResults: [],
            startTime: null
        },
        
        /**
         * Run all single fix tests
         */
        runAllTests: function() {
            console.log('🧪 Starting Redco Single Fix Test Suite...');
            this.state.startTime = Date.now();
            this.state.testResults = [];
            
            // Test 1: Check if diagnostic results exist
            this.testDiagnosticResults();
            
            // Test 2: Check single fix button availability
            this.testSingleFixButtons();
            
            // Test 3: Test AJAX configuration for single fix
            this.testSingleFixAjaxConfig();
            
            // Test 4: Test error handling simulation
            this.testErrorHandling();
            
            // Test 5: Test nonce validation (if safe)
            this.testNonceValidation();
            
            // Show results
            this.showTestResults();
        },
        
        /**
         * Test 1: Check if diagnostic results exist
         */
        testDiagnosticResults: function() {
            console.log('🔍 Test 1: Checking diagnostic results...');
            
            const result = {
                test: 'Diagnostic Results',
                passed: false,
                details: []
            };
            
            // Check if diagnostic results container exists
            const $resultsContainer = $('.diagnostic-results, #diagnostic-results');
            if ($resultsContainer.length > 0) {
                result.details.push('✅ Diagnostic results container found');
            } else {
                result.details.push('❌ Diagnostic results container not found');
            }
            
            // Check if issues are displayed
            const $issues = $('.issue-item, .diagnostic-issue');
            if ($issues.length > 0) {
                result.details.push(`✅ Found ${$issues.length} diagnostic issues`);
                result.passed = true;
            } else {
                result.details.push('❌ No diagnostic issues found - run a scan first');
            }
            
            // Check if auto-fixable issues exist
            const $autoFixableIssues = $issues.filter('[data-auto-fixable="true"], .auto-fixable');
            if ($autoFixableIssues.length > 0) {
                result.details.push(`✅ Found ${$autoFixableIssues.length} auto-fixable issues`);
            } else {
                result.details.push('⚠️ No auto-fixable issues found');
            }
            
            this.state.testResults.push(result);
        },
        
        /**
         * Test 2: Check single fix button availability
         */
        testSingleFixButtons: function() {
            console.log('🔍 Test 2: Testing single fix buttons...');
            
            const result = {
                test: 'Single Fix Buttons',
                passed: false,
                details: []
            };
            
            // Find single fix buttons
            const $fixButtons = $('.apply-single-fix, .fix-button, [data-action="apply-single-fix"]');
            
            if ($fixButtons.length > 0) {
                result.details.push(`✅ Found ${$fixButtons.length} single fix buttons`);
                
                // Check if buttons are enabled
                const $enabledButtons = $fixButtons.filter(':not(:disabled)');
                if ($enabledButtons.length > 0) {
                    result.details.push(`✅ ${$enabledButtons.length} buttons are enabled`);
                    result.passed = true;
                } else {
                    result.details.push('❌ All fix buttons are disabled');
                }
                
                // Check if buttons have proper data attributes
                let buttonsWithData = 0;
                $fixButtons.each(function() {
                    const $btn = $(this);
                    if ($btn.data('issue-id') || $btn.attr('data-issue-id')) {
                        buttonsWithData++;
                    }
                });
                
                if (buttonsWithData > 0) {
                    result.details.push(`✅ ${buttonsWithData} buttons have issue IDs`);
                } else {
                    result.details.push('❌ No buttons have issue ID data attributes');
                    result.passed = false;
                }
            } else {
                result.details.push('❌ No single fix buttons found');
            }
            
            this.state.testResults.push(result);
        },
        
        /**
         * Test 3: Test AJAX configuration for single fix
         */
        testSingleFixAjaxConfig: function() {
            console.log('🔍 Test 3: Testing single fix AJAX configuration...');
            
            const result = {
                test: 'Single Fix AJAX Config',
                passed: false,
                details: []
            };
            
            // Check if diagnostic AJAX config exists
            if (typeof redcoDiagnosticAjax !== 'undefined') {
                result.details.push('✅ Diagnostic AJAX config available');
                
                // Check AJAX URL
                if (redcoDiagnosticAjax.ajaxurl) {
                    result.details.push('✅ AJAX URL configured');
                } else {
                    result.details.push('❌ AJAX URL missing');
                }
                
                // Check nonce
                if (redcoDiagnosticAjax.nonce) {
                    result.details.push('✅ Nonce configured');
                    result.passed = true;
                } else {
                    result.details.push('❌ Nonce missing');
                }
            } else {
                result.details.push('❌ Diagnostic AJAX config not found');
            }
            
            // Check if RedcoDiagnostic object exists
            if (typeof RedcoDiagnostic !== 'undefined') {
                result.details.push('✅ RedcoDiagnostic object available');
                
                // Check if handleSingleFix method exists
                if (typeof RedcoDiagnostic.handleSingleFix === 'function') {
                    result.details.push('✅ handleSingleFix method available');
                } else {
                    result.details.push('❌ handleSingleFix method not found');
                    result.passed = false;
                }
            } else {
                result.details.push('❌ RedcoDiagnostic object not found');
                result.passed = false;
            }
            
            this.state.testResults.push(result);
        },
        
        /**
         * Test 4: Test error handling simulation
         */
        testErrorHandling: function() {
            console.log('🔍 Test 4: Testing error handling...');
            
            const result = {
                test: 'Error Handling',
                passed: false,
                details: []
            };
            
            // Test if error handling functions exist
            if (typeof RedcoDiagnostic !== 'undefined') {
                // Check if showToast method exists
                if (typeof RedcoDiagnostic.showToast === 'function') {
                    result.details.push('✅ showToast method available');
                    result.passed = true;
                } else {
                    result.details.push('❌ showToast method not found');
                }
                
                // Check if showError method exists
                if (typeof RedcoDiagnostic.showError === 'function') {
                    result.details.push('✅ showError method available');
                } else {
                    result.details.push('❌ showError method not found');
                }
                
                // Test error message display (safe test)
                try {
                    // This should not cause any actual errors
                    const testErrorData = {
                        message: 'Test error message',
                        error_code: 'TEST_ERROR',
                        suggested_action: 'This is a test'
                    };
                    
                    result.details.push('✅ Error data structure validation passed');
                } catch (e) {
                    result.details.push('❌ Error data structure validation failed: ' + e.message);
                    result.passed = false;
                }
            } else {
                result.details.push('❌ RedcoDiagnostic object not available for testing');
            }
            
            this.state.testResults.push(result);
        },
        
        /**
         * Test 5: Test nonce validation (safe test)
         */
        testNonceValidation: function() {
            console.log('🔍 Test 5: Testing nonce validation...');
            
            const result = {
                test: 'Nonce Validation',
                passed: false,
                details: []
            };
            
            if (typeof redcoDiagnosticAjax !== 'undefined' && redcoDiagnosticAjax.nonce) {
                // Test nonce format (should be alphanumeric string)
                const nonce = redcoDiagnosticAjax.nonce;
                
                if (typeof nonce === 'string' && nonce.length > 0) {
                    result.details.push('✅ Nonce is a non-empty string');
                    
                    // Check if nonce looks valid (alphanumeric)
                    if (/^[a-zA-Z0-9]+$/.test(nonce)) {
                        result.details.push('✅ Nonce format appears valid');
                        result.passed = true;
                    } else {
                        result.details.push('⚠️ Nonce contains non-alphanumeric characters');
                        result.passed = true; // Still pass, might be valid
                    }
                    
                    // Check nonce length (WordPress nonces are typically 10 characters)
                    if (nonce.length >= 8 && nonce.length <= 12) {
                        result.details.push('✅ Nonce length appears correct');
                    } else {
                        result.details.push(`⚠️ Nonce length is ${nonce.length} (expected 8-12)`);
                    }
                } else {
                    result.details.push('❌ Nonce is not a valid string');
                }
            } else {
                result.details.push('❌ Nonce not available for testing');
            }
            
            this.state.testResults.push(result);
        },
        
        /**
         * Show test results
         */
        showTestResults: function() {
            const duration = ((Date.now() - this.state.startTime) / 1000).toFixed(2);
            const passed = this.state.testResults.filter(r => r.passed).length;
            const total = this.state.testResults.length;
            
            console.log('\n📊 REDCO SINGLE FIX TEST RESULTS');
            console.log('=================================');
            console.log(`⏱️ Total Duration: ${duration}s`);
            console.log(`✅ Passed: ${passed}/${total}`);
            console.log(`❌ Failed: ${total - passed}/${total}`);
            console.log('');
            
            this.state.testResults.forEach((result, index) => {
                console.log(`${index + 1}. ${result.test}: ${result.passed ? '✅ PASSED' : '❌ FAILED'}`);
                result.details.forEach(detail => {
                    console.log(`   ${detail}`);
                });
                console.log('');
            });
            
            if (passed === total) {
                console.log('🎉 ALL TESTS PASSED! Single fix functionality should work correctly.');
            } else {
                console.log('⚠️ SOME TESTS FAILED. Please review the issues above.');
            }
            
            // Return summary for programmatic use
            return {
                passed: passed,
                total: total,
                success: passed === total,
                results: this.state.testResults
            };
        },
        
        /**
         * Simulate single fix (for testing - does not actually apply fixes)
         */
        simulateSingleFix: function(issueId) {
            console.log(`🧪 Simulating single fix for issue: ${issueId}`);
            
            if (!issueId) {
                console.error('❌ Issue ID is required for simulation');
                return;
            }
            
            // This is a safe simulation that doesn't actually apply fixes
            const simulationData = {
                action: 'redco_apply_single_fix',
                issue_id: issueId,
                nonce: redcoDiagnosticAjax.nonce
            };
            
            console.log('📤 Would send AJAX request with data:', simulationData);
            console.log('🔗 To endpoint:', redcoDiagnosticAjax.ajaxurl);
            console.log('⚠️ This is a simulation - no actual fix is applied');
            
            return simulationData;
        }
    };

    // Auto-load test suite if in debug mode
    $(document).ready(function() {
        if (typeof redcoDebug !== 'undefined' && redcoDebug.enabled) {
            console.log('🧪 Single fix test suite loaded. Run RedcoSingleFixTest.runAllTests() to test single fix functionality.');
        }
    });

})(jQuery);
