/**
 * CRITICAL FIX: Centralized Progress Utility Library
 * Eliminates code duplication of progress simulation across 5 instances
 * Provides consistent progress handling and modal management
 */

(function() {
    'use strict';

    /**
     * Centralized Progress Utility System
     */
    window.RedcoProgress = {
        
        /**
         * Active progress instances
         */
        activeProgress: new Map(),
        
        /**
         * Default progress settings
         */
        defaults: {
            interval: 800,
            autoClose: true,
            closeDelay: 2000,
            showPercentage: true,
            allowCancel: false
        },
        
        /**
         * Create and show progress modal
         * 
         * @param {string} title Modal title
         * @param {Object} options Progress options
         * @return {string} Progress instance ID
         */
        showModal: function(title, options = {}) {
            const settings = $.extend({}, this.defaults, options);
            const instanceId = 'progress_' + Date.now();
            
            // Create modal HTML
            const modalHtml = `
                <div id="${instanceId}" class="redco-progress-modal">
                    <div class="redco-progress-overlay"></div>
                    <div class="redco-progress-container">
                        <div class="redco-progress-header">
                            <h3 class="redco-progress-title">${title}</h3>
                            ${settings.allowCancel ? '<button class="redco-progress-cancel" type="button">&times;</button>' : ''}
                        </div>
                        <div class="redco-progress-body">
                            <div class="redco-progress-bar-container">
                                <div class="redco-progress-bar">
                                    <div class="redco-progress-fill" style="width: 0%"></div>
                                </div>
                                ${settings.showPercentage ? '<div class="redco-progress-percentage">0%</div>' : ''}
                            </div>
                            <div class="redco-progress-message">Initializing...</div>
                        </div>
                    </div>
                </div>
            `;
            
            // Add to DOM
            $('body').append(modalHtml);
            
            // Store instance
            this.activeProgress.set(instanceId, {
                settings: settings,
                intervals: new Set(),
                startTime: Date.now()
            });
            
            // Setup cancel handler if enabled
            if (settings.allowCancel && settings.onCancel) {
                $(`#${instanceId} .redco-progress-cancel`).on('click', () => {
                    settings.onCancel();
                    this.hideModal(instanceId);
                });
            }
            
            return instanceId;
        },
        
        /**
         * Update progress
         * 
         * @param {string} instanceId Progress instance ID
         * @param {number} percentage Progress percentage (0-100)
         * @param {string} message Progress message
         */
        updateProgress: function(instanceId, percentage, message = '') {
            const $modal = $(`#${instanceId}`);
            if (!$modal.length) return;
            
            // Update progress bar
            $modal.find('.redco-progress-fill').css('width', percentage + '%');
            
            // Update percentage text
            if ($modal.find('.redco-progress-percentage').length) {
                $modal.find('.redco-progress-percentage').text(Math.round(percentage) + '%');
            }
            
            // Update message
            if (message) {
                $modal.find('.redco-progress-message').text(message);
            }
            
            // Auto-close on completion
            if (percentage >= 100) {
                const instance = this.activeProgress.get(instanceId);
                if (instance && instance.settings.autoClose) {
                    setTimeout(() => {
                        this.hideModal(instanceId);
                    }, instance.settings.closeDelay);
                }
            }
        },
        
        /**
         * Hide progress modal
         * 
         * @param {string} instanceId Progress instance ID
         */
        hideModal: function(instanceId) {
            const instance = this.activeProgress.get(instanceId);
            if (instance) {
                // Clear all intervals for this instance
                instance.intervals.forEach(intervalId => {
                    clearInterval(intervalId);
                });
                
                // Remove from active progress
                this.activeProgress.delete(instanceId);
            }
            
            // Remove modal from DOM
            $(`#${instanceId}`).fadeOut(300, function() {
                $(this).remove();
            });
        },
        
        /**
         * Simulate progress with predefined steps
         * 
         * @param {string} instanceId Progress instance ID
         * @param {Array} steps Array of progress steps
         * @return {number} Interval ID
         */
        simulateProgress: function(instanceId, steps) {
            const instance = this.activeProgress.get(instanceId);
            if (!instance) return null;
            
            let currentStep = 0;
            
            const intervalId = setInterval(() => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    this.updateProgress(instanceId, step.progress, step.text);
                    currentStep++;
                } else {
                    clearInterval(intervalId);
                    instance.intervals.delete(intervalId);
                }
            }, instance.settings.interval);
            
            // Track interval for cleanup
            instance.intervals.add(intervalId);
            
            return intervalId;
        },
        
        /**
         * Create batch progress handler
         * 
         * @param {string} instanceId Progress instance ID
         * @param {number} totalItems Total number of items to process
         * @param {Function} onProgress Progress callback
         * @return {Object} Batch progress handler
         */
        createBatchHandler: function(instanceId, totalItems, onProgress) {
            let processedItems = 0;
            
            return {
                increment: (message = '') => {
                    processedItems++;
                    const percentage = (processedItems / totalItems) * 100;
                    this.updateProgress(instanceId, percentage, message);
                    
                    if (onProgress) {
                        onProgress(processedItems, totalItems, percentage);
                    }
                },
                
                setProgress: (processed, message = '') => {
                    processedItems = processed;
                    const percentage = (processedItems / totalItems) * 100;
                    this.updateProgress(instanceId, percentage, message);
                    
                    if (onProgress) {
                        onProgress(processedItems, totalItems, percentage);
                    }
                },
                
                complete: (message = 'Complete!') => {
                    this.updateProgress(instanceId, 100, message);
                }
            };
        },
        
        /**
         * Show simple progress bar (without modal)
         * 
         * @param {string} selector Container selector
         * @param {Object} options Progress options
         * @return {string} Progress instance ID
         */
        showInline: function(selector, options = {}) {
            const settings = $.extend({}, this.defaults, options);
            const instanceId = 'inline_progress_' + Date.now();
            const $container = $(selector);
            
            if (!$container.length) return null;
            
            // Create inline progress HTML
            const progressHtml = `
                <div id="${instanceId}" class="redco-inline-progress">
                    <div class="redco-progress-bar-container">
                        <div class="redco-progress-bar">
                            <div class="redco-progress-fill" style="width: 0%"></div>
                        </div>
                        ${settings.showPercentage ? '<div class="redco-progress-percentage">0%</div>' : ''}
                    </div>
                    <div class="redco-progress-message">Ready...</div>
                </div>
            `;
            
            $container.html(progressHtml);
            
            // Store instance
            this.activeProgress.set(instanceId, {
                settings: settings,
                intervals: new Set(),
                startTime: Date.now(),
                inline: true
            });
            
            return instanceId;
        },
        
        /**
         * Hide inline progress
         * 
         * @param {string} instanceId Progress instance ID
         */
        hideInline: function(instanceId) {
            const instance = this.activeProgress.get(instanceId);
            if (instance && instance.inline) {
                // Clear intervals
                instance.intervals.forEach(intervalId => {
                    clearInterval(intervalId);
                });
                
                // Remove from active progress
                this.activeProgress.delete(instanceId);
                
                // Remove from DOM
                $(`#${instanceId}`).fadeOut(300, function() {
                    $(this).remove();
                });
            }
        },
        
        /**
         * Get progress statistics
         * 
         * @param {string} instanceId Progress instance ID
         * @return {Object} Progress statistics
         */
        getStats: function(instanceId) {
            const instance = this.activeProgress.get(instanceId);
            if (!instance) return null;
            
            const elapsed = Date.now() - instance.startTime;
            const $modal = $(`#${instanceId}`);
            const currentProgress = parseFloat($modal.find('.redco-progress-fill').css('width')) || 0;
            
            return {
                elapsed: elapsed,
                elapsedFormatted: this.formatTime(elapsed),
                currentProgress: currentProgress,
                estimatedTotal: currentProgress > 0 ? (elapsed / currentProgress) * 100 : 0
            };
        },
        
        /**
         * Format time duration
         * 
         * @param {number} milliseconds Time in milliseconds
         * @return {string} Formatted time string
         */
        formatTime: function(milliseconds) {
            const seconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(seconds / 60);
            
            if (minutes > 0) {
                return `${minutes}m ${seconds % 60}s`;
            }
            return `${seconds}s`;
        },
        
        /**
         * Cancel all active progress instances
         */
        cancelAll: function() {
            this.activeProgress.forEach((instance, instanceId) => {
                if (instance.inline) {
                    this.hideInline(instanceId);
                } else {
                    this.hideModal(instanceId);
                }
            });
        }
    };

})();
