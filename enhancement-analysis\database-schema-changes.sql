-- Database Schema Changes for Enhanced Redco Optimizer
-- These tables support the new granular fix system, monitoring, and scheduling features

-- Table for storing real-time performance metrics
CREATE TABLE IF NOT EXISTS `wp_redco_performance_metrics` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `timestamp` datetime NOT NULL,
    `metric_type` varchar(50) NOT NULL,
    `metric_name` varchar(100) NOT NULL,
    `metric_value` decimal(10,4) NOT NULL,
    `metric_unit` varchar(20) DEFAULT NULL,
    `page_url` varchar(500) DEFAULT NULL,
    `user_agent` varchar(255) DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `session_id` varchar(100) DEFAULT NULL,
    `additional_data` longtext DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `timestamp_idx` (`timestamp`),
    KEY `metric_type_idx` (`metric_type`),
    KEY `metric_name_idx` (`metric_name`),
    KEY `page_url_idx` (`page_url`(255))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing scheduled fixes
CREATE TABLE IF NOT EXISTS `wp_redco_scheduled_fixes` (
    `id` varchar(36) NOT NULL,
    `fix_id` varchar(100) NOT NULL,
    `fix_details` longtext NOT NULL,
    `schedule_type` varchar(50) NOT NULL,
    `scheduled_time` datetime NOT NULL,
    `created_by` bigint(20) unsigned NOT NULL,
    `created_at` datetime NOT NULL,
    `executed_at` datetime DEFAULT NULL,
    `status` varchar(20) NOT NULL DEFAULT 'scheduled',
    `execution_result` longtext DEFAULT NULL,
    `config` longtext DEFAULT NULL,
    `notifications` longtext DEFAULT NULL,
    `rollback_config` longtext DEFAULT NULL,
    `backup_id` varchar(36) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `fix_id_idx` (`fix_id`),
    KEY `scheduled_time_idx` (`scheduled_time`),
    KEY `status_idx` (`status`),
    KEY `created_by_idx` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing fix execution history with detailed results
CREATE TABLE IF NOT EXISTS `wp_redco_fix_history` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `fix_id` varchar(100) NOT NULL,
    `fix_type` varchar(50) NOT NULL,
    `fix_tier` varchar(20) NOT NULL,
    `fix_category` varchar(50) NOT NULL,
    `executed_at` datetime NOT NULL,
    `executed_by` bigint(20) unsigned NOT NULL,
    `execution_method` varchar(50) NOT NULL DEFAULT 'manual',
    `success` tinyint(1) NOT NULL,
    `error_message` text DEFAULT NULL,
    `before_metrics` longtext DEFAULT NULL,
    `after_metrics` longtext DEFAULT NULL,
    `files_modified` longtext DEFAULT NULL,
    `database_changes` longtext DEFAULT NULL,
    `backup_id` varchar(36) DEFAULT NULL,
    `rollback_id` varchar(36) DEFAULT NULL,
    `rollback_deadline` datetime DEFAULT NULL,
    `impact_score` decimal(5,2) DEFAULT NULL,
    `user_rating` tinyint(1) DEFAULT NULL,
    `user_feedback` text DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `fix_id_idx` (`fix_id`),
    KEY `executed_at_idx` (`executed_at`),
    KEY `success_idx` (`success`),
    KEY `fix_tier_idx` (`fix_tier`),
    KEY `fix_category_idx` (`fix_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing performance alerts and notifications
CREATE TABLE IF NOT EXISTS `wp_redco_performance_alerts` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `alert_type` varchar(50) NOT NULL,
    `severity` varchar(20) NOT NULL,
    `metric_name` varchar(100) NOT NULL,
    `threshold_value` decimal(10,4) NOT NULL,
    `actual_value` decimal(10,4) NOT NULL,
    `message` text NOT NULL,
    `triggered_at` datetime NOT NULL,
    `acknowledged_at` datetime DEFAULT NULL,
    `acknowledged_by` bigint(20) unsigned DEFAULT NULL,
    `resolved_at` datetime DEFAULT NULL,
    `resolution_notes` text DEFAULT NULL,
    `notification_sent` tinyint(1) DEFAULT 0,
    `notification_channels` varchar(255) DEFAULT NULL,
    `additional_context` longtext DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `alert_type_idx` (`alert_type`),
    KEY `severity_idx` (`severity`),
    KEY `triggered_at_idx` (`triggered_at`),
    KEY `acknowledged_idx` (`acknowledged_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing backup information
CREATE TABLE IF NOT EXISTS `wp_redco_backups` (
    `id` varchar(36) NOT NULL,
    `backup_type` varchar(50) NOT NULL,
    `created_at` datetime NOT NULL,
    `created_by` bigint(20) unsigned NOT NULL,
    `backup_size` bigint(20) unsigned DEFAULT NULL,
    `backup_path` varchar(500) NOT NULL,
    `backup_hash` varchar(64) DEFAULT NULL,
    `files_included` longtext DEFAULT NULL,
    `database_tables` longtext DEFAULT NULL,
    `related_fix_id` varchar(100) DEFAULT NULL,
    `expiry_date` datetime DEFAULT NULL,
    `status` varchar(20) NOT NULL DEFAULT 'active',
    `restoration_count` int(11) DEFAULT 0,
    `last_restored_at` datetime DEFAULT NULL,
    `metadata` longtext DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `backup_type_idx` (`backup_type`),
    KEY `created_at_idx` (`created_at`),
    KEY `related_fix_id_idx` (`related_fix_id`),
    KEY `status_idx` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing user preferences and fix configurations
CREATE TABLE IF NOT EXISTS `wp_redco_user_preferences` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) unsigned NOT NULL,
    `preference_key` varchar(100) NOT NULL,
    `preference_value` longtext DEFAULT NULL,
    `preference_type` varchar(50) NOT NULL DEFAULT 'string',
    `created_at` datetime NOT NULL,
    `updated_at` datetime NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_preference_unique` (`user_id`, `preference_key`),
    KEY `user_id_idx` (`user_id`),
    KEY `preference_key_idx` (`preference_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing API integration data and rate limiting
CREATE TABLE IF NOT EXISTS `wp_redco_api_usage` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `api_provider` varchar(50) NOT NULL,
    `api_endpoint` varchar(255) NOT NULL,
    `request_date` date NOT NULL,
    `request_count` int(11) NOT NULL DEFAULT 1,
    `last_request_at` datetime NOT NULL,
    `rate_limit_remaining` int(11) DEFAULT NULL,
    `rate_limit_reset` datetime DEFAULT NULL,
    `response_cache` longtext DEFAULT NULL,
    `cache_expires_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `api_daily_usage` (`api_provider`, `request_date`),
    KEY `api_provider_idx` (`api_provider`),
    KEY `request_date_idx` (`request_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing Core Web Vitals data
CREATE TABLE IF NOT EXISTS `wp_redco_core_web_vitals` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `page_url` varchar(500) NOT NULL,
    `measurement_date` date NOT NULL,
    `measurement_source` varchar(50) NOT NULL,
    `lcp_value` decimal(8,2) DEFAULT NULL,
    `fid_value` decimal(8,2) DEFAULT NULL,
    `cls_value` decimal(6,4) DEFAULT NULL,
    `fcp_value` decimal(8,2) DEFAULT NULL,
    `ttfb_value` decimal(8,2) DEFAULT NULL,
    `device_type` varchar(20) NOT NULL DEFAULT 'desktop',
    `connection_type` varchar(50) DEFAULT NULL,
    `user_agent` varchar(255) DEFAULT NULL,
    `additional_metrics` longtext DEFAULT NULL,
    `created_at` datetime NOT NULL,
    PRIMARY KEY (`id`),
    KEY `page_url_idx` (`page_url`(255)),
    KEY `measurement_date_idx` (`measurement_date`),
    KEY `measurement_source_idx` (`measurement_source`),
    KEY `device_type_idx` (`device_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing accessibility scan results
CREATE TABLE IF NOT EXISTS `wp_redco_accessibility_scans` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `page_url` varchar(500) NOT NULL,
    `scan_date` datetime NOT NULL,
    `wcag_level` varchar(5) NOT NULL DEFAULT 'AA',
    `total_issues` int(11) NOT NULL DEFAULT 0,
    `critical_issues` int(11) NOT NULL DEFAULT 0,
    `warning_issues` int(11) NOT NULL DEFAULT 0,
    `info_issues` int(11) NOT NULL DEFAULT 0,
    `compliance_score` decimal(5,2) DEFAULT NULL,
    `scan_results` longtext NOT NULL,
    `auto_fixable_count` int(11) NOT NULL DEFAULT 0,
    `scan_duration` decimal(8,2) DEFAULT NULL,
    `scan_status` varchar(20) NOT NULL DEFAULT 'completed',
    PRIMARY KEY (`id`),
    KEY `page_url_idx` (`page_url`(255)),
    KEY `scan_date_idx` (`scan_date`),
    KEY `wcag_level_idx` (`wcag_level`),
    KEY `compliance_score_idx` (`compliance_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing mobile optimization data
CREATE TABLE IF NOT EXISTS `wp_redco_mobile_optimization` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `page_url` varchar(500) NOT NULL,
    `scan_date` datetime NOT NULL,
    `mobile_score` decimal(5,2) DEFAULT NULL,
    `viewport_configured` tinyint(1) DEFAULT 0,
    `touch_targets_optimized` tinyint(1) DEFAULT 0,
    `responsive_design` tinyint(1) DEFAULT 0,
    `mobile_performance_score` decimal(5,2) DEFAULT NULL,
    `amp_compatible` tinyint(1) DEFAULT 0,
    `pwa_features` longtext DEFAULT NULL,
    `optimization_opportunities` longtext DEFAULT NULL,
    `scan_results` longtext NOT NULL,
    PRIMARY KEY (`id`),
    KEY `page_url_idx` (`page_url`(255)),
    KEY `scan_date_idx` (`scan_date`),
    KEY `mobile_score_idx` (`mobile_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Indexes for performance optimization
CREATE INDEX `wp_redco_metrics_composite` ON `wp_redco_performance_metrics` (`timestamp`, `metric_type`, `metric_name`);
CREATE INDEX `wp_redco_alerts_composite` ON `wp_redco_performance_alerts` (`triggered_at`, `severity`, `alert_type`);
CREATE INDEX `wp_redco_history_composite` ON `wp_redco_fix_history` (`executed_at`, `success`, `fix_tier`);

-- Views for common queries
CREATE OR REPLACE VIEW `wp_redco_recent_metrics` AS
SELECT 
    metric_type,
    metric_name,
    AVG(metric_value) as avg_value,
    MIN(metric_value) as min_value,
    MAX(metric_value) as max_value,
    COUNT(*) as measurement_count
FROM `wp_redco_performance_metrics`
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY metric_type, metric_name;

CREATE OR REPLACE VIEW `wp_redco_fix_success_rate` AS
SELECT 
    fix_tier,
    fix_category,
    COUNT(*) as total_fixes,
    SUM(success) as successful_fixes,
    ROUND((SUM(success) / COUNT(*)) * 100, 2) as success_rate
FROM `wp_redco_fix_history`
WHERE executed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY fix_tier, fix_category;

CREATE OR REPLACE VIEW `wp_redco_active_alerts` AS
SELECT 
    alert_type,
    severity,
    COUNT(*) as alert_count,
    MIN(triggered_at) as first_alert,
    MAX(triggered_at) as latest_alert
FROM `wp_redco_performance_alerts`
WHERE acknowledged_at IS NULL AND resolved_at IS NULL
GROUP BY alert_type, severity;

-- Stored procedures for common operations
DELIMITER //

CREATE PROCEDURE `sp_redco_cleanup_old_metrics`(IN days_to_keep INT)
BEGIN
    DELETE FROM `wp_redco_performance_metrics` 
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    DELETE FROM `wp_redco_performance_alerts` 
    WHERE resolved_at IS NOT NULL 
    AND resolved_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
END //

CREATE PROCEDURE `sp_redco_get_performance_summary`(IN page_url_param VARCHAR(500))
BEGIN
    SELECT 
        DATE(timestamp) as measurement_date,
        AVG(CASE WHEN metric_name = 'page_load_time' THEN metric_value END) as avg_load_time,
        AVG(CASE WHEN metric_name = 'ttfb' THEN metric_value END) as avg_ttfb,
        AVG(CASE WHEN metric_name = 'lcp' THEN metric_value END) as avg_lcp,
        AVG(CASE WHEN metric_name = 'fid' THEN metric_value END) as avg_fid,
        AVG(CASE WHEN metric_name = 'cls' THEN metric_value END) as avg_cls
    FROM `wp_redco_performance_metrics`
    WHERE (page_url_param IS NULL OR page_url = page_url_param)
    AND timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    GROUP BY DATE(timestamp)
    ORDER BY measurement_date DESC;
END //

DELIMITER ;

-- Triggers for automatic data management
DELIMITER //

CREATE TRIGGER `tr_redco_fix_history_after_insert`
AFTER INSERT ON `wp_redco_fix_history`
FOR EACH ROW
BEGIN
    -- Update user preferences based on successful fixes
    IF NEW.success = 1 THEN
        INSERT INTO `wp_redco_user_preferences` 
        (user_id, preference_key, preference_value, preference_type, created_at, updated_at)
        VALUES 
        (NEW.executed_by, CONCAT('successful_fix_', NEW.fix_tier), '1', 'counter', NOW(), NOW())
        ON DUPLICATE KEY UPDATE 
        preference_value = CAST(preference_value AS UNSIGNED) + 1,
        updated_at = NOW();
    END IF;
END //

CREATE TRIGGER `tr_redco_alerts_after_insert`
AFTER INSERT ON `wp_redco_performance_alerts`
FOR EACH ROW
BEGIN
    -- Auto-acknowledge low severity alerts after 24 hours
    IF NEW.severity = 'low' THEN
        INSERT INTO `wp_redco_scheduled_fixes` 
        (id, fix_id, fix_details, schedule_type, scheduled_time, created_by, created_at, status, config)
        VALUES 
        (UUID(), 'auto_acknowledge_alert', 
         JSON_OBJECT('alert_id', NEW.id), 
         'custom_time', 
         DATE_ADD(NOW(), INTERVAL 24 HOUR), 
         1, NOW(), 'scheduled',
         JSON_OBJECT('auto_generated', true));
    END IF;
END //

DELIMITER ;
