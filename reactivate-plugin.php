<?php
/**
 * REACTIVATE: Redco Optimizer Plugin
 * This reactivates the plugin after fixing the .htaccess issue
 */

// Database configuration
$db_host = 'localhost';
$db_name = 'wp_redco';
$db_user = 'root';
$db_pass = '';
$table_prefix = 'wp_';

echo "🔄 REACTIVATING REDCO OPTIMIZER\n";
echo "===============================\n\n";

try {
    // Connect to database
    $pdo = new PDO("mysql:host={$db_host};dbname={$db_name}", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful\n";
    
    // Get current active plugins
    $stmt = $pdo->prepare("SELECT option_value FROM {$table_prefix}options WHERE option_name = 'active_plugins'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        $active_plugins = unserialize($result['option_value']);
        echo "📋 Current active plugins:\n";
        
        if (is_array($active_plugins)) {
            foreach ($active_plugins as $plugin) {
                echo "  - {$plugin}\n";
            }
            
            // Add Redco Optimizer to active plugins if not already there
            $redco_plugin = 'redco-optimizer/redco-optimizer.php';
            
            if (!in_array($redco_plugin, $active_plugins)) {
                $active_plugins[] = $redco_plugin;
                
                // Update the database
                $serialized_plugins = serialize(array_values($active_plugins));
                $update_stmt = $pdo->prepare("UPDATE {$table_prefix}options SET option_value = ? WHERE option_name = 'active_plugins'");
                $update_stmt->execute(array($serialized_plugins));
                
                echo "\n✅ Redco Optimizer reactivated successfully!\n";
                echo "📝 Plugin added to active plugins list\n";
                
            } else {
                echo "\n⚠️ Redco Optimizer is already active\n";
            }
            
        } else {
            echo "❌ Active plugins data is corrupted\n";
        }
        
    } else {
        echo "❌ Could not find active_plugins option in database\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🧪 TEST NOW:\n";
echo "============\n";
echo "Try accessing: http://localhost/wordpress/wp-admin/\n";
echo "The .htaccess BOM issue has been fixed, so it should work now!\n";

echo "\n📋 WHAT WAS FIXED:\n";
echo "==================\n";
echo "✅ Removed UTF-8 BOM from .htaccess file\n";
echo "✅ Reactivated Redco Optimizer plugin\n";
echo "✅ WordPress should now load without 500 errors\n";

?>
