<?php
/**
 * Lazy Load Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$lazy_load = new Redco_Lazy_Load();
$is_enabled = redco_is_module_enabled('lazy-load');

// Get current settings using standardized Settings Manager
$defaults = Redco_Config::get_module_defaults('lazy-load');
$exclude_featured = Redco_Settings_Manager::get_setting('lazy-load', 'exclude_featured', $defaults['exclude_featured']);
$exclude_woocommerce = Redco_Settings_Manager::get_setting('lazy-load', 'exclude_woocommerce', $defaults['exclude_woocommerce']);
$threshold = Redco_Settings_Manager::get_setting('lazy-load', 'threshold', $defaults['threshold']);

// Get statistics - only if module is enabled
$stats = array('images_processed' => 0, 'bytes_saved' => 0);
if ($is_enabled && class_exists('Redco_Lazy_Load')) {
    $stats = $lazy_load->get_stats();
}

// Create settings array for shared optimization panel
$settings = array(
    'exclude_featured' => $exclude_featured,
    'exclude_woocommerce' => $exclude_woocommerce,
    'threshold' => $threshold,
    'exclude_first_images' => redco_get_module_option('lazy-load', 'exclude_first_images', 2),
    'enable_native_lazy_loading' => redco_get_module_option('lazy-load', 'enable_native_lazy_loading', 1)
);
?>

<div class="redco-module-tab" data-module="lazy-load">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('Lazy Load', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-format-image"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('Lazy Load', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Improve page load times by loading images only when they enter the viewport for better performance', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if ($exclude_featured): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-format-image"></span>
                                    <?php _e('Featured Excluded', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($threshold <= 200): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-performance"></span>
                                    <?php _e('Optimized', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled): ?>
                            <button type="button" class="header-action-btn" id="optimize-for-performance">
                                <span class="dashicons dashicons-performance"></span>
                                <?php _e('Optimize', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="enable-recommended-exclusions">
                                <span class="dashicons dashicons-yes"></span>
                                <?php _e('Recommended', 'redco-optimizer'); ?>
                            </button>
                            <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'); ?>" class="header-action-btn">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Diagnose', 'redco-optimizer'); ?>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <?php if ($is_enabled): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo number_format($stats['images_processed']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Images', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $threshold; ?>px
                            </div>
                            <div class="header-metric-label"><?php _e('Threshold', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $exclude_featured ? '✓' : '✗'; ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Featured', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form <?php echo redco_auto_save_form_class('lazy-load'); ?>" data-module="lazy-load">
                    <!-- Lazy Loading Settings Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-visibility"></span>
                                <?php _e('Loading Behavior Settings', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Performance Impact:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="lazy-impact"><?php _e('High Performance', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">

                                <div class="setting-item enhanced">
                                    <label for="threshold" class="setting-label">
                                        <strong><?php _e('Loading Threshold Distance', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended: 200px', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-control">
                                        <div class="threshold-slider-container">
                                            <input type="range" id="threshold-slider" min="0" max="1000" step="50" value="<?php echo esc_attr($threshold); ?>" class="threshold-slider">
                                            <input type="number" name="settings[threshold]" id="threshold" value="<?php echo esc_attr($threshold); ?>" min="0" max="1000" step="50" class="threshold-input" <?php echo redco_auto_save_attrs('lazy-load', 'threshold'); ?>>
                                            <span class="threshold-unit">px</span>
                                        </div>
                                        <div class="setting-help">
                                            <span class="help-icon" title="<?php _e('Distance before viewport to start loading images', 'redco-optimizer'); ?>">?</span>
                                        </div>
                                    </div>
                                    <div class="setting-description">
                                        <p><?php _e('Distance in pixels before an image enters the viewport to start loading. Lower values save more bandwidth, higher values provide smoother scrolling.', 'redco-optimizer'); ?></p>
                                        <div class="threshold-recommendations">
                                            <div class="threshold-preset" data-value="0">
                                                <span class="preset-label"><?php _e('Maximum Savings', 'redco-optimizer'); ?></span>
                                                <span class="preset-value">0px</span>
                                            </div>
                                            <div class="threshold-preset recommended" data-value="200">
                                                <span class="preset-label"><?php _e('Balanced', 'redco-optimizer'); ?></span>
                                                <span class="preset-value">200px</span>
                                            </div>
                                            <div class="threshold-preset" data-value="500">
                                                <span class="preset-label"><?php _e('Smooth Experience', 'redco-optimizer'); ?></span>
                                                <span class="preset-value">500px</span>
                                            </div>
                                        </div>
                                        <div class="setting-impact">
                                            <span class="impact-info">
                                                <span class="impact-label"><?php _e('Bandwidth Savings:', 'redco-optimizer'); ?></span>
                                                <span class="impact-value" id="threshold-savings"><?php _e('High', 'redco-optimizer'); ?></span>
                                            </span>
                                            <span class="smoothness-info">
                                                <span class="smoothness-label"><?php _e('Scroll Smoothness:', 'redco-optimizer'); ?></span>
                                                <span class="smoothness-value" id="threshold-smoothness"><?php _e('Good', 'redco-optimizer'); ?></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Exclusion Settings Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-format-image"></span>
                                <?php _e('Image Type Exclusions', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="exclusion-intro">
                                <div class="exclusion-summary">
                                    <span class="total-exclusions"><?php _e('Exclusion rules available:', 'redco-optimizer'); ?> <strong id="total-exclusion-count"><?php echo redco_is_woocommerce_active() ? '2' : '1'; ?></strong></span>
                                    <span class="active-exclusions"><?php _e('Active exclusions:', 'redco-optimizer'); ?> <strong id="active-exclusion-count">0</strong></span>
                                </div>
                            </div>

                            <div class="exclusion-section">

                                <div class="exclusion-options">
                                    <div class="exclusion-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[exclude_featured]" value="1" <?php checked($exclude_featured); ?> class="exclusion-checkbox" <?php echo redco_auto_save_attrs('lazy-load', 'exclude_featured'); ?>>
                                            <span class="option-icon">🖼️</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Featured Images (Post Thumbnails)', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Excludes featured images and post thumbnails from lazy loading. These images are often above the fold and critical for LCP (Largest Contentful Paint) performance.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Performance Impact:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('High', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('SEO Benefit:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('High', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <?php if (redco_is_woocommerce_active()): ?>
                                    <div class="exclusion-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[exclude_woocommerce]" value="1" <?php checked($exclude_woocommerce); ?> class="exclusion-checkbox" <?php echo redco_auto_save_attrs('lazy-load', 'exclude_woocommerce'); ?>>
                                            <span class="option-icon">🛒</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('WooCommerce Product Images', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Excludes WooCommerce product images from lazy loading. Product images are crucial for conversion rates and should be immediately visible to customers.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Conversion Impact:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('High', 'redco-optimizer'); ?></span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('User Experience:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('Critical', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="exclusion-summary-footer">
                                    <div class="summary-stats">
                                        <span class="enabled-exclusions">0 <?php _e('exclusions enabled', 'redco-optimizer'); ?></span>
                                        <span class="performance-impact"><?php _e('Performance impact: Optimal', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="exclusion-tips">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('Tip: Enable exclusions for images that need to load immediately for best user experience.', 'redco-optimizer'); ?>
                                    </div>
                                </div>

                                <div class="advanced-exclusions">
                                    <h5 class="advanced-title">
                                        <span class="dashicons dashicons-admin-generic"></span>
                                        <?php _e('Advanced Exclusion Methods', 'redco-optimizer'); ?>
                                    </h5>
                                    <div class="advanced-content">

                                        <div class="code-examples">
                                            <div class="code-example">
                                                <span class="code-label"><?php _e('CSS Classes:', 'redco-optimizer'); ?></span>
                                                <code class="code-snippet">skip-lazy, no-lazy, lazy-ignore</code>
                                            </div>
                                            <div class="code-example">
                                                <span class="code-label"><?php _e('HTML Example:', 'redco-optimizer'); ?></span>
                                                <code class="code-snippet">&lt;img src="image.jpg" class="skip-lazy" alt="..."&gt;</code>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Module Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Module Statistics', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-images">
                                <span class="stat-value"><?php echo number_format($stats['images_processed']); ?></span>
                                <span class="stat-label"><?php _e('Images Processed', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-bandwidth">
                                <span class="stat-value"><?php echo redco_format_bytes($stats['bytes_saved']); ?></span>
                                <span class="stat-label"><?php _e('Bandwidth Saved', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-threshold">
                                <span class="stat-value"><?php echo $threshold; ?>px</span>
                                <span class="stat-label"><?php _e('Load Threshold', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-featured">
                                <span class="stat-value"><?php echo $exclude_featured ? __('Yes', 'redco-optimizer') : __('No', 'redco-optimizer'); ?></span>
                                <span class="stat-label"><?php _e('Exclude Featured', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Impact -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-performance"></span>
                            <?php _e('Performance Impact', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <p><strong><?php _e('Estimated Improvements:', 'redco-optimizer'); ?></strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><?php _e('Faster initial page load times', 'redco-optimizer'); ?></li>
                            <li><?php _e('Reduced bandwidth usage', 'redco-optimizer'); ?></li>
                            <li><?php _e('Better user experience on slow connections', 'redco-optimizer'); ?></li>
                            <li><?php _e('Improved Core Web Vitals scores', 'redco-optimizer'); ?></li>
                        </ul>
                    </div>
                </div>

                <!-- Phase 3 Consolidation: Shared Optimization Panel -->
                <?php
                if (class_exists('Redco_Shared_Optimization_Settings')) {
                    Redco_Shared_Optimization_Settings::render_performance_optimization_panel('lazy-load', $settings);
                }
                ?>

                <!-- Technical Information -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Technical Details', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <p><strong><?php _e('Implementation:', 'redco-optimizer'); ?></strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px; font-size: 12px;">
                            <li><?php _e('Uses modern Intersection Observer API', 'redco-optimizer'); ?></li>
                            <li><?php _e('Falls back to scroll event listener', 'redco-optimizer'); ?></li>
                            <li><?php _e('Includes native loading="lazy" attribute', 'redco-optimizer'); ?></li>
                        </ul>
                        <p><strong><?php _e('Exclusion Classes:', 'redco-optimizer'); ?></strong></p>
                        <p style="font-size: 12px; font-family: monospace; background: #f5f5f5; padding: 8px; border-radius: 4px;">
                            skip-lazy, no-lazy, lazy-ignore
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-format-image"></span>
            <h3><?php _e('Lazy Load Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to improve page load times by loading images only when they enter the viewport.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Phase 3 Consolidation: Shared Optimization JavaScript -->
<?php
if (class_exists('Redco_Shared_Optimization_Settings')) {
    Redco_Shared_Optimization_Settings::render_optimization_javascript();
}
?>

<script>
jQuery(document).ready(function($) {
    // Lazy Load Module Enhanced Functionality

    // Threshold slider and input synchronization
    function syncThresholdControls() {
        const slider = $('#threshold-slider');
        const input = $('#threshold');

        slider.on('input', function() {
            const value = $(this).val();
            input.val(value);
            updateThresholdImpact(value);
        });

        input.on('input', function() {
            const value = $(this).val();
            slider.val(value);
            updateThresholdImpact(value);
        });
    }

    // Update threshold impact indicators
    function updateThresholdImpact(threshold) {
        const thresholdValue = parseInt(threshold);
        let savings, smoothness;

        if (thresholdValue <= 100) {
            savings = '<?php _e("Very High", "redco-optimizer"); ?>';
            smoothness = '<?php _e("Basic", "redco-optimizer"); ?>';
        } else if (thresholdValue <= 300) {
            savings = '<?php _e("High", "redco-optimizer"); ?>';
            smoothness = '<?php _e("Good", "redco-optimizer"); ?>';
        } else if (thresholdValue <= 500) {
            savings = '<?php _e("Medium", "redco-optimizer"); ?>';
            smoothness = '<?php _e("Very Good", "redco-optimizer"); ?>';
        } else {
            savings = '<?php _e("Low", "redco-optimizer"); ?>';
            smoothness = '<?php _e("Excellent", "redco-optimizer"); ?>';
        }

        $('#threshold-savings').text(savings);
        $('#threshold-smoothness').text(smoothness);

        // Update impact colors
        $('#threshold-savings').removeClass('high medium low').addClass(
            thresholdValue <= 200 ? 'high' : (thresholdValue <= 400 ? 'medium' : 'low')
        );
    }

    // Threshold preset buttons
    $('.threshold-preset').on('click', function() {
        const value = $(this).data('value');
        $('#threshold-slider').val(value);
        $('#threshold').val(value);
        updateThresholdImpact(value);

        // Visual feedback
        $('.threshold-preset').removeClass('active');
        $(this).addClass('active');

        // Highlight animation
        $(this).addClass('preset-selected');
        setTimeout(() => {
            $(this).removeClass('preset-selected');
        }, 300);
    });

    // Optimize for Performance button
    $('#optimize-for-performance').on('click', function(e) {
        e.preventDefault();

        const button = $(this);

        // Prevent multiple clicks
        if (button.hasClass('loading')) {
            return;
        }

        button.addClass('loading').text('<?php _e("Optimizing...", "redco-optimizer"); ?>');

        // Set optimal performance settings
        setTimeout(() => {
            $('#threshold-slider').val(0);
            $('#threshold').val(0);
            $('.exclusion-checkbox').prop('checked', true);

            updateThresholdImpact(0);
            updateExclusionSummary();

            // Update preset selection
            $('.threshold-preset').removeClass('active');
            $('.threshold-preset[data-value="0"]').addClass('active');

            // Show success message
            if (typeof showToast === 'function') {
                showToast('<?php _e("Settings optimized for maximum performance!", "redco-optimizer"); ?>', 'success');
            }

            // Trigger auto-save using the global auto-save function
            // Wait a moment for the UI updates to complete, then trigger auto-save
            setTimeout(() => {
                const $form = $('.redco-module-form[data-module="lazy-load"]');
                if ($form.length && typeof window.performAutoSave === 'function') {
                    window.performAutoSave($form, 'lazy-load');
                } else {
                    // Fallback: trigger change events to activate auto-save listeners
                    $('input[name="settings[threshold]"]').trigger('change');
                    $('input[name="settings[exclude_featured]"]').trigger('change');
                    $('input[name="settings[exclude_woocommerce]"]').trigger('change');
                }
            }, 200);

            // Reset button after a short delay
            setTimeout(() => {
                button.removeClass('loading').text('<?php _e("Optimize", "redco-optimizer"); ?>');
            }, 1000);

        }, 500);
    });

    // Reset to Defaults button
    $('#reset-lazy-settings').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Resetting...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Reset to default values
            $('#threshold-slider').val(200);
            $('#threshold').val(200);
            $('.exclusion-checkbox').prop('checked', false);

            updateThresholdImpact(200);
            updateExclusionSummary();

            // Update preset selection
            $('.threshold-preset').removeClass('active');
            $('.threshold-preset[data-value="200"]').addClass('active');

            button.removeClass('loading').text('<?php _e("Reset to Defaults", "redco-optimizer"); ?>');

            // Use global toast notification system
            if (typeof showToast === 'function') {
                showToast('<?php _e("Settings reset to defaults", "redco-optimizer"); ?>', 'info');
            }
        }, 500);
    });

    // Enable Recommended Exclusions button
    $('#enable-recommended-exclusions').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        const recommendedCheckboxes = $('.exclusion-option.recommended .exclusion-checkbox');
        const allRecommendedChecked = recommendedCheckboxes.length === recommendedCheckboxes.filter(':checked').length;

        if (allRecommendedChecked) {
            // Disable recommended
            recommendedCheckboxes.prop('checked', false);
            button.text('<?php _e("Enable Recommended", "redco-optimizer"); ?>');
            button.removeClass('button-primary').addClass('button-secondary');
        } else {
            // Enable recommended
            recommendedCheckboxes.prop('checked', true);
            button.text('<?php _e("Disable Recommended", "redco-optimizer"); ?>');
            button.removeClass('button-secondary').addClass('button-primary');
        }

        updateExclusionSummary();

        // Visual feedback
        $('.exclusion-option.recommended').addClass('selection-highlight');
        setTimeout(() => {
            $('.exclusion-option.recommended').removeClass('selection-highlight');
        }, 300);
    });

    // Disable All Exclusions button
    $('#disable-all-exclusions').on('click', function(e) {
        e.preventDefault();

        $('.exclusion-checkbox').prop('checked', false);
        updateExclusionSummary();

        // Update other button state
        $('#enable-recommended-exclusions').text('<?php _e("Enable Recommended", "redco-optimizer"); ?>');
        $('#enable-recommended-exclusions').removeClass('button-primary').addClass('button-secondary');

        // Visual feedback
        $('.exclusion-option').addClass('selection-highlight');
        setTimeout(() => {
            $('.exclusion-option').removeClass('selection-highlight');
        }, 300);
    });

    // Update exclusion summary
    function updateExclusionSummary() {
        const totalExclusions = $('.exclusion-checkbox').length;
        const activeExclusions = $('.exclusion-checkbox:checked').length;

        $('#total-exclusion-count').text(totalExclusions);
        $('#active-exclusion-count').text(activeExclusions);
        $('.enabled-exclusions').text(activeExclusions + ' <?php _e("exclusions enabled", "redco-optimizer"); ?>');

        // Update performance impact
        let impact = '<?php _e("Optimal", "redco-optimizer"); ?>';
        if (activeExclusions === 0) {
            impact = '<?php _e("Maximum Savings", "redco-optimizer"); ?>';
        } else if (activeExclusions === totalExclusions) {
            impact = '<?php _e("Balanced", "redco-optimizer"); ?>';
        }

        $('.performance-impact').text('<?php _e("Performance impact:", "redco-optimizer"); ?> ' + impact);
    }

    // Individual exclusion checkbox changes
    $('.exclusion-checkbox').on('change', function() {
        updateExclusionSummary();

        // Update button states
        const recommendedCheckboxes = $('.exclusion-option.recommended .exclusion-checkbox');
        const allRecommendedChecked = recommendedCheckboxes.length === recommendedCheckboxes.filter(':checked').length;
        const onlyRecommendedChecked = allRecommendedChecked && ($('.exclusion-checkbox:checked').length === recommendedCheckboxes.length);

        if (onlyRecommendedChecked) {
            $('#enable-recommended-exclusions').text('<?php _e("Disable Recommended", "redco-optimizer"); ?>');
            $('#enable-recommended-exclusions').removeClass('button-secondary').addClass('button-primary');
        } else {
            $('#enable-recommended-exclusions').text('<?php _e("Enable Recommended", "redco-optimizer"); ?>');
            $('#enable-recommended-exclusions').removeClass('button-primary').addClass('button-secondary');
        }
    });

    // Page-specific notification system removed - using global toast notifications

    // Initialize
    syncThresholdControls();
    updateThresholdImpact($('#threshold').val());
    updateExclusionSummary();

    // Set initial preset selection
    const currentThreshold = $('#threshold').val();
    $('.threshold-preset[data-value="' + currentThreshold + '"]').addClass('active');

    // Lazy Load module enhanced functionality loaded
});
</script>


