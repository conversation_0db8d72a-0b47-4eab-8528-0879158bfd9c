<?php
/**
 * Plugin Name: Redco Diagnostic & Auto-Fix
 * Plugin URI: https://redco.io/diagnostic-autofix
 * Description: Advanced WordPress diagnostic scanning and automated fixing system with real-time monitoring, Core Web Vitals integration, and intelligent optimization recommendations.
 * Version: 1.0.0
 * Author: Redco Team
 * Author URI: https://redco.io
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: redco-diagnostic-autofix
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: true
 * 
 * @package Redco_Diagnostic_AutoFix
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('REDCO_DIAGNOSTIC_VERSION', '1.0.0');
define('REDCO_DIAGNOSTIC_PLUGIN_FILE', __FILE__);
define('REDCO_DIAGNOSTIC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('REDCO_DIAGNOSTIC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('REDCO_DIAGNOSTIC_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Minimum requirements check
if (version_compare(PHP_VERSION, '7.4', '<')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo '<strong>Redco Diagnostic & Auto-Fix:</strong> ';
        echo 'This plugin requires PHP 7.4 or higher. You are running PHP ' . PHP_VERSION . '.';
        echo '</p></div>';
    });
    return;
}

if (version_compare(get_bloginfo('version'), '5.0', '<')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo '<strong>Redco Diagnostic & Auto-Fix:</strong> ';
        echo 'This plugin requires WordPress 5.0 or higher. You are running WordPress ' . get_bloginfo('version') . '.';
        echo '</p></div>';
    });
    return;
}

/**
 * Main plugin class
 */
class Redco_Standalone_Diagnostic_Plugin {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Activation/Deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Plugin loaded hook
        add_action('plugins_loaded', array($this, 'init'));
        
        // Admin hooks
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // AJAX hooks
        add_action('wp_ajax_redco_diagnostic_scan', array($this, 'ajax_diagnostic_scan'));
        add_action('wp_ajax_redco_diagnostic_apply_fixes', array($this, 'ajax_apply_fixes'));
        
        // Cron hooks
        add_action('redco_diagnostic_scheduled_scan', array($this, 'run_scheduled_scan'));
        
        // Network admin hooks (for multisite)
        if (is_multisite()) {
            add_action('network_admin_menu', array($this, 'add_network_admin_menu'));
        }
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        // Load helper functions
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'includes/helpers.php';
        
        // Load core classes
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'includes/class-standalone-diagnostic-engine.php';
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'includes/class-standalone-diagnostic-helpers.php';

        // Load Phase 1 enhancements
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'safety/class-standalone-tiered-fix-system.php';
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'safety/class-standalone-enhanced-backup.php';
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'scheduling/class-standalone-fix-scheduler.php';

        // Load Phase 2 enhancements
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'monitoring/class-standalone-realtime-monitor.php';
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'monitoring/class-standalone-core-web-vitals.php';

        // Load admin classes
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'admin/class-standalone-admin-page.php';
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain
        load_plugin_textdomain('redco-diagnostic-autofix', false, dirname(REDCO_DIAGNOSTIC_PLUGIN_BASENAME) . '/languages');
        
        // Initialize components
        $this->init_components();
        
        // Check for database updates
        $this->maybe_update_database();
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components() {
        // Initialize diagnostic engine
        if (class_exists('Redco_Standalone_Diagnostic_Engine')) {
            $diagnostic_engine = new Redco_Standalone_Diagnostic_Engine();
            $diagnostic_engine->init();
        }

        // Initialize tiered fix system
        if (class_exists('Redco_Standalone_Tiered_Fix_System')) {
            $tiered_system = new Redco_Standalone_Tiered_Fix_System();
            $tiered_system->init();
        }

        // Initialize enhanced backup system
        if (class_exists('Redco_Standalone_Enhanced_Backup')) {
            $backup_system = new Redco_Standalone_Enhanced_Backup();
            $backup_system->init();
        }

        // Initialize fix scheduler
        if (class_exists('Redco_Standalone_Fix_Scheduler')) {
            $scheduler = new Redco_Standalone_Fix_Scheduler();
            $scheduler->init();
        }

        // Initialize real-time monitoring
        if (class_exists('Redco_Standalone_Realtime_Monitor')) {
            $monitor = new Redco_Standalone_Realtime_Monitor();
            $monitor->init();
        }

        // Initialize Core Web Vitals
        if (class_exists('Redco_Standalone_Core_Web_Vitals')) {
            $vitals = new Redco_Standalone_Core_Web_Vitals();
            $vitals->init();
        }
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Redco Diagnostic', 'redco-diagnostic-autofix'),
            __('Redco Diagnostic', 'redco-diagnostic-autofix'),
            'manage_options',
            'redco-diagnostic',
            array($this, 'admin_page'),
            'dashicons-search',
            30
        );
        
        // Add submenu pages
        add_submenu_page(
            'redco-diagnostic',
            __('Dashboard', 'redco-diagnostic-autofix'),
            __('Dashboard', 'redco-diagnostic-autofix'),
            'manage_options',
            'redco-diagnostic',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'redco-diagnostic',
            __('Real-time Monitoring', 'redco-diagnostic-autofix'),
            __('Monitoring', 'redco-diagnostic-autofix'),
            'manage_options',
            'redco-diagnostic-monitoring',
            array($this, 'monitoring_page')
        );
        
        add_submenu_page(
            'redco-diagnostic',
            __('Core Web Vitals', 'redco-diagnostic-autofix'),
            __('Core Web Vitals', 'redco-diagnostic-autofix'),
            'manage_options',
            'redco-diagnostic-vitals',
            array($this, 'vitals_page')
        );
        
        add_submenu_page(
            'redco-diagnostic',
            __('Settings', 'redco-diagnostic-autofix'),
            __('Settings', 'redco-diagnostic-autofix'),
            'manage_options',
            'redco-diagnostic-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * Add network admin menu (for multisite)
     */
    public function add_network_admin_menu() {
        add_menu_page(
            __('Redco Diagnostic Network', 'redco-diagnostic-autofix'),
            __('Redco Diagnostic', 'redco-diagnostic-autofix'),
            'manage_network_options',
            'redco-diagnostic-network',
            array($this, 'network_admin_page'),
            'dashicons-search',
            30
        );
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on plugin pages
        if (strpos($hook, 'redco-diagnostic') === false) {
            return;
        }
        
        // Enqueue CSS
        wp_enqueue_style(
            'redco-diagnostic-admin',
            REDCO_DIAGNOSTIC_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            REDCO_DIAGNOSTIC_VERSION
        );
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'redco-diagnostic-admin',
            REDCO_DIAGNOSTIC_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            REDCO_DIAGNOSTIC_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('redco-diagnostic-admin', 'redcoDiagnosticAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('redco_diagnostic_nonce'),
            'strings' => array(
                'scanning' => __('Running diagnostic scan...', 'redco-diagnostic-autofix'),
                'applying_fixes' => __('Applying fixes...', 'redco-diagnostic-autofix'),
                'scan_complete' => __('Scan completed!', 'redco-diagnostic-autofix'),
                'fixes_applied' => __('Fixes applied successfully!', 'redco-diagnostic-autofix'),
                'error' => __('An error occurred. Please try again.', 'redco-diagnostic-autofix')
            )
        ));
    }
    
    /**
     * Maybe update database
     */
    private function maybe_update_database() {
        $current_version = get_option('redco_diagnostic_db_version', '0.0.0');

        if (version_compare($current_version, REDCO_DIAGNOSTIC_VERSION, '<')) {
            self::create_database_tables();
            update_option('redco_diagnostic_db_version', REDCO_DIAGNOSTIC_VERSION);
        }
    }
    
    /**
     * Admin page callback
     */
    public function admin_page() {
        if (class_exists('Redco_Standalone_Admin_Page')) {
            $admin_page = new Redco_Standalone_Admin_Page();
            $admin_page->render();
        }
    }

    /**
     * Plugin activation
     */
    public static function activate($network_wide = false) {
        // Set default options
        $default_options = array(
            'version' => REDCO_DIAGNOSTIC_VERSION,
            'installed_at' => current_time('mysql'),
            'auto_scan_enabled' => false,
            'notification_settings' => array(
                'performance_alerts' => false,
                'scheduled_fix_completion' => false
            )
        );

        add_option('redco_diagnostic_options', $default_options);

        // Create database tables
        self::create_database_tables();

        // Schedule initial scan
        if (!wp_next_scheduled('redco_diagnostic_daily_scan')) {
            wp_schedule_event(time() + 3600, 'daily', 'redco_diagnostic_daily_scan');
        }

        // Log activation
        if (function_exists('redco_diagnostic_log')) {
            redco_diagnostic_log('Plugin activated successfully');
        }
    }

    /**
     * Plugin deactivation
     */
    public static function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook('redco_diagnostic_daily_scan');
        wp_clear_scheduled_hook('redco_diagnostic_cleanup_backups');
        wp_clear_scheduled_hook('redco_diagnostic_cleanup_scheduled_fixes');
        wp_clear_scheduled_hook('redco_diagnostic_collect_metrics');
        wp_clear_scheduled_hook('redco_diagnostic_collect_web_vitals');

        // Log deactivation
        if (function_exists('redco_diagnostic_log')) {
            redco_diagnostic_log('Plugin deactivated');
        }
    }

    /**
     * Create database tables
     */
    private static function create_database_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Tables to create
        $tables = array(
            // Diagnostic results table
            $wpdb->prefix . 'redco_diagnostic_results' => "CREATE TABLE " . $wpdb->prefix . "redco_diagnostic_results (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                scan_id varchar(32) NOT NULL,
                timestamp datetime NOT NULL,
                scan_type varchar(50) NOT NULL DEFAULT 'comprehensive',
                issues_found int(11) NOT NULL DEFAULT 0,
                critical_issues int(11) NOT NULL DEFAULT 0,
                auto_fixable int(11) NOT NULL DEFAULT 0,
                performance_score int(3) NOT NULL DEFAULT 0,
                health_score int(3) NOT NULL DEFAULT 0,
                scan_data longtext,
                PRIMARY KEY (id),
                KEY scan_id (scan_id),
                KEY timestamp (timestamp),
                KEY scan_type (scan_type)
            ) {$charset_collate};",

            // Backups table
            $wpdb->prefix . 'redco_diagnostic_backups' => "CREATE TABLE " . $wpdb->prefix . "redco_diagnostic_backups (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                backup_id varchar(50) NOT NULL,
                type varchar(20) NOT NULL DEFAULT 'manual',
                description text,
                created_at datetime NOT NULL,
                created_by bigint(20) unsigned NOT NULL,
                size bigint(20) unsigned NOT NULL DEFAULT 0,
                fix_context text,
                backup_data longtext,
                PRIMARY KEY (id),
                UNIQUE KEY backup_id (backup_id),
                KEY created_at (created_at),
                KEY type (type)
            ) {$charset_collate};",

            // Scheduled fixes table
            $wpdb->prefix . 'redco_diagnostic_scheduled_fixes' => "CREATE TABLE " . $wpdb->prefix . "redco_diagnostic_scheduled_fixes (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                schedule_id varchar(50) NOT NULL,
                fix_id varchar(100) NOT NULL,
                scheduled_time datetime NOT NULL,
                status varchar(20) NOT NULL DEFAULT 'scheduled',
                fix_data text,
                notes text,
                created_by bigint(20) unsigned NOT NULL,
                created_at datetime NOT NULL,
                executed_at datetime NULL,
                result_message text,
                backup_id varchar(50) NULL,
                PRIMARY KEY (id),
                UNIQUE KEY schedule_id (schedule_id),
                KEY fix_id (fix_id),
                KEY scheduled_time (scheduled_time),
                KEY status (status)
            ) {$charset_collate};",

            // Performance metrics table
            $wpdb->prefix . 'redco_diagnostic_performance_metrics' => "CREATE TABLE " . $wpdb->prefix . "redco_diagnostic_performance_metrics (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                timestamp datetime NOT NULL,
                page_load_time float NOT NULL DEFAULT 0,
                memory_usage bigint(20) unsigned NOT NULL DEFAULT 0,
                peak_memory bigint(20) unsigned NOT NULL DEFAULT 0,
                database_queries int(11) NOT NULL DEFAULT 0,
                cache_hit_ratio float NOT NULL DEFAULT 0,
                error_rate float NOT NULL DEFAULT 0,
                server_response_time float NOT NULL DEFAULT 0,
                metrics_data longtext,
                PRIMARY KEY (id),
                KEY timestamp (timestamp)
            ) {$charset_collate};",

            // Core Web Vitals table
            $wpdb->prefix . 'redco_diagnostic_core_web_vitals' => "CREATE TABLE " . $wpdb->prefix . "redco_diagnostic_core_web_vitals (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                timestamp datetime NOT NULL,
                page_url varchar(500),
                lcp float NOT NULL DEFAULT 0,
                fid float NOT NULL DEFAULT 0,
                cls float NOT NULL DEFAULT 0,
                fcp float NOT NULL DEFAULT 0,
                ttfb float NOT NULL DEFAULT 0,
                overall_score int(3) NOT NULL DEFAULT 0,
                vitals_data longtext,
                user_agent text,
                PRIMARY KEY (id),
                KEY timestamp (timestamp),
                KEY page_url (page_url(255)),
                KEY overall_score (overall_score)
            ) {$charset_collate};",

            // Frontend metrics table
            $wpdb->prefix . 'redco_diagnostic_frontend_metrics' => "CREATE TABLE " . $wpdb->prefix . "redco_diagnostic_frontend_metrics (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                timestamp datetime NOT NULL,
                page_url varchar(500),
                user_agent text,
                metrics_data longtext,
                PRIMARY KEY (id),
                KEY timestamp (timestamp)
            ) {$charset_collate};"
        );

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        foreach ($tables as $table_name => $sql) {
            if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
                dbDelta($sql);
            }
        }

        // Update database version
        update_option('redco_diagnostic_db_version', REDCO_DIAGNOSTIC_VERSION);
    }
    
    /**
     * Monitoring page callback
     */
    public function monitoring_page() {
        include REDCO_DIAGNOSTIC_PLUGIN_DIR . 'admin/monitoring-page.php';
    }
    
    /**
     * Core Web Vitals page callback
     */
    public function vitals_page() {
        include REDCO_DIAGNOSTIC_PLUGIN_DIR . 'admin/vitals-page.php';
    }
    
    /**
     * Settings page callback
     */
    public function settings_page() {
        include REDCO_DIAGNOSTIC_PLUGIN_DIR . 'admin/settings-page.php';
    }
    
    /**
     * Network admin page callback
     */
    public function network_admin_page() {
        echo '<div class="wrap"><h1>Network Admin - Coming Soon</h1></div>';
    }

    /**
     * AJAX handlers (placeholder methods)
     */
    public function ajax_diagnostic_scan() {
        wp_send_json_error('Method not implemented yet');
    }

    public function ajax_apply_fixes() {
        wp_send_json_error('Method not implemented yet');
    }

    public function run_scheduled_scan() {
        // Placeholder for scheduled scan
    }
}

// Register activation and deactivation hooks
register_activation_hook(__FILE__, array('Redco_Standalone_Diagnostic_Plugin', 'activate'));
register_deactivation_hook(__FILE__, array('Redco_Standalone_Diagnostic_Plugin', 'deactivate'));

// Initialize the plugin
Redco_Standalone_Diagnostic_Plugin::get_instance();
