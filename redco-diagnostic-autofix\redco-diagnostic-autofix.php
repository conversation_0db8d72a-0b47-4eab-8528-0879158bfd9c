<?php
/**
 * Plugin Name: Redco Diagnostic & Auto-Fix
 * Plugin URI: https://redco.io/diagnostic-autofix
 * Description: Advanced WordPress diagnostic scanning and automated fixing system with real-time monitoring, Core Web Vitals integration, and intelligent optimization recommendations.
 * Version: 1.0.0
 * Author: Redco Team
 * Author URI: https://redco.io
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: redco-diagnostic-autofix
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: true
 * 
 * @package Redco_Diagnostic_AutoFix
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('REDCO_DIAGNOSTIC_VERSION', '1.0.0');
define('REDCO_DIAGNOSTIC_PLUGIN_FILE', __FILE__);
define('REDCO_DIAGNOSTIC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('REDCO_DIAGNOSTIC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('REDCO_DIAGNOSTIC_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Minimum requirements check
if (version_compare(PHP_VERSION, '7.4', '<')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo '<strong>Redco Diagnostic & Auto-Fix:</strong> ';
        echo 'This plugin requires PHP 7.4 or higher. You are running PHP ' . PHP_VERSION . '.';
        echo '</p></div>';
    });
    return;
}

if (version_compare(get_bloginfo('version'), '5.0', '<')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo '<strong>Redco Diagnostic & Auto-Fix:</strong> ';
        echo 'This plugin requires WordPress 5.0 or higher. You are running WordPress ' . get_bloginfo('version') . '.';
        echo '</p></div>';
    });
    return;
}

/**
 * Main plugin class
 */
class Redco_Diagnostic_AutoFix_Plugin {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Activation/Deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Plugin loaded hook
        add_action('plugins_loaded', array($this, 'init'));
        
        // Admin hooks
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // AJAX hooks
        add_action('wp_ajax_redco_diagnostic_scan', array($this, 'ajax_diagnostic_scan'));
        add_action('wp_ajax_redco_diagnostic_apply_fixes', array($this, 'ajax_apply_fixes'));
        
        // Cron hooks
        add_action('redco_diagnostic_scheduled_scan', array($this, 'run_scheduled_scan'));
        
        // Network admin hooks (for multisite)
        if (is_multisite()) {
            add_action('network_admin_menu', array($this, 'add_network_admin_menu'));
        }
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        // Load helper functions
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'includes/helpers.php';
        
        // Load core classes
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'includes/class-diagnostic-engine.php';
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'includes/class-diagnostic-helpers.php';
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'includes/class-settings-manager.php';
        
        // Load Phase 1 enhancements
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'safety/class-tiered-fix-system.php';
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'safety/class-enhanced-backup.php';
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'scheduling/class-fix-scheduler.php';
        
        // Load Phase 2 enhancements
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'monitoring/class-realtime-monitor.php';
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'monitoring/class-core-web-vitals.php';
        
        // Load admin classes
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'admin/class-admin-page.php';
        require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'admin/class-dashboard.php';
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain
        load_plugin_textdomain('redco-diagnostic-autofix', false, dirname(REDCO_DIAGNOSTIC_PLUGIN_BASENAME) . '/languages');
        
        // Initialize components
        $this->init_components();
        
        // Check for database updates
        $this->maybe_update_database();
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components() {
        // Initialize diagnostic engine
        if (class_exists('Redco_Diagnostic_Engine')) {
            $diagnostic_engine = new Redco_Diagnostic_Engine();
            $diagnostic_engine->init();
        }
        
        // Initialize tiered fix system
        if (class_exists('Redco_Diagnostic_Tiered_Fix_System')) {
            $tiered_system = new Redco_Diagnostic_Tiered_Fix_System();
            $tiered_system->init();
        }
        
        // Initialize enhanced backup system
        if (class_exists('Redco_Diagnostic_Enhanced_Backup')) {
            $backup_system = new Redco_Diagnostic_Enhanced_Backup();
            $backup_system->init();
        }
        
        // Initialize fix scheduler
        if (class_exists('Redco_Diagnostic_Fix_Scheduler')) {
            $scheduler = new Redco_Diagnostic_Fix_Scheduler();
            $scheduler->init();
        }
        
        // Initialize real-time monitoring
        if (class_exists('Redco_Diagnostic_Realtime_Monitor')) {
            $monitor = new Redco_Diagnostic_Realtime_Monitor();
            $monitor->init();
        }
        
        // Initialize Core Web Vitals
        if (class_exists('Redco_Diagnostic_Core_Web_Vitals')) {
            $vitals = new Redco_Diagnostic_Core_Web_Vitals();
            $vitals->init();
        }
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Redco Diagnostic', 'redco-diagnostic-autofix'),
            __('Redco Diagnostic', 'redco-diagnostic-autofix'),
            'manage_options',
            'redco-diagnostic',
            array($this, 'admin_page'),
            'dashicons-search',
            30
        );
        
        // Add submenu pages
        add_submenu_page(
            'redco-diagnostic',
            __('Dashboard', 'redco-diagnostic-autofix'),
            __('Dashboard', 'redco-diagnostic-autofix'),
            'manage_options',
            'redco-diagnostic',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'redco-diagnostic',
            __('Real-time Monitoring', 'redco-diagnostic-autofix'),
            __('Monitoring', 'redco-diagnostic-autofix'),
            'manage_options',
            'redco-diagnostic-monitoring',
            array($this, 'monitoring_page')
        );
        
        add_submenu_page(
            'redco-diagnostic',
            __('Core Web Vitals', 'redco-diagnostic-autofix'),
            __('Core Web Vitals', 'redco-diagnostic-autofix'),
            'manage_options',
            'redco-diagnostic-vitals',
            array($this, 'vitals_page')
        );
        
        add_submenu_page(
            'redco-diagnostic',
            __('Settings', 'redco-diagnostic-autofix'),
            __('Settings', 'redco-diagnostic-autofix'),
            'manage_options',
            'redco-diagnostic-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * Add network admin menu (for multisite)
     */
    public function add_network_admin_menu() {
        add_menu_page(
            __('Redco Diagnostic Network', 'redco-diagnostic-autofix'),
            __('Redco Diagnostic', 'redco-diagnostic-autofix'),
            'manage_network_options',
            'redco-diagnostic-network',
            array($this, 'network_admin_page'),
            'dashicons-search',
            30
        );
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on plugin pages
        if (strpos($hook, 'redco-diagnostic') === false) {
            return;
        }
        
        // Enqueue CSS
        wp_enqueue_style(
            'redco-diagnostic-admin',
            REDCO_DIAGNOSTIC_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            REDCO_DIAGNOSTIC_VERSION
        );
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'redco-diagnostic-admin',
            REDCO_DIAGNOSTIC_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            REDCO_DIAGNOSTIC_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('redco-diagnostic-admin', 'redcoDiagnosticAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('redco_diagnostic_nonce'),
            'strings' => array(
                'scanning' => __('Running diagnostic scan...', 'redco-diagnostic-autofix'),
                'applying_fixes' => __('Applying fixes...', 'redco-diagnostic-autofix'),
                'scan_complete' => __('Scan completed!', 'redco-diagnostic-autofix'),
                'fixes_applied' => __('Fixes applied successfully!', 'redco-diagnostic-autofix'),
                'error' => __('An error occurred. Please try again.', 'redco-diagnostic-autofix')
            )
        ));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_database_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Schedule cron events
        $this->schedule_cron_events();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled cron events
        wp_clear_scheduled_hook('redco_diagnostic_scheduled_scan');
        wp_clear_scheduled_hook('redco_diagnostic_cleanup');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Create database tables
     */
    private function create_database_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Diagnostic results table
        $table_diagnostic_results = $wpdb->prefix . 'redco_diagnostic_results';
        $sql_diagnostic = "CREATE TABLE {$table_diagnostic_results} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            scan_id varchar(32) NOT NULL,
            timestamp datetime NOT NULL,
            scan_type varchar(50) NOT NULL DEFAULT 'comprehensive',
            issues_found int(11) NOT NULL DEFAULT 0,
            critical_issues int(11) NOT NULL DEFAULT 0,
            auto_fixable int(11) NOT NULL DEFAULT 0,
            performance_score int(3) NOT NULL DEFAULT 0,
            health_score int(3) NOT NULL DEFAULT 0,
            scan_data longtext,
            PRIMARY KEY (id),
            KEY scan_id (scan_id),
            KEY timestamp (timestamp)
        ) {$charset_collate};";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_diagnostic);
        
        // Update database version
        update_option('redco_diagnostic_db_version', '1.0.0');
    }
    
    /**
     * Admin page callback
     */
    public function admin_page() {
        if (class_exists('Redco_Diagnostic_Admin_Page')) {
            $admin_page = new Redco_Diagnostic_Admin_Page();
            $admin_page->render();
        }
    }
    
    /**
     * Monitoring page callback
     */
    public function monitoring_page() {
        include REDCO_DIAGNOSTIC_PLUGIN_DIR . 'admin/monitoring-page.php';
    }
    
    /**
     * Core Web Vitals page callback
     */
    public function vitals_page() {
        include REDCO_DIAGNOSTIC_PLUGIN_DIR . 'admin/vitals-page.php';
    }
    
    /**
     * Settings page callback
     */
    public function settings_page() {
        include REDCO_DIAGNOSTIC_PLUGIN_DIR . 'admin/settings-page.php';
    }
    
    /**
     * Network admin page callback
     */
    public function network_admin_page() {
        include REDCO_DIAGNOSTIC_PLUGIN_DIR . 'admin/network-admin-page.php';
    }
}

// Initialize the plugin
Redco_Diagnostic_AutoFix_Plugin::get_instance();
