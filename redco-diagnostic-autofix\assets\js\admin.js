/**
 * Admin JavaScript for Redco Diagnostic & Auto-Fix
 * 
 * Handles all frontend interactions and AJAX communications
 */

(function($) {
    'use strict';
    
    /**
     * Main Admin Controller
     */
    const RedcoDiagnosticAdmin = {
        
        /**
         * Initialize the admin interface
         */
        init: function() {
            this.bindEvents();
            this.initTabs();
            this.loadDashboardData();
            this.initCharts();
        },
        
        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Tab navigation
            $(document).on('click', '.redco-diagnostic-nav .nav-tab', this.handleTabClick);
            
            // Diagnostic scan
            $(document).on('click', '#start-diagnostic-scan', this.startDiagnosticScan);
            $(document).on('click', '#run-quick-scan', this.runQuickScan);
            
            // Fix application
            $(document).on('click', '#apply-safe-fixes', this.applySafeFixes);
            $(document).on('click', '.apply-fix-btn', this.applySingleFix);
            
            // Chart controls
            $(document).on('change', '#chart-period', this.updateChart);
            
            // Real-time monitoring
            this.startRealtimeMonitoring();
        },
        
        /**
         * Initialize tab system
         */
        initTabs: function() {
            // Show first tab by default
            $('.redco-tab-content').hide();
            $('.redco-tab-content.active').show();
        },
        
        /**
         * Handle tab clicks
         */
        handleTabClick: function(e) {
            e.preventDefault();
            
            const $tab = $(this);
            const tabId = $tab.data('tab');
            
            // Update active tab
            $('.nav-tab').removeClass('nav-tab-active');
            $tab.addClass('nav-tab-active');
            
            // Show corresponding content
            $('.redco-tab-content').removeClass('active').hide();
            $('#tab-' + tabId).addClass('active').show();
            
            // Load tab-specific data
            RedcoDiagnosticAdmin.loadTabData(tabId);
        },
        
        /**
         * Load dashboard data
         */
        loadDashboardData: function() {
            this.loadRecentActivity();
            this.updatePerformanceChart('7d');
        },
        
        /**
         * Load tab-specific data
         */
        loadTabData: function(tabId) {
            switch (tabId) {
                case 'monitoring':
                    this.loadMonitoringData();
                    break;
                case 'vitals':
                    this.loadVitalsData();
                    break;
                case 'scheduled':
                    this.loadScheduledFixes();
                    break;
                case 'backups':
                    this.loadBackups();
                    break;
            }
        },
        
        /**
         * Start diagnostic scan
         */
        startDiagnosticScan: function(e) {
            e.preventDefault();
            
            const scanType = $('input[name="scan_type"]:checked').val();
            
            // Show progress
            $('#scan-progress').show();
            $('#scan-results').hide();
            
            // Update progress
            RedcoDiagnosticAdmin.updateScanProgress(0, 'Initializing scan...');
            
            // Start scan
            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_diagnostic_scan',
                    nonce: redcoDiagnosticAjax.nonce,
                    scan_type: scanType
                },
                success: function(response) {
                    if (response.success) {
                        RedcoDiagnosticAdmin.displayScanResults(response.data);
                    } else {
                        RedcoDiagnosticAdmin.showError('Scan failed: ' + response.data);
                    }
                },
                error: function() {
                    RedcoDiagnosticAdmin.showError('Network error occurred during scan');
                }
            });
        },
        
        /**
         * Run quick scan from dashboard
         */
        runQuickScan: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const originalText = $button.html();
            
            $button.html('<span class="dashicons dashicons-update spin"></span> Scanning...').prop('disabled', true);
            
            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_diagnostic_scan',
                    nonce: redcoDiagnosticAjax.nonce,
                    scan_type: 'quick'
                },
                success: function(response) {
                    if (response.success) {
                        // Switch to diagnostic tab and show results
                        $('.nav-tab[data-tab="diagnostic"]').click();
                        RedcoDiagnosticAdmin.displayScanResults(response.data);
                        RedcoDiagnosticAdmin.showSuccess('Quick scan completed successfully!');
                    } else {
                        RedcoDiagnosticAdmin.showError('Quick scan failed: ' + response.data);
                    }
                },
                error: function() {
                    RedcoDiagnosticAdmin.showError('Network error occurred during quick scan');
                },
                complete: function() {
                    $button.html(originalText).prop('disabled', false);
                }
            });
        },
        
        /**
         * Update scan progress
         */
        updateScanProgress: function(percentage, message) {
            $('.redco-progress-fill').css('width', percentage + '%');
            $('.redco-progress-text').text(message);
        },
        
        /**
         * Display scan results
         */
        displayScanResults: function(data) {
            $('#scan-progress').hide();
            
            let html = '<div class="redco-card">';
            html += '<div class="redco-card-header">';
            html += '<h3><span class="dashicons dashicons-yes-alt"></span> Scan Results</h3>';
            html += '</div>';
            html += '<div class="redco-card-body">';
            
            // Summary
            html += '<div class="redco-scan-summary">';
            html += '<div class="redco-stat-grid">';
            html += '<div class="redco-stat">';
            html += '<div class="redco-stat-value">' + data.issues.length + '</div>';
            html += '<div class="redco-stat-label">Issues Found</div>';
            html += '</div>';
            html += '<div class="redco-stat">';
            html += '<div class="redco-stat-value">' + data.performance_score + '%</div>';
            html += '<div class="redco-stat-label">Performance Score</div>';
            html += '</div>';
            html += '<div class="redco-stat">';
            html += '<div class="redco-stat-value">' + data.health_score + '%</div>';
            html += '<div class="redco-stat-label">Health Score</div>';
            html += '</div>';
            html += '<div class="redco-stat">';
            html += '<div class="redco-stat-value">' + data.scan_duration + 's</div>';
            html += '<div class="redco-stat-label">Scan Duration</div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            
            // Issues list
            if (data.issues.length > 0) {
                html += '<div class="redco-issues-list">';
                html += '<h4>Issues Found:</h4>';
                
                data.issues.forEach(function(issue) {
                    html += '<div class="redco-issue-item ' + issue.severity + '">';
                    html += '<div class="redco-issue-header">';
                    html += '<h5>' + issue.title + '</h5>';
                    html += '<span class="redco-issue-severity ' + issue.severity + '">' + issue.severity + '</span>';
                    html += '</div>';
                    html += '<div class="redco-issue-description">' + issue.description + '</div>';
                    
                    if (issue.auto_fixable) {
                        html += '<div class="redco-issue-actions">';
                        html += '<button class="button apply-fix-btn" data-fix-id="' + issue.id + '">';
                        html += '<span class="dashicons dashicons-admin-tools"></span> Apply Fix';
                        html += '</button>';
                        html += '</div>';
                    }
                    
                    html += '</div>';
                });
                
                html += '</div>';
            } else {
                html += '<div class="redco-no-issues">';
                html += '<p><span class="dashicons dashicons-yes-alt"></span> No issues found! Your site is in excellent condition.</p>';
                html += '</div>';
            }
            
            html += '</div>';
            html += '</div>';
            
            $('#scan-results').html(html).show();
        },
        
        /**
         * Apply safe fixes
         */
        applySafeFixes: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const originalText = $button.html();
            
            $button.html('Applying fixes...').prop('disabled', true);
            
            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_diagnostic_apply_tier_fixes',
                    nonce: redcoDiagnosticAjax.nonce,
                    tier: 'safe'
                },
                success: function(response) {
                    if (response.success) {
                        RedcoDiagnosticAdmin.showSuccess('Safe fixes applied successfully!');
                        // Refresh dashboard data
                        RedcoDiagnosticAdmin.loadDashboardData();
                    } else {
                        RedcoDiagnosticAdmin.showError('Failed to apply fixes: ' + response.data);
                    }
                },
                error: function() {
                    RedcoDiagnosticAdmin.showError('Network error occurred while applying fixes');
                },
                complete: function() {
                    $button.html(originalText).prop('disabled', false);
                }
            });
        },
        
        /**
         * Apply single fix
         */
        applySingleFix: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const fixId = $button.data('fix-id');
            const originalText = $button.html();
            
            $button.html('<span class="dashicons dashicons-update spin"></span> Applying...').prop('disabled', true);
            
            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_diagnostic_apply_fixes',
                    nonce: redcoDiagnosticAjax.nonce,
                    fix_ids: [fixId]
                },
                success: function(response) {
                    if (response.success) {
                        $button.closest('.redco-issue-item').addClass('fixed');
                        $button.html('<span class="dashicons dashicons-yes-alt"></span> Fixed').removeClass('button').addClass('button-disabled');
                        RedcoDiagnosticAdmin.showSuccess('Fix applied successfully!');
                    } else {
                        RedcoDiagnosticAdmin.showError('Failed to apply fix: ' + response.data);
                        $button.html(originalText).prop('disabled', false);
                    }
                },
                error: function() {
                    RedcoDiagnosticAdmin.showError('Network error occurred while applying fix');
                    $button.html(originalText).prop('disabled', false);
                }
            });
        },
        
        /**
         * Load recent activity
         */
        loadRecentActivity: function() {
            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_diagnostic_get_recent_fixes',
                    nonce: redcoDiagnosticAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        RedcoDiagnosticAdmin.displayRecentActivity(response.data);
                    }
                }
            });
        },
        
        /**
         * Display recent activity
         */
        displayRecentActivity: function(data) {
            let html = '';
            
            if (data.recent_fixes && data.recent_fixes.length > 0) {
                data.recent_fixes.slice(0, 5).forEach(function(fix) {
                    html += '<div class="redco-activity-item">';
                    html += '<div class="redco-activity-icon ' + (fix.success ? 'success' : 'error') + '">';
                    html += '<span class="dashicons dashicons-' + (fix.success ? 'yes-alt' : 'dismiss') + '"></span>';
                    html += '</div>';
                    html += '<div class="redco-activity-content">';
                    html += '<div class="redco-activity-title">' + fix.fix_type + '</div>';
                    html += '<div class="redco-activity-time">' + fix.executed_at + '</div>';
                    html += '</div>';
                    html += '</div>';
                });
            } else {
                html = '<div class="redco-no-activity">No recent activity</div>';
            }
            
            $('#recent-activity-list').html(html);
        },
        
        /**
         * Initialize charts
         */
        initCharts: function() {
            // Initialize Chart.js if available
            if (typeof Chart !== 'undefined') {
                this.performanceChart = new Chart(document.getElementById('performance-chart'), {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: 'Performance Score',
                            data: [],
                            borderColor: '#2271b1',
                            backgroundColor: 'rgba(34, 113, 177, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
            }
        },
        
        /**
         * Update performance chart
         */
        updatePerformanceChart: function(period) {
            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_diagnostic_get_performance_history',
                    nonce: redcoDiagnosticAjax.nonce,
                    period: period
                },
                success: function(response) {
                    if (response.success && RedcoDiagnosticAdmin.performanceChart) {
                        const data = response.data;
                        RedcoDiagnosticAdmin.performanceChart.data.labels = data.labels;
                        RedcoDiagnosticAdmin.performanceChart.data.datasets[0].data = data.performance_scores;
                        RedcoDiagnosticAdmin.performanceChart.update();
                    }
                }
            });
        },
        
        /**
         * Update chart based on period selection
         */
        updateChart: function() {
            const period = $(this).val();
            RedcoDiagnosticAdmin.updatePerformanceChart(period);
        },
        
        /**
         * Start real-time monitoring
         */
        startRealtimeMonitoring: function() {
            // Update metrics every 30 seconds
            setInterval(function() {
                RedcoDiagnosticAdmin.updateRealtimeMetrics();
            }, 30000);
        },
        
        /**
         * Update real-time metrics
         */
        updateRealtimeMetrics: function() {
            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_diagnostic_get_realtime_metrics',
                    nonce: redcoDiagnosticAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Update dashboard metrics if visible
                        if ($('#tab-dashboard').hasClass('active')) {
                            RedcoDiagnosticAdmin.updateDashboardMetrics(response.data);
                        }
                    }
                }
            });
        },
        
        /**
         * Update dashboard metrics
         */
        updateDashboardMetrics: function(metrics) {
            // Update Core Web Vitals if available
            if (metrics.core_web_vitals) {
                const vitals = metrics.core_web_vitals;
                $('.redco-vital-item:nth-child(1) .redco-vital-value').text(vitals.lcp.toFixed(2) + 's');
                $('.redco-vital-item:nth-child(2) .redco-vital-value').text(Math.round(vitals.fid) + 'ms');
                $('.redco-vital-item:nth-child(3) .redco-vital-value').text(vitals.cls.toFixed(3));
            }
        },
        
        /**
         * Show success message
         */
        showSuccess: function(message) {
            this.showNotice(message, 'success');
        },
        
        /**
         * Show error message
         */
        showError: function(message) {
            this.showNotice(message, 'error');
        },
        
        /**
         * Show notice
         */
        showNotice: function(message, type) {
            const $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.redco-diagnostic-admin').prepend($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    };
    
    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        RedcoDiagnosticAdmin.init();
    });
    
})(jQuery);
