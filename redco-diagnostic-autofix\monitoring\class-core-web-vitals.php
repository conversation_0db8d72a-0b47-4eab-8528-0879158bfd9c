<?php
/**
 * Core Web Vitals Integration for Redco Diagnostic & Auto-Fix
 * 
 * Phase 2 Enhancement: Core Web Vitals monitoring and optimization
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Diagnostic_Core_Web_Vitals {
    
    private $table_name;
    private $vitals_thresholds;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'redco_diagnostic_core_web_vitals';
        
        // Google's Core Web Vitals thresholds
        $this->vitals_thresholds = array(
            'lcp' => array('good' => 2.5, 'needs_improvement' => 4.0), // Largest Contentful Paint (seconds)
            'fid' => array('good' => 100, 'needs_improvement' => 300), // First Input Delay (milliseconds)
            'cls' => array('good' => 0.1, 'needs_improvement' => 0.25), // Cumulative Layout Shift
            'fcp' => array('good' => 1.8, 'needs_improvement' => 3.0), // First Contentful Paint (seconds)
            'ttfb' => array('good' => 0.8, 'needs_improvement' => 1.8) // Time to First Byte (seconds)
        );
    }
    
    /**
     * Initialize Core Web Vitals monitoring
     */
    public function init() {
        // AJAX handlers
        add_action('wp_ajax_redco_diagnostic_get_core_web_vitals', array($this, 'ajax_get_core_web_vitals'));
        add_action('wp_ajax_redco_diagnostic_record_web_vitals', array($this, 'ajax_record_web_vitals'));
        add_action('wp_ajax_nopriv_redco_diagnostic_record_web_vitals', array($this, 'ajax_record_web_vitals'));
        add_action('wp_ajax_redco_diagnostic_get_vitals_history', array($this, 'ajax_get_vitals_history'));
        add_action('wp_ajax_redco_diagnostic_get_vitals_recommendations', array($this, 'ajax_get_vitals_recommendations'));
        
        // Frontend integration
        add_action('wp_footer', array($this, 'inject_web_vitals_script'));
        
        // Scheduled collection
        add_action('redco_diagnostic_collect_web_vitals', array($this, 'collect_scheduled_vitals'));
        
        // Schedule collection if not already scheduled
        if (!wp_next_scheduled('redco_diagnostic_collect_web_vitals')) {
            wp_schedule_event(time(), 'hourly', 'redco_diagnostic_collect_web_vitals');
        }
        
        // Create database table
        $this->maybe_create_table();
    }
    
    /**
     * Get current Core Web Vitals
     */
    public function get_current_vitals() {
        global $wpdb;
        
        // Get latest vitals from database
        $latest_vitals = $wpdb->get_row(
            "SELECT * FROM {$this->table_name} 
             ORDER BY timestamp DESC 
             LIMIT 1",
            ARRAY_A
        );
        
        if ($latest_vitals) {
            $vitals_data = json_decode($latest_vitals['vitals_data'], true);
            
            return array(
                'lcp' => $vitals_data['lcp'] ?? 0,
                'fid' => $vitals_data['fid'] ?? 0,
                'cls' => $vitals_data['cls'] ?? 0,
                'fcp' => $vitals_data['fcp'] ?? 0,
                'ttfb' => $vitals_data['ttfb'] ?? 0,
                'timestamp' => $latest_vitals['timestamp'],
                'scores' => $this->calculate_vitals_scores($vitals_data)
            );
        }
        
        return array(
            'lcp' => 0,
            'fid' => 0,
            'cls' => 0,
            'fcp' => 0,
            'ttfb' => 0,
            'timestamp' => null,
            'scores' => array()
        );
    }
    
    /**
     * AJAX: Get Core Web Vitals
     */
    public function ajax_get_core_web_vitals() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $vitals = $this->get_current_vitals();
        $statistics = $this->get_vitals_statistics();
        $recommendations = $this->get_vitals_recommendations();
        
        wp_send_json_success(array(
            'current_vitals' => $vitals,
            'statistics' => $statistics,
            'recommendations' => $recommendations,
            'thresholds' => $this->vitals_thresholds
        ));
    }
    
    /**
     * Inject Web Vitals measurement script
     */
    public function inject_web_vitals_script() {
        if (is_admin() || wp_doing_ajax()) {
            return;
        }
        
        ?>
        <script>
        (function() {
            // Web Vitals measurement using the web-vitals library approach
            var vitalsData = {};
            
            // Largest Contentful Paint (LCP)
            function measureLCP() {
                if ('PerformanceObserver' in window) {
                    var observer = new PerformanceObserver(function(list) {
                        var entries = list.getEntries();
                        var lastEntry = entries[entries.length - 1];
                        vitalsData.lcp = lastEntry.startTime / 1000; // Convert to seconds
                    });
                    observer.observe({entryTypes: ['largest-contentful-paint']});
                }
            }
            
            // First Input Delay (FID)
            function measureFID() {
                if ('PerformanceObserver' in window) {
                    var observer = new PerformanceObserver(function(list) {
                        var entries = list.getEntries();
                        entries.forEach(function(entry) {
                            vitalsData.fid = entry.processingStart - entry.startTime;
                        });
                    });
                    observer.observe({entryTypes: ['first-input']});
                }
            }
            
            // Cumulative Layout Shift (CLS)
            function measureCLS() {
                if ('PerformanceObserver' in window) {
                    var clsValue = 0;
                    var observer = new PerformanceObserver(function(list) {
                        var entries = list.getEntries();
                        entries.forEach(function(entry) {
                            if (!entry.hadRecentInput) {
                                clsValue += entry.value;
                            }
                        });
                        vitalsData.cls = clsValue;
                    });
                    observer.observe({entryTypes: ['layout-shift']});
                }
            }
            
            // First Contentful Paint (FCP)
            function measureFCP() {
                if (window.performance && window.performance.getEntriesByType) {
                    var paintEntries = window.performance.getEntriesByType('paint');
                    paintEntries.forEach(function(entry) {
                        if (entry.name === 'first-contentful-paint') {
                            vitalsData.fcp = entry.startTime / 1000; // Convert to seconds
                        }
                    });
                }
            }
            
            // Time to First Byte (TTFB)
            function measureTTFB() {
                if (window.performance && window.performance.timing) {
                    var timing = window.performance.timing;
                    vitalsData.ttfb = (timing.responseStart - timing.navigationStart) / 1000;
                }
            }
            
            // Initialize measurements
            measureLCP();
            measureFID();
            measureCLS();
            measureFCP();
            measureTTFB();
            
            // Send data after page load
            window.addEventListener('load', function() {
                setTimeout(function() {
                    // Ensure we have some data before sending
                    if (Object.keys(vitalsData).length > 0) {
                        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'action=redco_diagnostic_record_web_vitals&nonce=<?php echo redco_diagnostic_create_nonce(); ?>&vitals=' + encodeURIComponent(JSON.stringify(vitalsData)) + '&url=' + encodeURIComponent(window.location.href)
                        });
                    }
                }, 2000); // Wait 2 seconds to ensure measurements are complete
            });
            
            // Also send data when user is about to leave (for CLS final value)
            window.addEventListener('beforeunload', function() {
                if (navigator.sendBeacon && Object.keys(vitalsData).length > 0) {
                    var formData = new FormData();
                    formData.append('action', 'redco_diagnostic_record_web_vitals');
                    formData.append('nonce', '<?php echo redco_diagnostic_create_nonce(); ?>');
                    formData.append('vitals', JSON.stringify(vitalsData));
                    formData.append('url', window.location.href);
                    navigator.sendBeacon('<?php echo admin_url('admin-ajax.php'); ?>', formData);
                }
            });
        })();
        </script>
        <?php
    }
    
    /**
     * AJAX: Record Web Vitals data
     */
    public function ajax_record_web_vitals() {
        $vitals_json = $_POST['vitals'] ?? '';
        $page_url = sanitize_url($_POST['url'] ?? '');
        
        $vitals = json_decode(stripslashes($vitals_json), true);
        
        if ($vitals && is_array($vitals)) {
            $this->store_vitals_data($vitals, $page_url);
        }
        
        wp_die();
    }
    
    /**
     * Store Web Vitals data
     */
    private function store_vitals_data($vitals, $page_url) {
        global $wpdb;
        
        // Calculate scores
        $scores = $this->calculate_vitals_scores($vitals);
        
        $wpdb->insert(
            $this->table_name,
            array(
                'timestamp' => current_time('mysql'),
                'page_url' => $page_url,
                'lcp' => $vitals['lcp'] ?? 0,
                'fid' => $vitals['fid'] ?? 0,
                'cls' => $vitals['cls'] ?? 0,
                'fcp' => $vitals['fcp'] ?? 0,
                'ttfb' => $vitals['ttfb'] ?? 0,
                'overall_score' => $scores['overall'],
                'vitals_data' => json_encode($vitals),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ),
            array('%s', '%s', '%f', '%f', '%f', '%f', '%f', '%d', '%s', '%s')
        );
        
        // Cleanup old data (keep last 30 days)
        $wpdb->query(
            "DELETE FROM {$this->table_name} 
             WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY)"
        );
    }
    
    /**
     * Calculate Web Vitals scores
     */
    private function calculate_vitals_scores($vitals) {
        $scores = array();
        
        foreach ($this->vitals_thresholds as $metric => $thresholds) {
            $value = $vitals[$metric] ?? 0;
            
            if ($value <= $thresholds['good']) {
                $scores[$metric] = 'good';
                $scores[$metric . '_score'] = 100;
            } elseif ($value <= $thresholds['needs_improvement']) {
                $scores[$metric] = 'needs_improvement';
                $scores[$metric . '_score'] = 75;
            } else {
                $scores[$metric] = 'poor';
                $scores[$metric . '_score'] = 25;
            }
        }
        
        // Calculate overall score
        $total_score = 0;
        $metric_count = 0;
        
        foreach (['lcp', 'fid', 'cls'] as $core_metric) {
            if (isset($scores[$core_metric . '_score'])) {
                $total_score += $scores[$core_metric . '_score'];
                $metric_count++;
            }
        }
        
        $scores['overall'] = $metric_count > 0 ? round($total_score / $metric_count) : 0;
        
        return $scores;
    }
    
    /**
     * Get Web Vitals statistics
     */
    public function get_vitals_statistics() {
        global $wpdb;
        
        $stats = $wpdb->get_row(
            "SELECT 
                COUNT(*) as total_measurements,
                AVG(lcp) as avg_lcp,
                AVG(fid) as avg_fid,
                AVG(cls) as avg_cls,
                AVG(fcp) as avg_fcp,
                AVG(ttfb) as avg_ttfb,
                AVG(overall_score) as avg_overall_score
             FROM {$this->table_name}
             WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)",
            ARRAY_A
        );
        
        if ($stats) {
            // Calculate percentage of good scores
            $good_scores = $wpdb->get_row(
                "SELECT 
                    SUM(CASE WHEN lcp <= {$this->vitals_thresholds['lcp']['good']} THEN 1 ELSE 0 END) as lcp_good,
                    SUM(CASE WHEN fid <= {$this->vitals_thresholds['fid']['good']} THEN 1 ELSE 0 END) as fid_good,
                    SUM(CASE WHEN cls <= {$this->vitals_thresholds['cls']['good']} THEN 1 ELSE 0 END) as cls_good
                 FROM {$this->table_name}
                 WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)",
                ARRAY_A
            );
            
            $total = max(1, $stats['total_measurements']); // Avoid division by zero
            
            $stats['lcp_good_percentage'] = round(($good_scores['lcp_good'] / $total) * 100);
            $stats['fid_good_percentage'] = round(($good_scores['fid_good'] / $total) * 100);
            $stats['cls_good_percentage'] = round(($good_scores['cls_good'] / $total) * 100);
        }
        
        return $stats ?: array(
            'total_measurements' => 0,
            'avg_lcp' => 0,
            'avg_fid' => 0,
            'avg_cls' => 0,
            'avg_fcp' => 0,
            'avg_ttfb' => 0,
            'avg_overall_score' => 0,
            'lcp_good_percentage' => 0,
            'fid_good_percentage' => 0,
            'cls_good_percentage' => 0
        );
    }
    
    /**
     * AJAX: Get Web Vitals history
     */
    public function ajax_get_vitals_history() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $period = sanitize_text_field($_POST['period'] ?? '7d');
        $history = $this->get_vitals_history($period);
        
        wp_send_json_success($history);
    }
    
    /**
     * Get Web Vitals history
     */
    public function get_vitals_history($period = '7d') {
        global $wpdb;
        
        $interval_map = array(
            '24h' => 'INTERVAL 24 HOUR',
            '7d' => 'INTERVAL 7 DAY',
            '30d' => 'INTERVAL 30 DAY'
        );
        
        $interval = $interval_map[$period] ?? 'INTERVAL 7 DAY';
        
        $history = $wpdb->get_results(
            "SELECT 
                DATE(timestamp) as date,
                AVG(lcp) as avg_lcp,
                AVG(fid) as avg_fid,
                AVG(cls) as avg_cls,
                AVG(overall_score) as avg_score,
                COUNT(*) as measurements
             FROM {$this->table_name}
             WHERE timestamp >= DATE_SUB(NOW(), {$interval})
             GROUP BY DATE(timestamp)
             ORDER BY date ASC",
            ARRAY_A
        );
        
        return $history;
    }
    
    /**
     * Get Web Vitals recommendations
     */
    public function get_vitals_recommendations() {
        $current_vitals = $this->get_current_vitals();
        $recommendations = array();
        
        // LCP recommendations
        if ($current_vitals['lcp'] > $this->vitals_thresholds['lcp']['good']) {
            $recommendations[] = array(
                'metric' => 'lcp',
                'title' => 'Improve Largest Contentful Paint',
                'description' => 'Your LCP is ' . round($current_vitals['lcp'], 2) . 's. Consider optimizing images, improving server response times, and removing render-blocking resources.',
                'priority' => $current_vitals['lcp'] > $this->vitals_thresholds['lcp']['needs_improvement'] ? 'high' : 'medium',
                'actions' => array(
                    'Optimize images and use modern formats (WebP)',
                    'Implement lazy loading for images',
                    'Improve server response times',
                    'Remove render-blocking CSS and JavaScript',
                    'Use a Content Delivery Network (CDN)'
                )
            );
        }
        
        // FID recommendations
        if ($current_vitals['fid'] > $this->vitals_thresholds['fid']['good']) {
            $recommendations[] = array(
                'metric' => 'fid',
                'title' => 'Improve First Input Delay',
                'description' => 'Your FID is ' . round($current_vitals['fid']) . 'ms. Consider reducing JavaScript execution time and breaking up long tasks.',
                'priority' => $current_vitals['fid'] > $this->vitals_thresholds['fid']['needs_improvement'] ? 'high' : 'medium',
                'actions' => array(
                    'Minimize and defer JavaScript',
                    'Remove unused JavaScript',
                    'Break up long-running tasks',
                    'Use web workers for heavy computations',
                    'Optimize third-party scripts'
                )
            );
        }
        
        // CLS recommendations
        if ($current_vitals['cls'] > $this->vitals_thresholds['cls']['good']) {
            $recommendations[] = array(
                'metric' => 'cls',
                'title' => 'Improve Cumulative Layout Shift',
                'description' => 'Your CLS is ' . round($current_vitals['cls'], 3) . '. Consider setting dimensions for images and ads, and avoiding inserting content above existing content.',
                'priority' => $current_vitals['cls'] > $this->vitals_thresholds['cls']['needs_improvement'] ? 'high' : 'medium',
                'actions' => array(
                    'Set explicit dimensions for images and videos',
                    'Reserve space for ads and embeds',
                    'Avoid inserting content above existing content',
                    'Use CSS aspect-ratio for responsive images',
                    'Preload custom fonts'
                )
            );
        }
        
        return $recommendations;
    }
    
    /**
     * AJAX: Get Web Vitals recommendations
     */
    public function ajax_get_vitals_recommendations() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $recommendations = $this->get_vitals_recommendations();
        
        wp_send_json_success($recommendations);
    }
    
    /**
     * Maybe create table
     */
    private function maybe_create_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") != $this->table_name) {
            $sql = "CREATE TABLE {$this->table_name} (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                timestamp datetime NOT NULL,
                page_url varchar(500),
                lcp float NOT NULL DEFAULT 0,
                fid float NOT NULL DEFAULT 0,
                cls float NOT NULL DEFAULT 0,
                fcp float NOT NULL DEFAULT 0,
                ttfb float NOT NULL DEFAULT 0,
                overall_score int(3) NOT NULL DEFAULT 0,
                vitals_data longtext,
                user_agent text,
                PRIMARY KEY (id),
                KEY timestamp (timestamp),
                KEY page_url (page_url(255)),
                KEY overall_score (overall_score)
            ) {$charset_collate};";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }
}
