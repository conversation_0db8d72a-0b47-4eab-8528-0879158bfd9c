<?php
/**
 * CRITICAL FIX: Centralized Debug Utility System
 * Replaces scattered error_log() statements with conditional debug logging
 * Only activates in development environments
 *
 * @package Redco_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Centralized Debug Utility Class
 */
class Redco_Debug_Utils {
    
    /**
     * Check if we're in development environment
     *
     * @return bool True if development environment
     */
    public static function is_development_environment() {
        // Check for development indicators
        $dev_indicators = array(
            defined('WP_DEBUG') && WP_DEBUG,
            defined('WP_DEBUG_LOG') && WP_DEBUG_LOG,
            in_array($_SERVER['HTTP_HOST'] ?? '', array('localhost', '127.0.0.1')),
            strpos($_SERVER['HTTP_HOST'] ?? '', '.local') !== false,
            strpos($_SERVER['HTTP_HOST'] ?? '', '.dev') !== false,
            defined('REDCO_DEBUG_MODE') && REDCO_DEBUG_MODE
        );
        
        return in_array(true, $dev_indicators, true);
    }
    
    /**
     * Conditional debug logging (replaces error_log)
     *
     * @param string $message Debug message
     * @param string $context Optional context (e.g., 'AJAX', 'Settings', 'Performance')
     */
    public static function log($message, $context = '') {
        if (!self::is_development_environment()) {
            return;
        }
        
        $prefix = '[Redco Debug]';
        if (!empty($context)) {
            $prefix .= " [{$context}]";
        }
        
        error_log($prefix . ' ' . $message);
    }
    
    /**
     * Log warnings (development only)
     *
     * @param string $message Warning message
     * @param string $context Optional context
     */
    public static function warn($message, $context = '') {
        if (!self::is_development_environment()) {
            return;
        }
        
        $prefix = '[Redco Warning]';
        if (!empty($context)) {
            $prefix .= " [{$context}]";
        }
        
        error_log($prefix . ' ' . $message);
    }
    
    /**
     * Log errors (always logged, but with context in development)
     *
     * @param string $message Error message
     * @param string $context Optional context
     * @param bool $force_log Force logging even in production
     */
    public static function error($message, $context = '', $force_log = false) {
        if (!$force_log && !self::is_development_environment()) {
            return;
        }
        
        $prefix = '[Redco Error]';
        if (!empty($context)) {
            $prefix .= " [{$context}]";
        }
        
        error_log($prefix . ' ' . $message);
    }
    
    /**
     * Log performance metrics (development only)
     *
     * @param string $operation Operation name
     * @param float $execution_time Execution time in seconds
     * @param array $additional_data Additional performance data
     */
    public static function performance($operation, $execution_time, $additional_data = array()) {
        if (!self::is_development_environment()) {
            return;
        }
        
        $message = "Performance: {$operation} took {$execution_time}s";
        if (!empty($additional_data)) {
            $message .= ' | Data: ' . wp_json_encode($additional_data);
        }
        
        self::log($message, 'Performance');
    }
    
    /**
     * Log AJAX requests/responses (development only)
     *
     * @param string $action AJAX action name
     * @param array $request_data Request data
     * @param array $response_data Response data
     */
    public static function ajax($action, $request_data = array(), $response_data = array()) {
        if (!self::is_development_environment()) {
            return;
        }
        
        $message = "AJAX: {$action}";
        if (!empty($request_data)) {
            $message .= ' | Request: ' . wp_json_encode($request_data);
        }
        if (!empty($response_data)) {
            $message .= ' | Response: ' . wp_json_encode($response_data);
        }
        
        self::log($message, 'AJAX');
    }
    
    /**
     * Log database queries (development only)
     *
     * @param string $query SQL query
     * @param float $execution_time Query execution time
     */
    public static function query($query, $execution_time = null) {
        if (!self::is_development_environment()) {
            return;
        }
        
        $message = "Query: " . substr($query, 0, 200);
        if ($execution_time !== null) {
            $message .= " | Time: {$execution_time}s";
        }
        
        self::log($message, 'Database');
    }
    
    /**
     * Dump variable for debugging (development only)
     *
     * @param mixed $var Variable to dump
     * @param string $label Optional label
     */
    public static function dump($var, $label = '') {
        if (!self::is_development_environment()) {
            return;
        }
        
        $message = empty($label) ? 'Variable dump:' : "Variable dump ({$label}):";
        $message .= ' ' . print_r($var, true);
        
        self::log($message, 'Debug');
    }
}
