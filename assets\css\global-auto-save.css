/**
 * Global Auto-Save Toast Notifications CSS
 * Redco Optimizer Plugin
 */

/* Toast Container */
.redco-toast-container {
    position: fixed;
    top: 32px; /* Below WordPress admin bar */
    right: 20px;
    z-index: 999999;
    pointer-events: none;
}

/* Toast Base Styles */
.redco-toast {
    background: #fff;
    border-left: 4px solid #0073aa;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    max-width: 350px;
    min-width: 250px;
    opacity: 0;
    pointer-events: auto;
    transform: translateX(100%);
    transition: all 0.3s ease-in-out;
}

/* Toast Show State */
.redco-toast.show {
    opacity: 1;
    transform: translateX(0);
}

/* Toast Content */
.redco-toast .toast-content {
    align-items: center;
    display: flex;
    padding: 12px 16px;
}

/* Toast Icon */
.redco-toast .toast-icon {
    flex-shrink: 0;
    font-size: 16px;
    margin-right: 10px;
    width: 16px;
}

/* Toast Message */
.redco-toast .toast-message {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
}

/* Toast Close Button */
.redco-toast .toast-close {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    margin-left: 10px;
    padding: 0;
    width: 20px;
}

.redco-toast .toast-close:hover {
    color: #333;
}

/* Toast Type Variations */
.redco-toast-info {
    border-left-color: #0073aa;
}

.redco-toast-info .toast-icon::before {
    content: "ℹ";
    color: #0073aa;
}

.redco-toast-success {
    border-left-color: #46b450;
}

.redco-toast-success .toast-icon::before {
    content: "✓";
    color: #46b450;
}

.redco-toast-warning {
    border-left-color: #ffb900;
}

.redco-toast-warning .toast-icon::before {
    content: "⚠";
    color: #ffb900;
}

.redco-toast-error {
    border-left-color: #dc3232;
}

.redco-toast-error .toast-icon::before {
    content: "✕";
    color: #dc3232;
}

/* Toast Update Animation */
.redco-toast.updating {
    transform: scale(1.02);
}

/* Responsive Design */
@media (max-width: 782px) {
    .redco-toast-container {
        left: 20px;
        right: 20px;
        top: 46px; /* Below mobile admin bar */
    }
    
    .redco-toast {
        max-width: none;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .redco-toast {
        border-width: 2px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .redco-toast {
        transition: opacity 0.2s ease-in-out;
        transform: none;
    }
    
    .redco-toast.show {
        transform: none;
    }
    
    .redco-toast.updating {
        transform: none;
    }
}

/* Focus Styles for Accessibility */
.redco-toast .toast-close:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Dark Mode Support (if WordPress admin uses dark theme) */
@media (prefers-color-scheme: dark) {
    .redco-toast {
        background: #1e1e1e;
        color: #fff;
    }
    
    .redco-toast .toast-close {
        color: #ccc;
    }
    
    .redco-toast .toast-close:hover {
        color: #fff;
    }
}
