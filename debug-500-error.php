<?php
/**
 * CRITICAL DEBUG: 500 Internal Server Error Detector
 * 
 * This script helps identify the exact cause of the 500 error
 * Run this directly in browser to see detailed error information
 */

// Enable comprehensive error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Set up basic WordPress constants if not defined
if (!defined('ABSPATH')) {
    // Try to find WordPress root
    $wp_root = dirname(__FILE__);
    while (!file_exists($wp_root . '/wp-config.php') && $wp_root !== '/') {
        $wp_root = dirname($wp_root);
    }
    
    if (file_exists($wp_root . '/wp-config.php')) {
        define('ABSPATH', $wp_root . '/');
    } else {
        define('ABSPATH', dirname(__FILE__) . '/');
    }
}

echo "🚨 REDCO OPTIMIZER 500 ERROR DEBUG TOOL\n";
echo "=====================================\n\n";

// Test 1: Check if we can access WordPress
echo "Test 1: WordPress Environment Check\n";
echo "-----------------------------------\n";

if (file_exists(ABSPATH . 'wp-config.php')) {
    echo "✅ WordPress found at: " . ABSPATH . "\n";
    
    // Try to load WordPress minimally
    try {
        define('WP_USE_THEMES', false);
        define('WP_DEBUG', true);
        define('WP_DEBUG_DISPLAY', true);
        
        // Load WordPress bootstrap
        require_once ABSPATH . 'wp-config.php';
        require_once ABSPATH . 'wp-includes/wp-db.php';
        require_once ABSPATH . 'wp-includes/functions.php';
        require_once ABSPATH . 'wp-includes/plugin.php';
        
        echo "✅ WordPress core loaded successfully\n";
        
    } catch (Exception $e) {
        echo "❌ WordPress loading failed: " . $e->getMessage() . "\n";
        echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
        exit(1);
    } catch (Error $e) {
        echo "❌ WordPress fatal error: " . $e->getMessage() . "\n";
        echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
        exit(1);
    }
} else {
    echo "❌ WordPress not found. Please run this script from the plugin directory.\n";
    exit(1);
}

// Test 2: Check plugin file structure
echo "\nTest 2: Plugin File Structure Check\n";
echo "-----------------------------------\n";

$plugin_dir = dirname(__FILE__);
$required_files = array(
    'redco-optimizer.php' => 'Main plugin file',
    'includes/class-loader.php' => 'Main loader class',
    'includes/class-api-endpoints.php' => 'API endpoints class',
    'modules/diagnostic-autofix/class-diagnostic-helpers.php' => 'Diagnostic helpers trait',
    'modules/diagnostic-autofix/class-diagnostic-autofix.php' => 'Diagnostic autofix class'
);

foreach ($required_files as $file => $description) {
    $full_path = $plugin_dir . '/' . $file;
    if (file_exists($full_path)) {
        echo "✅ {$description}: {$file}\n";
    } else {
        echo "❌ Missing {$description}: {$file}\n";
    }
}

// Test 3: Check plugin constants and loading
echo "\nTest 3: Plugin Constants and Loading\n";
echo "-----------------------------------\n";

try {
    // Simulate plugin loading
    $plugin_file = $plugin_dir . '/redco-optimizer.php';
    
    if (file_exists($plugin_file)) {
        echo "Loading plugin file: {$plugin_file}\n";
        
        // Capture any output/errors
        ob_start();
        $error_occurred = false;
        
        try {
            include_once $plugin_file;
            echo "✅ Plugin file loaded successfully\n";
            
            // Check if constants are defined
            $constants = array(
                'REDCO_OPTIMIZER_VERSION',
                'REDCO_OPTIMIZER_PLUGIN_FILE',
                'REDCO_OPTIMIZER_PLUGIN_DIR',
                'REDCO_OPTIMIZER_PLUGIN_URL',
                'REDCO_OPTIMIZER_PLUGIN_BASENAME',
                'REDCO_OPTIMIZER_PATH'
            );
            
            foreach ($constants as $constant) {
                if (defined($constant)) {
                    echo "✅ Constant {$constant}: " . constant($constant) . "\n";
                } else {
                    echo "❌ Missing constant: {$constant}\n";
                    $error_occurred = true;
                }
            }
            
        } catch (ParseError $e) {
            echo "❌ Parse Error in plugin file: " . $e->getMessage() . "\n";
            echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
            $error_occurred = true;
        } catch (Error $e) {
            echo "❌ Fatal Error in plugin file: " . $e->getMessage() . "\n";
            echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
            $error_occurred = true;
        } catch (Exception $e) {
            echo "❌ Exception in plugin file: " . $e->getMessage() . "\n";
            echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
            $error_occurred = true;
        }
        
        $output = ob_get_clean();
        if (!empty($output)) {
            echo "Plugin output/warnings:\n" . $output . "\n";
        }
        
    } else {
        echo "❌ Plugin file not found: {$plugin_file}\n";
        $error_occurred = true;
    }
    
} catch (Exception $e) {
    echo "❌ Error during plugin loading test: " . $e->getMessage() . "\n";
    $error_occurred = true;
}

// Test 4: Check specific problematic files
echo "\nTest 4: Individual File Syntax Check\n";
echo "-----------------------------------\n";

$files_to_check = array(
    'includes/class-api-endpoints.php',
    'modules/diagnostic-autofix/class-diagnostic-helpers.php',
    'modules/diagnostic-autofix/class-diagnostic-autofix.php',
    'includes/class-pagespeed-diagnostics.php',
    'includes/class-admin-ui.php'
);

foreach ($files_to_check as $file) {
    $full_path = $plugin_dir . '/' . $file;
    if (file_exists($full_path)) {
        echo "Checking syntax: {$file}...\n";
        
        // Use php -l to check syntax
        $output = array();
        $return_code = 0;
        
        exec("php -l \"{$full_path}\" 2>&1", $output, $return_code);
        
        if ($return_code === 0) {
            echo "✅ Syntax OK: {$file}\n";
        } else {
            echo "❌ Syntax Error in {$file}:\n";
            echo implode("\n", $output) . "\n";
            $error_occurred = true;
        }
    } else {
        echo "⚠️ File not found: {$file}\n";
    }
}

// Test 5: Check WordPress plugin activation
echo "\nTest 5: WordPress Plugin Status Check\n";
echo "-------------------------------------\n";

if (function_exists('get_option')) {
    $active_plugins = get_option('active_plugins', array());
    $plugin_basename = 'redco-optimizer/redco-optimizer.php';
    
    if (in_array($plugin_basename, $active_plugins)) {
        echo "✅ Redco Optimizer is active in WordPress\n";
    } else {
        echo "⚠️ Redco Optimizer is not active in WordPress\n";
        echo "Active plugins:\n";
        foreach ($active_plugins as $plugin) {
            echo "  - {$plugin}\n";
        }
    }
} else {
    echo "❌ WordPress functions not available\n";
}

// Test 6: Memory and resource check
echo "\nTest 6: System Resource Check\n";
echo "-----------------------------\n";

echo "PHP Version: " . PHP_VERSION . "\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "\n";
echo "Error Reporting: " . error_reporting() . "\n";
echo "Display Errors: " . ini_get('display_errors') . "\n";

// Check memory usage
$memory_usage = memory_get_usage(true);
$memory_peak = memory_get_peak_usage(true);
echo "Current Memory Usage: " . round($memory_usage / 1024 / 1024, 2) . " MB\n";
echo "Peak Memory Usage: " . round($memory_peak / 1024 / 1024, 2) . " MB\n";

// Final summary
echo "\n🎯 SUMMARY\n";
echo "=========\n";

if (isset($error_occurred) && $error_occurred) {
    echo "❌ ERRORS DETECTED: The plugin has issues that need to be fixed.\n";
    echo "\nRecommended actions:\n";
    echo "1. Fix any syntax errors shown above\n";
    echo "2. Check file permissions\n";
    echo "3. Increase PHP memory limit if needed\n";
    echo "4. Check WordPress error logs\n";
    echo "5. Try deactivating and reactivating the plugin\n";
} else {
    echo "✅ NO OBVIOUS ERRORS DETECTED\n";
    echo "\nThe 500 error might be caused by:\n";
    echo "1. Server configuration issues\n";
    echo "2. Plugin conflicts\n";
    echo "3. WordPress core issues\n";
    echo "4. .htaccess problems\n";
    echo "5. Database connection issues\n";
    echo "\nNext steps:\n";
    echo "1. Check server error logs\n";
    echo "2. Try deactivating other plugins\n";
    echo "3. Switch to a default theme temporarily\n";
    echo "4. Check .htaccess file\n";
}

echo "\n📋 DEBUG COMPLETE\n";
echo "================\n";
echo "If errors persist, please:\n";
echo "1. Check your server's error log\n";
echo "2. Enable WordPress debug logging\n";
echo "3. Contact your hosting provider\n";
echo "4. Try accessing WordPress with all plugins deactivated\n";

?>
