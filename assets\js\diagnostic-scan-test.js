/**
 * CRITICAL FIX: Diagnostic Scan Test Suite
 * Tests the comprehensive scan functionality to ensure it works correctly
 * Run this in browser console on the diagnostic page to test the fixes
 */

(function($) {
    'use strict';

    /**
     * Diagnostic Scan Test Suite
     */
    window.RedcoDiagnosticTest = {
        
        /**
         * Configuration
         */
        config: {
            testTimeout: 180000, // 3 minutes
            maxRetries: 3
        },
        
        /**
         * Test state
         */
        state: {
            currentTest: null,
            testResults: [],
            startTime: null
        },
        
        /**
         * Run all diagnostic tests
         */
        runAllTests: function() {
            console.log('🧪 Starting Redco Diagnostic Scan Test Suite...');
            this.state.startTime = Date.now();
            this.state.testResults = [];
            
            // Test 1: Check if diagnostic module is loaded
            this.testModuleLoaded();
            
            // Test 2: Check AJAX configuration
            this.testAjaxConfiguration();
            
            // Test 3: Test scan button functionality
            this.testScanButton();
            
            // Test 4: Test emergency recovery
            this.testEmergencyRecovery();
            
            // Test 5: Test comprehensive scan (if safe)
            if (confirm('Run actual comprehensive scan? This will take 1-3 minutes.')) {
                this.testComprehensiveScan();
            } else {
                console.log('⏭️ Skipping comprehensive scan test (user choice)');
                this.showTestResults();
            }
        },
        
        /**
         * Test 1: Check if diagnostic module is loaded
         */
        testModuleLoaded: function() {
            console.log('🔍 Test 1: Checking if diagnostic module is loaded...');
            
            const result = {
                test: 'Module Loaded',
                passed: false,
                details: []
            };
            
            // Check if RedcoDiagnostic object exists
            if (typeof RedcoDiagnostic !== 'undefined') {
                result.details.push('✅ RedcoDiagnostic object exists');
                result.passed = true;
            } else {
                result.details.push('❌ RedcoDiagnostic object not found');
            }
            
            // Check if AJAX configuration exists
            if (typeof redcoDiagnosticAjax !== 'undefined') {
                result.details.push('✅ AJAX configuration exists');
            } else {
                result.details.push('❌ AJAX configuration missing');
                result.passed = false;
            }
            
            // Check if scan button exists
            if ($('#run-comprehensive-scan').length > 0 || $('.redco-run-scan').length > 0) {
                result.details.push('✅ Scan button found');
            } else {
                result.details.push('❌ Scan button not found');
                result.passed = false;
            }
            
            this.state.testResults.push(result);
            console.log(result.passed ? '✅ Test 1 PASSED' : '❌ Test 1 FAILED', result.details);
        },
        
        /**
         * Test 2: Check AJAX configuration
         */
        testAjaxConfiguration: function() {
            console.log('🔍 Test 2: Checking AJAX configuration...');
            
            const result = {
                test: 'AJAX Configuration',
                passed: false,
                details: []
            };
            
            if (typeof redcoDiagnosticAjax !== 'undefined') {
                // Check AJAX URL
                if (redcoDiagnosticAjax.ajaxurl) {
                    result.details.push('✅ AJAX URL configured: ' + redcoDiagnosticAjax.ajaxurl);
                } else {
                    result.details.push('❌ AJAX URL missing');
                }
                
                // Check nonce
                if (redcoDiagnosticAjax.nonce) {
                    result.details.push('✅ Nonce configured: ' + redcoDiagnosticAjax.nonce.substring(0, 10) + '...');
                } else {
                    result.details.push('❌ Nonce missing');
                }
                
                // Test nonce validity by making a test request
                this.testNonceValidity(result);
            } else {
                result.details.push('❌ redcoDiagnosticAjax not defined');
            }
            
            this.state.testResults.push(result);
        },
        
        /**
         * Test nonce validity
         */
        testNonceValidity: function(result) {
            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_test_nonce',
                    nonce: redcoDiagnosticAjax.nonce
                },
                timeout: 5000,
                success: function(response) {
                    result.details.push('✅ Nonce validation test passed');
                    result.passed = true;
                },
                error: function(xhr, status, error) {
                    if (xhr.status === 400 && xhr.responseText.includes('Invalid action')) {
                        result.details.push('✅ Nonce format valid (action not found is expected)');
                        result.passed = true;
                    } else {
                        result.details.push('❌ Nonce validation failed: ' + error);
                    }
                }
            });
        },
        
        /**
         * Test 3: Test scan button functionality
         */
        testScanButton: function() {
            console.log('🔍 Test 3: Testing scan button functionality...');
            
            const result = {
                test: 'Scan Button Functionality',
                passed: false,
                details: []
            };
            
            const $scanButton = $('#run-comprehensive-scan, .redco-run-scan').first();
            
            if ($scanButton.length === 0) {
                result.details.push('❌ No scan button found');
                this.state.testResults.push(result);
                return;
            }
            
            // Check if button is clickable
            if (!$scanButton.prop('disabled')) {
                result.details.push('✅ Scan button is enabled');
            } else {
                result.details.push('❌ Scan button is disabled');
            }
            
            // Check if click handler is bound
            const events = $._data($scanButton[0], 'events');
            if (events && events.click) {
                result.details.push('✅ Click handler is bound');
                result.passed = true;
            } else {
                result.details.push('❌ No click handler found');
            }
            
            this.state.testResults.push(result);
            console.log(result.passed ? '✅ Test 3 PASSED' : '❌ Test 3 FAILED', result.details);
        },
        
        /**
         * Test 4: Test emergency recovery
         */
        testEmergencyRecovery: function() {
            console.log('🔍 Test 4: Testing emergency recovery...');
            
            const result = {
                test: 'Emergency Recovery',
                passed: false,
                details: []
            };
            
            if (typeof RedcoDiagnostic !== 'undefined' && typeof RedcoDiagnostic.emergencyRecovery === 'function') {
                result.details.push('✅ Emergency recovery function exists');
                
                // Test emergency recovery function
                try {
                    RedcoDiagnostic.emergencyRecovery('test');
                    result.details.push('✅ Emergency recovery function executed without errors');
                    result.passed = true;
                } catch (e) {
                    result.details.push('❌ Emergency recovery function failed: ' + e.message);
                }
            } else {
                result.details.push('❌ Emergency recovery function not found');
            }
            
            this.state.testResults.push(result);
            console.log(result.passed ? '✅ Test 4 PASSED' : '❌ Test 4 FAILED', result.details);
        },
        
        /**
         * Test 5: Test comprehensive scan
         */
        testComprehensiveScan: function() {
            console.log('🔍 Test 5: Testing comprehensive scan...');
            
            const result = {
                test: 'Comprehensive Scan',
                passed: false,
                details: []
            };
            
            const startTime = Date.now();
            let scanCompleted = false;
            
            // Set up timeout
            const testTimeout = setTimeout(() => {
                if (!scanCompleted) {
                    result.details.push('❌ Scan timed out after 3 minutes');
                    this.state.testResults.push(result);
                    this.showTestResults();
                }
            }, this.config.testTimeout);
            
            // Monitor scan progress
            const originalShowSuccess = RedcoDiagnostic.showSuccess;
            const originalShowError = RedcoDiagnostic.showError;
            
            RedcoDiagnostic.showSuccess = function(message) {
                scanCompleted = true;
                clearTimeout(testTimeout);
                result.details.push('✅ Scan completed successfully: ' + message);
                result.details.push('⏱️ Scan duration: ' + ((Date.now() - startTime) / 1000).toFixed(2) + 's');
                result.passed = true;
                RedcoDiagnosticTest.state.testResults.push(result);
                RedcoDiagnosticTest.showTestResults();
                
                // Restore original function
                RedcoDiagnostic.showSuccess = originalShowSuccess;
                originalShowSuccess.call(this, message);
            };
            
            RedcoDiagnostic.showError = function(message) {
                scanCompleted = true;
                clearTimeout(testTimeout);
                result.details.push('❌ Scan failed: ' + message);
                result.details.push('⏱️ Scan duration: ' + ((Date.now() - startTime) / 1000).toFixed(2) + 's');
                RedcoDiagnosticTest.state.testResults.push(result);
                RedcoDiagnosticTest.showTestResults();
                
                // Restore original function
                RedcoDiagnostic.showError = originalShowError;
                originalShowError.call(this, message);
            };
            
            // Trigger the scan
            try {
                const $scanButton = $('#run-comprehensive-scan, .redco-run-scan').first();
                if ($scanButton.length > 0) {
                    result.details.push('🚀 Starting comprehensive scan...');
                    $scanButton.trigger('click');
                } else {
                    result.details.push('❌ Could not find scan button to trigger');
                    this.state.testResults.push(result);
                    this.showTestResults();
                }
            } catch (e) {
                result.details.push('❌ Error triggering scan: ' + e.message);
                this.state.testResults.push(result);
                this.showTestResults();
            }
        },
        
        /**
         * Show test results
         */
        showTestResults: function() {
            const duration = ((Date.now() - this.state.startTime) / 1000).toFixed(2);
            const passed = this.state.testResults.filter(r => r.passed).length;
            const total = this.state.testResults.length;
            
            console.log('\n📊 REDCO DIAGNOSTIC SCAN TEST RESULTS');
            console.log('=====================================');
            console.log(`⏱️ Total Duration: ${duration}s`);
            console.log(`✅ Passed: ${passed}/${total}`);
            console.log(`❌ Failed: ${total - passed}/${total}`);
            console.log('');
            
            this.state.testResults.forEach((result, index) => {
                console.log(`${index + 1}. ${result.test}: ${result.passed ? '✅ PASSED' : '❌ FAILED'}`);
                result.details.forEach(detail => {
                    console.log(`   ${detail}`);
                });
                console.log('');
            });
            
            if (passed === total) {
                console.log('🎉 ALL TESTS PASSED! The diagnostic scan should work correctly.');
            } else {
                console.log('⚠️ SOME TESTS FAILED. Please review the issues above.');
            }
        }
    };

    // Auto-run tests if in debug mode
    $(document).ready(function() {
        if (typeof redcoDebug !== 'undefined' && redcoDebug.enabled) {
            console.log('🧪 Diagnostic test suite loaded. Run RedcoDiagnosticTest.runAllTests() to test the scan functionality.');
        }
    });

})(jQuery);
