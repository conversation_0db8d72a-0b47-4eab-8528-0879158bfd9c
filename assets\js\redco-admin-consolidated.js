/**
 * RedCo Optimizer - Consolidated Admin JavaScript
 * Unified scripts for all admin functionality
 * Optimized for performance and consistency
 */

(function($) {
    'use strict';

    // Global RedCo Admin object
    window.RedcoAdmin = window.RedcoAdmin || {};

    /**
     * Core Admin Functionality
     */
    RedcoAdmin.Core = {
        init: function() {
            this.initAutoSave();
            this.initModuleToggle();
            this.initFormValidation();
            this.initTooltips();
            this.initLoadingStates();
            this.initAccessibility();
        },

        /**
         * Enhanced Auto-save functionality with debouncing
         */
        initAutoSave: function() {
            let autoSaveTimeout;
            const autoSaveDelay = 2000; // 2 seconds

            $(document).on('change input', '.redco-auto-save', function() {
                const $field = $(this);
                const $form = $field.closest('form');
                
                clearTimeout(autoSaveTimeout);
                
                // Show saving indicator
                RedcoAdmin.UI.showSavingIndicator($field);
                
                autoSaveTimeout = setTimeout(function() {
                    RedcoAdmin.Core.performAutoSave($field, $form);
                }, autoSaveDelay);
            });
        },

        /**
         * Perform auto-save operation
         */
        performAutoSave: function($field, $form) {
            const formData = new FormData($form[0]);
            formData.append('action', 'redco_auto_save_setting');
            formData.append('nonce', redcoAdmin.nonce);

            $.ajax({
                url: redcoAdmin.ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        RedcoAdmin.UI.showSuccessIndicator($field);
                        // Update any dependent UI elements
                        RedcoAdmin.Core.updateDependentElements(response.data);
                    } else {
                        RedcoAdmin.UI.showErrorIndicator($field, response.data.message);
                    }
                },
                error: function() {
                    RedcoAdmin.UI.showErrorIndicator($field, 'Network error occurred');
                }
            });
        },

        /**
         * Module toggle functionality
         */
        initModuleToggle: function() {
            $(document).on('change', '.module-toggle', function() {
                const $toggle = $(this);
                const moduleKey = $toggle.data('module');
                const isEnabled = $toggle.is(':checked');
                
                RedcoAdmin.Core.toggleModule(moduleKey, isEnabled, $toggle);
            });
        },

        /**
         * Toggle module state
         */
        toggleModule: function(moduleKey, isEnabled, $toggle) {
            const data = {
                action: 'redco_toggle_module',
                module: moduleKey,
                enabled: isEnabled ? 1 : 0,
                nonce: redcoAdmin.nonce
            };

            $.ajax({
                url: redcoAdmin.ajaxurl,
                type: 'POST',
                data: data,
                beforeSend: function() {
                    $toggle.prop('disabled', true);
                    RedcoAdmin.UI.showLoadingState($toggle.closest('.module-card'));
                },
                success: function(response) {
                    if (response.success) {
                        RedcoAdmin.UI.showNotification('Module ' + (isEnabled ? 'enabled' : 'disabled') + ' successfully', 'success');
                        // Update module status indicators
                        RedcoAdmin.Core.updateModuleStatus(moduleKey, isEnabled);
                    } else {
                        $toggle.prop('checked', !isEnabled);
                        RedcoAdmin.UI.showNotification(response.data.message || 'Failed to toggle module', 'error');
                    }
                },
                error: function() {
                    $toggle.prop('checked', !isEnabled);
                    RedcoAdmin.UI.showNotification('Network error occurred', 'error');
                },
                complete: function() {
                    $toggle.prop('disabled', false);
                    RedcoAdmin.UI.hideLoadingState($toggle.closest('.module-card'));
                }
            });
        },

        /**
         * Form validation
         */
        initFormValidation: function() {
            $(document).on('submit', '.redco-form', function(e) {
                const $form = $(this);
                const isValid = RedcoAdmin.Core.validateForm($form);
                
                if (!isValid) {
                    e.preventDefault();
                    return false;
                }
            });

            // Real-time validation
            $(document).on('blur', '.redco-form input, .redco-form select, .redco-form textarea', function() {
                RedcoAdmin.Core.validateField($(this));
            });
        },

        /**
         * Validate form
         */
        validateForm: function($form) {
            let isValid = true;
            
            $form.find('input[required], select[required], textarea[required]').each(function() {
                if (!RedcoAdmin.Core.validateField($(this))) {
                    isValid = false;
                }
            });
            
            return isValid;
        },

        /**
         * Validate individual field
         */
        validateField: function($field) {
            const value = $field.val();
            const type = $field.attr('type');
            const required = $field.prop('required');
            
            let isValid = true;
            let message = '';
            
            if (required && !value) {
                isValid = false;
                message = 'This field is required';
            } else if (type === 'email' && value && !RedcoAdmin.Utils.isValidEmail(value)) {
                isValid = false;
                message = 'Please enter a valid email address';
            } else if (type === 'url' && value && !RedcoAdmin.Utils.isValidUrl(value)) {
                isValid = false;
                message = 'Please enter a valid URL';
            } else if (type === 'number' && value) {
                const min = parseFloat($field.attr('min'));
                const max = parseFloat($field.attr('max'));
                const numValue = parseFloat(value);
                
                if (isNaN(numValue)) {
                    isValid = false;
                    message = 'Please enter a valid number';
                } else if (!isNaN(min) && numValue < min) {
                    isValid = false;
                    message = `Value must be at least ${min}`;
                } else if (!isNaN(max) && numValue > max) {
                    isValid = false;
                    message = `Value must be no more than ${max}`;
                }
            }
            
            RedcoAdmin.UI.updateFieldValidation($field, isValid, message);
            return isValid;
        },

        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            if ($.fn.tooltip) {
                $('.redco-tooltip').tooltip({
                    placement: 'top',
                    trigger: 'hover focus',
                    delay: { show: 500, hide: 100 }
                });
            }
        },

        /**
         * Initialize loading states
         */
        initLoadingStates: function() {
            $(document).on('click', '.redco-btn[data-loading]', function() {
                const $btn = $(this);
                const loadingText = $btn.data('loading');
                
                if (loadingText) {
                    $btn.data('original-text', $btn.text());
                    $btn.text(loadingText).prop('disabled', true);
                }
            });
        },

        /**
         * Initialize accessibility features
         */
        initAccessibility: function() {
            // Keyboard navigation for custom elements
            $(document).on('keydown', '.redco-btn, .module-toggle', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    $(this).click();
                }
            });

            // Focus management for modals
            $(document).on('shown.bs.modal', '.modal', function() {
                $(this).find('input, button, select, textarea').first().focus();
            });

            // Skip links
            $('.skip-link').on('click', function(e) {
                e.preventDefault();
                const target = $($(this).attr('href'));
                if (target.length) {
                    target.focus();
                }
            });
        },

        /**
         * Update dependent elements after changes
         */
        updateDependentElements: function(data) {
            if (data.stats) {
                RedcoAdmin.Stats.updateStats(data.stats);
            }
            
            if (data.modules) {
                RedcoAdmin.Core.updateModuleStatuses(data.modules);
            }
        },

        /**
         * Update module status
         */
        updateModuleStatus: function(moduleKey, isEnabled) {
            const $statusElements = $('[data-module-status="' + moduleKey + '"]');
            $statusElements.toggleClass('enabled', isEnabled).toggleClass('disabled', !isEnabled);
            
            // Update status text
            $statusElements.find('.status-text').text(isEnabled ? 'Enabled' : 'Disabled');
        },

        /**
         * Update multiple module statuses
         */
        updateModuleStatuses: function(modules) {
            $.each(modules, function(moduleKey, status) {
                RedcoAdmin.Core.updateModuleStatus(moduleKey, status.enabled);
            });
        }
    };

    /**
     * UI Helper Functions
     */
    RedcoAdmin.UI = {
        /**
         * Show saving indicator
         */
        showSavingIndicator: function($field) {
            const $indicator = this.getOrCreateIndicator($field);
            $indicator.removeClass('success error').addClass('saving')
                     .html('<span class="dashicons dashicons-update-alt"></span> Saving...');
        },

        /**
         * Show success indicator
         */
        showSuccessIndicator: function($field) {
            const $indicator = this.getOrCreateIndicator($field);
            $indicator.removeClass('saving error').addClass('success')
                     .html('<span class="dashicons dashicons-yes-alt"></span> Saved');
            
            setTimeout(function() {
                $indicator.fadeOut();
            }, 2000);
        },

        /**
         * Show error indicator
         */
        showErrorIndicator: function($field, message) {
            const $indicator = this.getOrCreateIndicator($field);
            $indicator.removeClass('saving success').addClass('error')
                     .html('<span class="dashicons dashicons-warning"></span> ' + (message || 'Error'));
        },

        /**
         * Get or create status indicator
         */
        getOrCreateIndicator: function($field) {
            let $indicator = $field.siblings('.save-indicator');
            
            if (!$indicator.length) {
                $indicator = $('<span class="save-indicator"></span>');
                $field.after($indicator);
            }
            
            return $indicator.show();
        },

        /**
         * Show notification
         */
        showNotification: function(message, type) {
            const $notification = $('<div class="redco-notification ' + type + '">' + message + '</div>');
            
            $('body').append($notification);
            
            setTimeout(function() {
                $notification.addClass('show');
            }, 100);
            
            setTimeout(function() {
                $notification.removeClass('show');
                setTimeout(function() {
                    $notification.remove();
                }, 300);
            }, 3000);
        },

        /**
         * Show loading state
         */
        showLoadingState: function($element) {
            $element.addClass('loading').attr('aria-busy', 'true');
        },

        /**
         * Hide loading state
         */
        hideLoadingState: function($element) {
            $element.removeClass('loading').removeAttr('aria-busy');
        },

        /**
         * Update field validation
         */
        updateFieldValidation: function($field, isValid, message) {
            const $group = $field.closest('.redco-form-group');
            const $feedback = $group.find('.invalid-feedback');
            
            $group.toggleClass('has-error', !isValid);
            $field.toggleClass('is-invalid', !isValid);
            
            if (!isValid && message) {
                if (!$feedback.length) {
                    $field.after('<div class="invalid-feedback"></div>');
                }
                $group.find('.invalid-feedback').text(message);
            } else {
                $feedback.remove();
            }
        }
    };

    /**
     * Utility Functions
     */
    RedcoAdmin.Utils = {
        /**
         * Validate email address
         */
        isValidEmail: function(email) {
            const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        },

        /**
         * Validate URL
         */
        isValidUrl: function(url) {
            try {
                new URL(url);
                return true;
            } catch {
                return false;
            }
        },

        /**
         * Debounce function
         */
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        /**
         * Format file size
         */
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        /**
         * Format number with commas
         */
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
    };

    /**
     * Statistics Management
     */
    RedcoAdmin.Stats = {
        init: function() {
            this.initStatsRefresh();
            this.initCharts();
        },

        /**
         * Initialize statistics refresh
         */
        initStatsRefresh: function() {
            $(document).on('click', '.refresh-stats', function(e) {
                e.preventDefault();
                const $btn = $(this);
                const statsType = $btn.data('stats-type') || 'all';

                RedcoAdmin.Stats.refreshStats(statsType, $btn);
            });

            // Auto-refresh stats every 30 seconds
            if (redcoAdmin.autoRefreshStats) {
                setInterval(function() {
                    RedcoAdmin.Stats.refreshStats('all');
                }, 30000);
            }
        },

        /**
         * Refresh statistics
         */
        refreshStats: function(type, $trigger) {
            const data = {
                action: 'redco_refresh_stats',
                type: type,
                nonce: redcoAdmin.nonce
            };

            if ($trigger) {
                RedcoAdmin.UI.showLoadingState($trigger);
            }

            $.ajax({
                url: redcoAdmin.ajaxurl,
                type: 'POST',
                data: data,
                success: function(response) {
                    if (response.success) {
                        RedcoAdmin.Stats.updateStats(response.data.stats);
                        RedcoAdmin.UI.showNotification('Statistics updated', 'success');
                    } else {
                        RedcoAdmin.UI.showNotification(response.data.message || 'Failed to refresh statistics', 'error');
                    }
                },
                error: function() {
                    RedcoAdmin.UI.showNotification('Network error occurred', 'error');
                },
                complete: function() {
                    if ($trigger) {
                        RedcoAdmin.UI.hideLoadingState($trigger);
                    }
                }
            });
        },

        /**
         * Update statistics display
         */
        updateStats: function(stats) {
            $.each(stats, function(key, value) {
                const $statElement = $('[data-stat="' + key + '"]');
                if ($statElement.length) {
                    if (typeof value === 'number') {
                        $statElement.text(RedcoAdmin.Utils.formatNumber(value));
                    } else {
                        $statElement.text(value);
                    }

                    // Add update animation
                    $statElement.addClass('stat-updated');
                    setTimeout(function() {
                        $statElement.removeClass('stat-updated');
                    }, 1000);
                }
            });
        },

        /**
         * Initialize charts
         */
        initCharts: function() {
            if (typeof Chart !== 'undefined') {
                this.initPerformanceChart();
                this.initModuleUsageChart();
            }
        },

        /**
         * Initialize performance chart
         */
        initPerformanceChart: function() {
            const $canvas = $('#redco-performance-chart');
            if (!$canvas.length) return;

            const ctx = $canvas[0].getContext('2d');
            const chartData = $canvas.data('chart-data');

            new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        },

        /**
         * Initialize module usage chart
         */
        initModuleUsageChart: function() {
            const $canvas = $('#redco-module-usage-chart');
            if (!$canvas.length) return;

            const ctx = $canvas[0].getContext('2d');
            const chartData = $canvas.data('chart-data');

            new Chart(ctx, {
                type: 'doughnut',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
        }
    };

    /**
     * Module-specific functionality
     */
    RedcoAdmin.Modules = {
        init: function() {
            this.initQuickActions();
            this.initBulkOperations();
        },

        /**
         * Initialize quick actions
         */
        initQuickActions: function() {
            $(document).on('click', '.quick-action-btn', function(e) {
                e.preventDefault();
                const $btn = $(this);
                const action = $btn.data('action');
                const module = $btn.data('module');

                RedcoAdmin.Modules.performQuickAction(action, module, $btn);
            });
        },

        /**
         * Perform quick action
         */
        performQuickAction: function(action, module, $trigger) {
            const data = {
                action: 'redco_quick_action',
                quick_action: action,
                module: module,
                nonce: redcoAdmin.nonce
            };

            RedcoAdmin.UI.showLoadingState($trigger);

            $.ajax({
                url: redcoAdmin.ajaxurl,
                type: 'POST',
                data: data,
                success: function(response) {
                    if (response.success) {
                        RedcoAdmin.UI.showNotification(response.data.message || 'Action completed successfully', 'success');

                        // Update UI based on action
                        if (response.data.reload) {
                            location.reload();
                        } else if (response.data.stats) {
                            RedcoAdmin.Stats.updateStats(response.data.stats);
                        }
                    } else {
                        RedcoAdmin.UI.showNotification(response.data.message || 'Action failed', 'error');
                    }
                },
                error: function() {
                    RedcoAdmin.UI.showNotification('Network error occurred', 'error');
                },
                complete: function() {
                    RedcoAdmin.UI.hideLoadingState($trigger);
                }
            });
        },

        /**
         * Initialize bulk operations
         */
        initBulkOperations: function() {
            $(document).on('click', '.bulk-action-btn', function(e) {
                e.preventDefault();
                const $btn = $(this);
                const action = $btn.data('action');
                const items = RedcoAdmin.Modules.getSelectedItems();

                if (items.length === 0) {
                    RedcoAdmin.UI.showNotification('Please select items first', 'warning');
                    return;
                }

                RedcoAdmin.Modules.performBulkAction(action, items, $btn);
            });

            // Select all functionality
            $(document).on('change', '.select-all', function() {
                const isChecked = $(this).is(':checked');
                $('.bulk-select').prop('checked', isChecked);
            });
        },

        /**
         * Get selected items for bulk operations
         */
        getSelectedItems: function() {
            const items = [];
            $('.bulk-select:checked').each(function() {
                items.push($(this).val());
            });
            return items;
        },

        /**
         * Perform bulk action
         */
        performBulkAction: function(action, items, $trigger) {
            const data = {
                action: 'redco_bulk_action',
                bulk_action: action,
                items: items,
                nonce: redcoAdmin.nonce
            };

            RedcoAdmin.UI.showLoadingState($trigger);

            $.ajax({
                url: redcoAdmin.ajaxurl,
                type: 'POST',
                data: data,
                success: function(response) {
                    if (response.success) {
                        RedcoAdmin.UI.showNotification(response.data.message || 'Bulk action completed', 'success');

                        if (response.data.reload) {
                            location.reload();
                        }
                    } else {
                        RedcoAdmin.UI.showNotification(response.data.message || 'Bulk action failed', 'error');
                    }
                },
                error: function() {
                    RedcoAdmin.UI.showNotification('Network error occurred', 'error');
                },
                complete: function() {
                    RedcoAdmin.UI.hideLoadingState($trigger);
                }
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        RedcoAdmin.Core.init();
        RedcoAdmin.Stats.init();
        RedcoAdmin.Modules.init();
    });

})(jQuery);
