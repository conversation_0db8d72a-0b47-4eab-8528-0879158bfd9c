/**
 * CRITICAL FIX: Performance Monitoring Module
 * Extracted from large admin-scripts.js for better maintainability
 * Handles all performance monitoring and metrics display
 */

(function($) {
    'use strict';

    /**
     * Performance Monitor Module
     */
    window.RedcoPerformanceMonitor = {
        
        /**
         * Configuration
         */
        config: {
            updateInterval: 60000, // 1 minute
            retryDelay: 10000, // 10 seconds
            cacheTimeout: 300000, // 5 minutes
            maxRetries: 3
        },
        
        /**
         * State management
         */
        state: {
            updateTimer: null,
            updateActive: false,
            cache: new Map(),
            retryCount: 0
        },
        
        /**
         * Initialize performance monitoring
         */
        init: function() {
            this.initUI();
            this.startMonitoring();
        },
        
        /**
         * Initialize UI components
         */
        initUI: function() {
            this.initCoreWebVitalsChart();
            this.bindRefreshHandlers();
        },
        
        /**
         * Start performance monitoring
         */
        startMonitoring: function() {
            // Initial load
            this.updateMetrics();
            
            // Set up periodic updates
            this.state.updateTimer = setInterval(() => {
                this.updateMetrics();
            }, this.config.updateInterval);
        },
        
        /**
         * Stop performance monitoring
         */
        stopMonitoring: function() {
            if (this.state.updateTimer) {
                clearInterval(this.state.updateTimer);
                this.state.updateTimer = null;
            }
        },
        
        /**
         * Update performance metrics
         */
        updateMetrics: function() {
            if (this.state.updateActive) {
                return;
            }
            
            this.state.updateActive = true;
            
            // Check cache first
            const cached = this.getCachedData('performance_metrics');
            if (cached) {
                this.displayMetrics(cached);
                this.state.updateActive = false;
                return;
            }
            
            // Fetch new data
            this.fetchMetrics();
        },
        
        /**
         * Fetch metrics from server
         */
        fetchMetrics: function() {
            const self = this;
            
            // CRITICAL FIX: Use centralized AJAX utility if available
            if (typeof RedcoAjax !== 'undefined') {
                RedcoAjax.request({
                    action: 'redco_get_performance_metrics',
                    data: {
                        nonce: redcoAjax.nonce
                    },
                    success: function(response) {
                        self.handleMetricsSuccess(response);
                    },
                    error: function(error) {
                        self.handleMetricsError(error);
                    },
                    settings: {
                        timeout: 15000,
                        cache: true,
                        cacheDuration: self.config.cacheTimeout
                    }
                });
            } else {
                // Fallback to traditional AJAX
                $.ajax({
                    url: redcoAjax.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_get_performance_metrics',
                        nonce: redcoAjax.nonce
                    },
                    timeout: 15000,
                    success: function(response) {
                        self.handleMetricsSuccess(response);
                    },
                    error: function(xhr, status, error) {
                        self.handleMetricsError(error);
                    }
                });
            }
        },
        
        /**
         * Handle successful metrics response
         */
        handleMetricsSuccess: function(response) {
            if (response.success && response.data) {
                this.setCachedData('performance_metrics', response.data);
                this.displayMetrics(response.data);
                this.state.retryCount = 0;
            } else {
                this.handleMetricsError('Invalid response format');
            }
            
            this.state.updateActive = false;
        },
        
        /**
         * Handle metrics error
         */
        handleMetricsError: function(error) {
            this.state.retryCount++;
            
            if (this.state.retryCount <= this.config.maxRetries) {
                // Retry after delay
                setTimeout(() => {
                    this.fetchMetrics();
                }, this.config.retryDelay);
            } else {
                // Max retries reached
                this.state.updateActive = false;
                this.state.retryCount = 0;
            }
        },
        
        /**
         * Display metrics in UI
         */
        displayMetrics: function(data) {
            // Update performance cards
            this.updatePerformanceCards(data);
            
            // Update charts
            this.updateCharts(data);
            
            // Update health score
            this.updateHealthScore(data);
        },
        
        /**
         * Update performance cards
         */
        updatePerformanceCards: function(data) {
            if (data.page_load_time !== undefined) {
                $('.metric-page-load .metric-value').text(data.page_load_time + 's');
            }
            
            if (data.memory_usage !== undefined) {
                $('.metric-memory .metric-value').text(this.formatBytes(data.memory_usage));
            }
            
            if (data.database_queries !== undefined) {
                $('.metric-queries .metric-value').text(data.database_queries);
            }
            
            if (data.cache_hit_ratio !== undefined) {
                $('.metric-cache .metric-value').text(data.cache_hit_ratio + '%');
            }
        },
        
        /**
         * Update charts
         */
        updateCharts: function(data) {
            if (typeof Chart !== 'undefined' && data.core_web_vitals) {
                this.updateCoreWebVitalsChart(data.core_web_vitals);
            }
        },
        
        /**
         * Update health score
         */
        updateHealthScore: function(data) {
            if (data.health_score !== undefined) {
                const $healthScore = $('.health-score-value');
                const $healthBar = $('.health-score-bar .progress-fill');
                
                $healthScore.text(data.health_score);
                $healthBar.css('width', data.health_score + '%');
                
                // Update color based on score
                const scoreClass = this.getScoreClass(data.health_score);
                $healthBar.removeClass('score-excellent score-good score-needs-improvement score-poor')
                         .addClass(scoreClass);
            }
        },
        
        /**
         * Get score class based on value
         */
        getScoreClass: function(score) {
            if (score >= 90) return 'score-excellent';
            if (score >= 75) return 'score-good';
            if (score >= 50) return 'score-needs-improvement';
            return 'score-poor';
        },
        
        /**
         * Initialize Core Web Vitals chart
         */
        initCoreWebVitalsChart: function() {
            const canvas = document.getElementById('coreWebVitalsChart');
            if (!canvas || typeof Chart === 'undefined') {
                return;
            }
            
            const ctx = canvas.getContext('2d');
            this.coreWebVitalsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'LCP (s)',
                        data: [],
                        borderColor: '#4CAF50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'FID (ms)',
                        data: [],
                        borderColor: '#2196F3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'CLS',
                        data: [],
                        borderColor: '#FF9800',
                        backgroundColor: 'rgba(255, 152, 0, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        },
        
        /**
         * Update Core Web Vitals chart
         */
        updateCoreWebVitalsChart: function(data) {
            if (!this.coreWebVitalsChart || !data) {
                return;
            }
            
            // Update chart data
            this.coreWebVitalsChart.data.labels = data.labels || [];
            this.coreWebVitalsChart.data.datasets[0].data = data.lcp || [];
            this.coreWebVitalsChart.data.datasets[1].data = data.fid || [];
            this.coreWebVitalsChart.data.datasets[2].data = data.cls || [];
            
            this.coreWebVitalsChart.update();
        },
        
        /**
         * Bind refresh handlers
         */
        bindRefreshHandlers: function() {
            const self = this;
            
            $(document).on('click', '.refresh-performance-metrics', function(e) {
                e.preventDefault();
                self.clearCache();
                self.updateMetrics();
            });
        },
        
        /**
         * Format bytes to human readable format
         */
        formatBytes: function(bytes) {
            if (bytes === 0) return '0 B';
            
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        /**
         * Cache management
         */
        getCachedData: function(key) {
            const cached = this.state.cache.get(key);
            return cached && (Date.now() - cached.timestamp) < this.config.cacheTimeout ? cached.data : null;
        },
        
        setCachedData: function(key, data) {
            this.state.cache.set(key, {
                data: data,
                timestamp: Date.now()
            });
        },
        
        clearCache: function(key) {
            if (key) {
                this.state.cache.delete(key);
            } else {
                this.state.cache.clear();
            }
        }
    };

})(jQuery);
