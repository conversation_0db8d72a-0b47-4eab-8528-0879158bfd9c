<?php
/**
 * Accessibility Analyzer for Redco Optimizer
 * 
 * WCAG 2.1 compliance checking and accessibility optimization
 */

class Redco_Accessibility_Analyzer {
    
    private $wcag_levels = array('A', 'AA', 'AAA');
    
    /**
     * Comprehensive accessibility analysis
     */
    public function analyze_accessibility($url, $wcag_level = 'AA') {
        return array(
            'images' => $this->analyze_image_accessibility($url),
            'forms' => $this->analyze_form_accessibility($url),
            'navigation' => $this->analyze_navigation_accessibility($url),
            'headings' => $this->analyze_heading_structure($url),
            'color_contrast' => $this->analyze_color_contrast($url),
            'keyboard_navigation' => $this->analyze_keyboard_navigation($url),
            'aria_labels' => $this->analyze_aria_implementation($url),
            'focus_management' => $this->analyze_focus_management($url),
            'semantic_markup' => $this->analyze_semantic_markup($url),
            'wcag_compliance' => $this->check_wcag_compliance($url, $wcag_level)
        );
    }
    
    /**
     * Image Accessibility Analysis
     */
    private function analyze_image_accessibility($url) {
        $issues = array();
        $page_content = $this->get_page_content($url);
        
        // Find all images
        preg_match_all('/<img[^>]*>/i', $page_content, $img_matches);
        
        foreach ($img_matches[0] as $img_tag) {
            // Check for missing alt attributes
            if (!preg_match('/alt\s*=\s*["\'][^"\']*["\']/', $img_tag)) {
                $issues[] = array(
                    'id' => 'missing_alt_text_' . md5($img_tag),
                    'title' => 'Missing Alt Text',
                    'description' => 'Image found without alt attribute for screen readers.',
                    'severity' => 'high',
                    'category' => 'accessibility',
                    'auto_fixable' => true,
                    'fix_action' => 'add_alt_text',
                    'wcag_criterion' => '1.1.1',
                    'wcag_level' => 'A'
                );
            }
            
            // Check for empty alt on decorative images
            if (preg_match('/alt\s*=\s*["\']["\']/', $img_tag) && !preg_match('/role\s*=\s*["\']presentation["\']/', $img_tag)) {
                $issues[] = array(
                    'id' => 'empty_alt_without_role_' . md5($img_tag),
                    'title' => 'Empty Alt Without Presentation Role',
                    'description' => 'Decorative images should have role="presentation" when alt is empty.',
                    'severity' => 'medium',
                    'category' => 'accessibility',
                    'auto_fixable' => true,
                    'fix_action' => 'add_presentation_role'
                );
            }
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * Form Accessibility Analysis
     */
    private function analyze_form_accessibility($url) {
        $issues = array();
        $page_content = $this->get_page_content($url);
        
        // Find all form inputs
        preg_match_all('/<input[^>]*>/i', $page_content, $input_matches);
        preg_match_all('/<textarea[^>]*>/i', $page_content, $textarea_matches);
        preg_match_all('/<select[^>]*>/i', $page_content, $select_matches);
        
        $form_elements = array_merge($input_matches[0], $textarea_matches[0], $select_matches[0]);
        
        foreach ($form_elements as $element) {
            // Check for missing labels
            if (!preg_match('/id\s*=\s*["\']([^"\']*)["\']/', $element, $id_matches)) {
                $issues[] = array(
                    'id' => 'form_element_no_id_' . md5($element),
                    'title' => 'Form Element Without ID',
                    'description' => 'Form element lacks ID for label association.',
                    'severity' => 'high',
                    'category' => 'accessibility',
                    'auto_fixable' => true,
                    'fix_action' => 'add_form_element_id',
                    'wcag_criterion' => '1.3.1',
                    'wcag_level' => 'A'
                );
            } else {
                $element_id = $id_matches[1];
                // Check if there's a corresponding label
                if (!preg_match('/for\s*=\s*["\']' . preg_quote($element_id, '/') . '["\']/', $page_content)) {
                    $issues[] = array(
                        'id' => 'missing_form_label_' . $element_id,
                        'title' => 'Missing Form Label',
                        'description' => 'Form element has no associated label.',
                        'severity' => 'high',
                        'category' => 'accessibility',
                        'auto_fixable' => true,
                        'fix_action' => 'add_form_label',
                        'wcag_criterion' => '1.3.1',
                        'wcag_level' => 'A'
                    );
                }
            }
            
            // Check for required field indicators
            if (preg_match('/required/', $element) && !preg_match('/aria-required\s*=\s*["\']true["\']/', $element)) {
                $issues[] = array(
                    'id' => 'missing_aria_required_' . md5($element),
                    'title' => 'Missing aria-required Attribute',
                    'description' => 'Required form field should have aria-required="true".',
                    'severity' => 'medium',
                    'category' => 'accessibility',
                    'auto_fixable' => true,
                    'fix_action' => 'add_aria_required',
                    'wcag_criterion' => '3.3.2',
                    'wcag_level' => 'A'
                );
            }
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * Navigation Accessibility Analysis
     */
    private function analyze_navigation_accessibility($url) {
        $issues = array();
        $page_content = $this->get_page_content($url);
        
        // Check for skip links
        if (!preg_match('/<a[^>]*href\s*=\s*["\']#[^"\']*["\'][^>]*>.*?skip.*?<\/a>/i', $page_content)) {
            $issues[] = array(
                'id' => 'missing_skip_links',
                'title' => 'Missing Skip Navigation Links',
                'description' => 'Skip links help keyboard users navigate efficiently.',
                'severity' => 'medium',
                'category' => 'accessibility',
                'auto_fixable' => true,
                'fix_action' => 'add_skip_links',
                'wcag_criterion' => '2.4.1',
                'wcag_level' => 'A'
            );
        }
        
        // Check for navigation landmarks
        if (!preg_match('/<nav[^>]*>/i', $page_content) && !preg_match('/role\s*=\s*["\']navigation["\']/', $page_content)) {
            $issues[] = array(
                'id' => 'missing_nav_landmarks',
                'title' => 'Missing Navigation Landmarks',
                'description' => 'Navigation areas should use <nav> element or role="navigation".',
                'severity' => 'medium',
                'category' => 'accessibility',
                'auto_fixable' => true,
                'fix_action' => 'add_nav_landmarks',
                'wcag_criterion' => '1.3.1',
                'wcag_level' => 'A'
            );
        }
        
        // Check for focus indicators
        if (!preg_match('/:focus\s*{[^}]*outline[^}]*}/', $page_content)) {
            $issues[] = array(
                'id' => 'missing_focus_indicators',
                'title' => 'Missing Focus Indicators',
                'description' => 'Interactive elements should have visible focus indicators.',
                'severity' => 'high',
                'category' => 'accessibility',
                'auto_fixable' => true,
                'fix_action' => 'add_focus_indicators',
                'wcag_criterion' => '2.4.7',
                'wcag_level' => 'AA'
            );
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * Heading Structure Analysis
     */
    private function analyze_heading_structure($url) {
        $issues = array();
        $page_content = $this->get_page_content($url);
        
        // Extract all headings
        preg_match_all('/<h([1-6])[^>]*>.*?<\/h[1-6]>/i', $page_content, $heading_matches, PREG_SET_ORDER);
        
        $heading_levels = array();
        foreach ($heading_matches as $match) {
            $heading_levels[] = intval($match[1]);
        }
        
        // Check for missing H1
        if (!in_array(1, $heading_levels)) {
            $issues[] = array(
                'id' => 'missing_h1',
                'title' => 'Missing H1 Heading',
                'description' => 'Page should have exactly one H1 heading.',
                'severity' => 'high',
                'category' => 'accessibility',
                'auto_fixable' => true,
                'fix_action' => 'add_h1_heading',
                'wcag_criterion' => '1.3.1',
                'wcag_level' => 'A'
            );
        }
        
        // Check for multiple H1s
        if (array_count_values($heading_levels)[1] > 1) {
            $issues[] = array(
                'id' => 'multiple_h1',
                'title' => 'Multiple H1 Headings',
                'description' => 'Page should have only one H1 heading.',
                'severity' => 'medium',
                'category' => 'accessibility',
                'auto_fixable' => true,
                'fix_action' => 'fix_multiple_h1'
            );
        }
        
        // Check for skipped heading levels
        for ($i = 1; $i < count($heading_levels); $i++) {
            if ($heading_levels[$i] - $heading_levels[$i-1] > 1) {
                $issues[] = array(
                    'id' => 'skipped_heading_level',
                    'title' => 'Skipped Heading Level',
                    'description' => 'Heading levels should not skip (e.g., H2 to H4).',
                    'severity' => 'medium',
                    'category' => 'accessibility',
                    'auto_fixable' => true,
                    'fix_action' => 'fix_heading_hierarchy',
                    'wcag_criterion' => '1.3.1',
                    'wcag_level' => 'A'
                );
                break;
            }
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * Color Contrast Analysis
     */
    private function analyze_color_contrast($url) {
        $issues = array();
        $page_content = $this->get_page_content($url);
        
        // Extract CSS color definitions
        preg_match_all('/color\s*:\s*([^;]+);/', $page_content, $color_matches);
        preg_match_all('/background-color\s*:\s*([^;]+);/', $page_content, $bg_color_matches);
        
        // This is a simplified check - real implementation would need CSS parsing and contrast calculation
        if (count($color_matches[0]) > 0) {
            $issues[] = array(
                'id' => 'color_contrast_check_needed',
                'title' => 'Color Contrast Verification Needed',
                'description' => 'Text and background colors should meet WCAG contrast ratios (4.5:1 for normal text, 3:1 for large text).',
                'severity' => 'medium',
                'category' => 'accessibility',
                'auto_fixable' => false,
                'fix_action' => 'verify_color_contrast',
                'wcag_criterion' => '1.4.3',
                'wcag_level' => 'AA',
                'recommendation' => 'Use tools like WebAIM Color Contrast Checker to verify all color combinations'
            );
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * ARIA Implementation Analysis
     */
    private function analyze_aria_implementation($url) {
        $issues = array();
        $page_content = $this->get_page_content($url);
        
        // Check for interactive elements without ARIA labels
        preg_match_all('/<button[^>]*>/i', $page_content, $button_matches);
        foreach ($button_matches[0] as $button) {
            if (!preg_match('/aria-label\s*=/', $button) && !preg_match('/>.*?[a-zA-Z].*?</', $button)) {
                $issues[] = array(
                    'id' => 'button_missing_label_' . md5($button),
                    'title' => 'Button Missing Accessible Label',
                    'description' => 'Button has no visible text or aria-label.',
                    'severity' => 'high',
                    'category' => 'accessibility',
                    'auto_fixable' => true,
                    'fix_action' => 'add_button_label',
                    'wcag_criterion' => '4.1.2',
                    'wcag_level' => 'A'
                );
            }
        }
        
        // Check for custom interactive elements without proper ARIA
        preg_match_all('/<div[^>]*onclick[^>]*>/i', $page_content, $clickable_divs);
        foreach ($clickable_divs[0] as $div) {
            if (!preg_match('/role\s*=/', $div) || !preg_match('/tabindex\s*=/', $div)) {
                $issues[] = array(
                    'id' => 'clickable_div_missing_aria_' . md5($div),
                    'title' => 'Clickable Element Missing ARIA',
                    'description' => 'Clickable div should have appropriate role and tabindex.',
                    'severity' => 'high',
                    'category' => 'accessibility',
                    'auto_fixable' => true,
                    'fix_action' => 'add_div_aria_attributes'
                );
            }
        }
        
        return array('issues' => $issues);
    }
    
    /**
     * Get page content for analysis
     */
    private function get_page_content($url) {
        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'user-agent' => 'Redco Accessibility Analyzer'
        ));
        
        if (is_wp_error($response)) {
            return '';
        }
        
        return wp_remote_retrieve_body($response);
    }
    
    /**
     * Check WCAG compliance level
     */
    private function check_wcag_compliance($url, $level) {
        // This would aggregate all issues and determine compliance level
        return array(
            'level' => $level,
            'compliant' => false,
            'issues_by_level' => array(
                'A' => 5,
                'AA' => 3,
                'AAA' => 1
            )
        );
    }
}
