<?php
/**
 * CRITICAL FIX: Admin Settings Manager
 * Extracted from large class-admin-ui.php to improve maintainability
 * Handles all settings registration, sanitization, and validation
 *
 * @package Redco_Optimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin Settings Manager Class
 * Responsible for settings registration and sanitization
 */
class Redco_Admin_Settings_Manager {
    
    /**
     * Available modules
     */
    private $modules = array();
    
    /**
     * Constructor
     *
     * @param array $modules Available modules
     */
    public function __construct($modules) {
        $this->modules = $modules;
    }
    
    /**
     * Initialize settings manager
     */
    public function init() {
        add_action('admin_init', array($this, 'register_settings'));
    }
    
    /**
     * Register all plugin settings
     */
    public function register_settings() {
        // Register global plugin settings
        register_setting('redco_optimizer_options', 'redco_optimizer_options', array($this, 'sanitize_options'));
        register_setting('redco_optimizer_performance', 'redco_optimizer_performance', array($this, 'sanitize_performance_settings'));
        register_setting('redco_optimizer_advanced', 'redco_optimizer_advanced', array($this, 'sanitize_advanced_settings'));
        register_setting('redco_optimizer_security', 'redco_optimizer_security', array($this, 'sanitize_security_settings'));

        // Register settings for each module
        foreach ($this->modules as $module_key => $module_data) {
            if ($module_data['type'] === 'free' && !isset($module_data['coming_soon'])) {
                register_setting('redco_optimizer_' . str_replace('-', '_', $module_key), 'redco_optimizer_' . str_replace('-', '_', $module_key));
            }
        }
    }
    
    /**
     * Sanitize main options
     *
     * @param array $input Raw input data
     * @return array Sanitized data
     */
    public function sanitize_options($input) {
        $sanitized = array();

        if (isset($input['modules_enabled']) && is_array($input['modules_enabled'])) {
            $sanitized['modules_enabled'] = array_map('sanitize_text_field', $input['modules_enabled']);
        }

        // Handle checkboxes with hidden field approach
        $sanitized['enabled'] = $this->sanitize_checkbox_field($input, 'enabled', 1);
        $sanitized['auto_enable_modules'] = $this->sanitize_checkbox_field($input, 'auto_enable_modules', 0);
        $sanitized['debug_mode'] = $this->sanitize_checkbox_field($input, 'debug_mode', 0);

        return $sanitized;
    }
    
    /**
     * Sanitize performance settings
     *
     * @param array $input Raw input data
     * @return array Sanitized data
     */
    public function sanitize_performance_settings($input) {
        $sanitized = array();

        // Handle checkbox with hidden field approach
        $sanitized['enable_monitoring'] = $this->sanitize_checkbox_field($input, 'enable_monitoring', 1);
        $sanitized['update_interval'] = isset($input['update_interval']) ? absint($input['update_interval']) : 30;
        $sanitized['data_retention'] = isset($input['data_retention']) ? absint($input['data_retention']) : 30;
        $sanitized['pagespeed_api_key'] = isset($input['pagespeed_api_key']) ? sanitize_text_field($input['pagespeed_api_key']) : '';

        // Handle numeric performance thresholds
        $sanitized['slow_query_threshold'] = isset($input['slow_query_threshold']) ? floatval($input['slow_query_threshold']) : 0.1;
        $sanitized['memory_threshold'] = isset($input['memory_threshold']) ? absint($input['memory_threshold']) : 67108864;

        // Handle cache settings
        $sanitized['enable_query_cache'] = $this->sanitize_checkbox_field($input, 'enable_query_cache', 1);
        $sanitized['cache_duration'] = isset($input['cache_duration']) ? absint($input['cache_duration']) : 300;
        $sanitized['enable_maintenance'] = $this->sanitize_checkbox_field($input, 'enable_maintenance', 1);

        // CRITICAL FIX: Use debug utility instead of direct error_log
        if (class_exists('Redco_Debug_Utils')) {
            Redco_Debug_Utils::log('Performance settings sanitized', 'Settings');
        }

        return $sanitized;
    }
    
    /**
     * Sanitize security settings
     *
     * @param array $input Raw input data
     * @return array Sanitized data
     */
    public function sanitize_security_settings($input) {
        $sanitized = array();

        // Handle security level dropdown
        $valid_levels = array('low', 'medium', 'high', 'strict');
        $sanitized['security_level'] = isset($input['security_level']) && in_array($input['security_level'], $valid_levels)
            ? $input['security_level'] : 'low';

        // Handle numeric security settings
        $sanitized['max_failed_attempts'] = isset($input['max_failed_attempts']) ? absint($input['max_failed_attempts']) : 10;
        $sanitized['lockout_duration'] = isset($input['lockout_duration']) ? absint($input['lockout_duration']) : 900;

        // Handle checkbox security settings
        $sanitized['enable_file_monitoring'] = $this->sanitize_checkbox_field($input, 'enable_file_monitoring', 0);
        $sanitized['enable_request_filtering'] = $this->sanitize_checkbox_field($input, 'enable_request_filtering', 0);
        $sanitized['enable_admin_protection'] = $this->sanitize_checkbox_field($input, 'enable_admin_protection', 0);
        $sanitized['enable_error_logging'] = $this->sanitize_checkbox_field($input, 'enable_error_logging', 1);

        // Handle log level dropdown
        $valid_log_levels = array('emergency', 'critical', 'error', 'warning', 'info', 'debug');
        $sanitized['min_log_level'] = isset($input['min_log_level']) && in_array($input['min_log_level'], $valid_log_levels)
            ? $input['min_log_level'] : 'error';

        // Update the security manager configuration
        if (class_exists('Redco_Security_Manager')) {
            Redco_Security_Manager::update_config($sanitized);
        }

        // CRITICAL FIX: Use debug utility instead of direct error_log
        if (class_exists('Redco_Debug_Utils')) {
            Redco_Debug_Utils::log('Security settings sanitized', 'Settings');
        }

        return $sanitized;
    }
    
    /**
     * Sanitize advanced settings
     *
     * @param array $input Raw input data
     * @return array Sanitized data
     */
    public function sanitize_advanced_settings($input) {
        $sanitized = array();

        // Handle checkbox with hidden field approach
        $sanitized['cleanup_on_uninstall'] = $this->sanitize_checkbox_field($input, 'cleanup_on_uninstall', 0);
        $sanitized['cache_dir'] = sanitize_text_field($input['cache_dir']);
        $sanitized['excluded_roles'] = isset($input['excluded_roles']) ? array_map('sanitize_text_field', $input['excluded_roles']) : array();

        return $sanitized;
    }
    
    /**
     * Helper method to sanitize checkbox fields with hidden field approach
     *
     * @param array $input Input data
     * @param string $field_name Field name
     * @param int $default Default value
     * @return int Sanitized checkbox value
     */
    private function sanitize_checkbox_field($input, $field_name, $default = 0) {
        // CRITICAL FIX: Use debug utility instead of direct error_log
        if (class_exists('Redco_Debug_Utils')) {
            Redco_Debug_Utils::log("Checkbox field '$field_name' processing", 'Settings');
        }

        if (!isset($input[$field_name])) {
            return $default;
        }

        // If it's an array (both hidden and checkbox values), get the last value (checkbox)
        if (is_array($input[$field_name])) {
            $value = end($input[$field_name]);
        } else {
            $value = $input[$field_name];
        }

        $result = ($value == '1') ? 1 : 0;

        // CRITICAL FIX: Use debug utility instead of direct error_log
        if (class_exists('Redco_Debug_Utils')) {
            Redco_Debug_Utils::log("Checkbox field '$field_name' result: $result", 'Settings');
        }

        return $result;
    }
}
