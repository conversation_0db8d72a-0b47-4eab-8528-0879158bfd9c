<?php
/**
 * Tiered Fix System for Redco Optimizer
 * 
 * Implements Safe/Moderate/Advanced fix categories with granular control
 */

class Redco_Tiered_Fix_System {
    
    private $fix_tiers = array(
        'safe' => array(
            'risk_level' => 'low',
            'backup_required' => false,
            'user_confirmation' => false,
            'rollback_window' => 24, // hours
            'description' => 'Low-risk optimizations that are unlikely to cause issues'
        ),
        'moderate' => array(
            'risk_level' => 'medium',
            'backup_required' => true,
            'user_confirmation' => true,
            'rollback_window' => 72, // hours
            'description' => 'Medium-risk optimizations that may require testing'
        ),
        'advanced' => array(
            'risk_level' => 'high',
            'backup_required' => true,
            'user_confirmation' => true,
            'rollback_window' => 168, // hours (1 week)
            'description' => 'High-risk optimizations for experienced users only'
        )
    );
    
    private $fix_categories = array(
        'database' => array(
            'name' => 'Database Optimization',
            'icon' => 'dashicons-database',
            'description' => 'Database cleanup and optimization fixes'
        ),
        'frontend' => array(
            'name' => 'Frontend Performance',
            'icon' => 'dashicons-performance',
            'description' => 'CSS, JavaScript, and rendering optimizations'
        ),
        'server' => array(
            'name' => 'Server Configuration',
            'icon' => 'dashicons-admin-settings',
            'description' => 'Server-level performance configurations'
        ),
        'security' => array(
            'name' => 'Security Optimization',
            'icon' => 'dashicons-shield',
            'description' => 'Security-related performance improvements'
        ),
        'seo' => array(
            'name' => 'SEO Performance',
            'icon' => 'dashicons-search',
            'description' => 'SEO-focused performance optimizations'
        ),
        'images' => array(
            'name' => 'Image Optimization',
            'icon' => 'dashicons-format-image',
            'description' => 'Image compression and delivery optimizations'
        )
    );
    
    /**
     * Get all available fixes organized by tier and category
     */
    public function get_organized_fixes() {
        $fixes = array();
        
        foreach ($this->fix_tiers as $tier => $tier_config) {
            $fixes[$tier] = array(
                'config' => $tier_config,
                'categories' => array()
            );
            
            foreach ($this->fix_categories as $category => $category_config) {
                $fixes[$tier]['categories'][$category] = array(
                    'config' => $category_config,
                    'fixes' => $this->get_fixes_by_tier_and_category($tier, $category)
                );
            }
        }
        
        return $fixes;
    }
    
    /**
     * Get fixes by tier and category
     */
    private function get_fixes_by_tier_and_category($tier, $category) {
        $all_fixes = $this->get_all_available_fixes();
        
        return array_filter($all_fixes, function($fix) use ($tier, $category) {
            return $fix['tier'] === $tier && $fix['category'] === $category;
        });
    }
    
    /**
     * Define all available fixes with their tier and category
     */
    private function get_all_available_fixes() {
        return array(
            // SAFE TIER FIXES
            array(
                'id' => 'enable_gzip_compression',
                'title' => 'Enable GZIP Compression',
                'description' => 'Compress text files to reduce transfer size',
                'tier' => 'safe',
                'category' => 'server',
                'estimated_impact' => 'high',
                'estimated_time' => '30-60% size reduction',
                'prerequisites' => array('mod_deflate or mod_gzip'),
                'conflicts' => array(),
                'reversible' => true
            ),
            array(
                'id' => 'set_browser_caching',
                'title' => 'Set Browser Cache Headers',
                'description' => 'Configure browser caching for static assets',
                'tier' => 'safe',
                'category' => 'server',
                'estimated_impact' => 'high',
                'estimated_time' => 'Eliminates repeat downloads',
                'prerequisites' => array('mod_expires'),
                'conflicts' => array(),
                'reversible' => true
            ),
            array(
                'id' => 'optimize_images_lazy_load',
                'title' => 'Enable Image Lazy Loading',
                'description' => 'Load images only when they enter the viewport',
                'tier' => 'safe',
                'category' => 'images',
                'estimated_impact' => 'medium',
                'estimated_time' => '20-40% faster initial load',
                'prerequisites' => array(),
                'conflicts' => array('existing_lazy_load_plugins'),
                'reversible' => true
            ),
            array(
                'id' => 'remove_query_strings',
                'title' => 'Remove Query Strings from Static Resources',
                'description' => 'Remove version parameters from CSS/JS files',
                'tier' => 'safe',
                'category' => 'frontend',
                'estimated_impact' => 'low',
                'estimated_time' => 'Better caching compatibility',
                'prerequisites' => array(),
                'conflicts' => array(),
                'reversible' => true
            ),
            array(
                'id' => 'cleanup_transients',
                'title' => 'Clean Up Expired Transients',
                'description' => 'Remove expired temporary data from database',
                'tier' => 'safe',
                'category' => 'database',
                'estimated_impact' => 'low',
                'estimated_time' => 'Reduced database size',
                'prerequisites' => array(),
                'conflicts' => array(),
                'reversible' => false
            ),
            
            // MODERATE TIER FIXES
            array(
                'id' => 'minify_css_js',
                'title' => 'Minify CSS and JavaScript',
                'description' => 'Remove whitespace and comments from CSS/JS files',
                'tier' => 'moderate',
                'category' => 'frontend',
                'estimated_impact' => 'medium',
                'estimated_time' => '10-30% size reduction',
                'prerequisites' => array(),
                'conflicts' => array('existing_minification_plugins'),
                'reversible' => true
            ),
            array(
                'id' => 'combine_css_js',
                'title' => 'Combine CSS and JavaScript Files',
                'description' => 'Reduce HTTP requests by combining files',
                'tier' => 'moderate',
                'category' => 'frontend',
                'estimated_impact' => 'medium',
                'estimated_time' => 'Fewer HTTP requests',
                'prerequisites' => array(),
                'conflicts' => array('http2_server'),
                'reversible' => true
            ),
            array(
                'id' => 'optimize_database_tables',
                'title' => 'Optimize Database Tables',
                'description' => 'Defragment and optimize database tables',
                'tier' => 'moderate',
                'category' => 'database',
                'estimated_impact' => 'medium',
                'estimated_time' => 'Faster database queries',
                'prerequisites' => array(),
                'conflicts' => array(),
                'reversible' => false
            ),
            array(
                'id' => 'enable_page_caching',
                'title' => 'Enable Page Caching',
                'description' => 'Cache full HTML pages for faster delivery',
                'tier' => 'moderate',
                'category' => 'server',
                'estimated_impact' => 'high',
                'estimated_time' => '50-90% faster page loads',
                'prerequisites' => array(),
                'conflicts' => array('existing_caching_plugins'),
                'reversible' => true
            ),
            array(
                'id' => 'optimize_autoload_data',
                'title' => 'Optimize Autoload Data',
                'description' => 'Reduce data loaded on every page request',
                'tier' => 'moderate',
                'category' => 'database',
                'estimated_impact' => 'medium',
                'estimated_time' => 'Faster page initialization',
                'prerequisites' => array(),
                'conflicts' => array(),
                'reversible' => true
            ),
            
            // ADVANCED TIER FIXES
            array(
                'id' => 'implement_critical_css',
                'title' => 'Implement Critical CSS',
                'description' => 'Inline critical CSS and defer non-critical styles',
                'tier' => 'advanced',
                'category' => 'frontend',
                'estimated_impact' => 'high',
                'estimated_time' => 'Faster first paint',
                'prerequisites' => array(),
                'conflicts' => array('theme_customizations'),
                'reversible' => true
            ),
            array(
                'id' => 'defer_javascript',
                'title' => 'Defer Non-Critical JavaScript',
                'description' => 'Load JavaScript after page content',
                'tier' => 'advanced',
                'category' => 'frontend',
                'estimated_impact' => 'high',
                'estimated_time' => 'Faster page rendering',
                'prerequisites' => array(),
                'conflicts' => array('inline_scripts'),
                'reversible' => true
            ),
            array(
                'id' => 'optimize_wp_config',
                'title' => 'Optimize WordPress Configuration',
                'description' => 'Add performance-focused wp-config.php settings',
                'tier' => 'advanced',
                'category' => 'server',
                'estimated_impact' => 'medium',
                'estimated_time' => 'Various optimizations',
                'prerequisites' => array('file_write_permissions'),
                'conflicts' => array('existing_wp_config_modifications'),
                'reversible' => true
            ),
            array(
                'id' => 'implement_preloading',
                'title' => 'Implement Resource Preloading',
                'description' => 'Preload critical resources for faster loading',
                'tier' => 'advanced',
                'category' => 'frontend',
                'estimated_impact' => 'medium',
                'estimated_time' => 'Faster resource loading',
                'prerequisites' => array(),
                'conflicts' => array(),
                'reversible' => true
            ),
            array(
                'id' => 'database_index_optimization',
                'title' => 'Optimize Database Indexes',
                'description' => 'Add and optimize database indexes for better performance',
                'tier' => 'advanced',
                'category' => 'database',
                'estimated_impact' => 'high',
                'estimated_time' => 'Faster database queries',
                'prerequisites' => array('database_admin_access'),
                'conflicts' => array(),
                'reversible' => true
            )
        );
    }
    
    /**
     * Get fix details with safety information
     */
    public function get_fix_details($fix_id) {
        $all_fixes = $this->get_all_available_fixes();
        $fix = array_filter($all_fixes, function($f) use ($fix_id) {
            return $f['id'] === $fix_id;
        });
        
        if (empty($fix)) {
            return false;
        }
        
        $fix = array_values($fix)[0];
        $tier_config = $this->fix_tiers[$fix['tier']];
        
        return array_merge($fix, array(
            'tier_config' => $tier_config,
            'safety_info' => $this->get_safety_information($fix),
            'compatibility_check' => $this->check_compatibility($fix),
            'impact_analysis' => $this->analyze_impact($fix)
        ));
    }
    
    /**
     * Get safety information for a fix
     */
    private function get_safety_information($fix) {
        $tier_config = $this->fix_tiers[$fix['tier']];
        
        return array(
            'risk_level' => $tier_config['risk_level'],
            'backup_required' => $tier_config['backup_required'],
            'user_confirmation' => $tier_config['user_confirmation'],
            'rollback_window' => $tier_config['rollback_window'],
            'reversible' => $fix['reversible'],
            'prerequisites' => $fix['prerequisites'],
            'conflicts' => $fix['conflicts'],
            'warnings' => $this->get_fix_warnings($fix)
        );
    }
    
    /**
     * Get warnings for specific fixes
     */
    private function get_fix_warnings($fix) {
        $warnings = array();
        
        switch ($fix['id']) {
            case 'minify_css_js':
                $warnings[] = 'May break sites with inline JavaScript that depends on formatting';
                break;
            case 'combine_css_js':
                $warnings[] = 'Not recommended for HTTP/2 servers';
                $warnings[] = 'May cause issues with conditional loading';
                break;
            case 'implement_critical_css':
                $warnings[] = 'Requires theme compatibility testing';
                $warnings[] = 'May cause flash of unstyled content (FOUC)';
                break;
            case 'defer_javascript':
                $warnings[] = 'May break functionality that depends on immediate script execution';
                break;
            case 'optimize_wp_config':
                $warnings[] = 'Modifies core WordPress configuration file';
                break;
        }
        
        return $warnings;
    }
    
    /**
     * Check compatibility for a fix
     */
    private function check_compatibility($fix) {
        $compatibility = array(
            'compatible' => true,
            'issues' => array(),
            'recommendations' => array()
        );
        
        // Check for conflicting plugins
        foreach ($fix['conflicts'] as $conflict) {
            if ($this->has_conflicting_feature($conflict)) {
                $compatibility['compatible'] = false;
                $compatibility['issues'][] = "Conflicts with existing {$conflict}";
            }
        }
        
        // Check prerequisites
        foreach ($fix['prerequisites'] as $prerequisite) {
            if (!$this->check_prerequisite($prerequisite)) {
                $compatibility['compatible'] = false;
                $compatibility['issues'][] = "Missing prerequisite: {$prerequisite}";
            }
        }
        
        return $compatibility;
    }
    
    /**
     * Analyze potential impact of a fix
     */
    private function analyze_impact($fix) {
        return array(
            'performance_impact' => $fix['estimated_impact'],
            'time_to_effect' => $fix['estimated_time'],
            'affected_areas' => $this->get_affected_areas($fix),
            'user_experience_impact' => $this->assess_ux_impact($fix),
            'maintenance_requirements' => $this->get_maintenance_requirements($fix)
        );
    }
    
    /**
     * Get areas affected by a fix
     */
    private function get_affected_areas($fix) {
        $areas = array();
        
        switch ($fix['category']) {
            case 'database':
                $areas = array('Database performance', 'Admin area speed', 'Query execution time');
                break;
            case 'frontend':
                $areas = array('Page load speed', 'User experience', 'Rendering performance');
                break;
            case 'server':
                $areas = array('Server response time', 'Resource utilization', 'Caching behavior');
                break;
            case 'images':
                $areas = array('Image loading speed', 'Bandwidth usage', 'Visual performance');
                break;
        }
        
        return $areas;
    }
    
    /**
     * Check if a conflicting feature exists
     */
    private function has_conflicting_feature($conflict) {
        switch ($conflict) {
            case 'existing_caching_plugins':
                return $this->has_caching_plugin();
            case 'existing_minification_plugins':
                return $this->has_minification_plugin();
            case 'existing_lazy_load_plugins':
                return $this->has_lazy_load_plugin();
            default:
                return false;
        }
    }
    
    /**
     * Check if prerequisite is met
     */
    private function check_prerequisite($prerequisite) {
        switch ($prerequisite) {
            case 'mod_deflate':
            case 'mod_gzip':
                return $this->check_apache_module($prerequisite);
            case 'mod_expires':
                return $this->check_apache_module($prerequisite);
            case 'file_write_permissions':
                return is_writable(ABSPATH);
            case 'database_admin_access':
                return current_user_can('manage_options');
            default:
                return true;
        }
    }
}
