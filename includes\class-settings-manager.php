<?php
/**
 * Standardized Settings Manager for Redco Optimizer
 *
 * Provides unified settings management across all modules with consistent
 * validation, auto-save functionality, and option naming conventions.
 *
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Settings_Manager {

    /**
     * Settings cache
     */
    private static $settings_cache = array();

    /**
     * Auto-save enabled modules
     */
    private static $auto_save_modules = array(
        'page-cache',
        'lazy-load',
        'asset-optimization',
        'heartbeat-control',
        'wordpress-core-tweaks',
        'smart-webp-conversion',
        'cdn-integration',
        'diagnostic-autofix',
        'database-cleanup'
    );

    /**
     * Initialize settings manager
     */
    public static function init() {
        // Register AJAX handlers for auto-save
        add_action('wp_ajax_redco_auto_save_setting', array(__CLASS__, 'handle_auto_save'));
        add_action('wp_ajax_redco_validate_setting', array(__CLASS__, 'handle_validation'));
        add_action('wp_ajax_redco_reset_module_settings', array(__CLASS__, 'handle_reset_settings'));
        
        // Hook into settings updates for validation
        add_filter('pre_update_option', array(__CLASS__, 'validate_option_update'), 10, 3);
    }

    /**
     * Get module setting with standardized naming
     *
     * @param string $module_key Module identifier
     * @param string $setting_key Setting key
     * @param mixed $default Default value
     * @return mixed Setting value
     */
    public static function get_setting($module_key, $setting_key, $default = null) {
        // Check cache first
        $cache_key = $module_key . '_' . $setting_key;
        if (isset(self::$settings_cache[$cache_key])) {
            return self::$settings_cache[$cache_key];
        }

        // Get module settings
        $module_settings = self::get_module_settings($module_key);
        
        // Get setting value with fallback to default
        if ($default === null) {
            $defaults = Redco_Config::get_module_defaults($module_key);
            $default = isset($defaults[$setting_key]) ? $defaults[$setting_key] : null;
        }

        $value = isset($module_settings[$setting_key]) ? $module_settings[$setting_key] : $default;
        
        // Cache the value
        self::$settings_cache[$cache_key] = $value;
        
        return $value;
    }

    /**
     * Set module setting with validation
     *
     * @param string $module_key Module identifier
     * @param string $setting_key Setting key
     * @param mixed $value Setting value
     * @return bool Success status
     */
    public static function set_setting($module_key, $setting_key, $value) {
        // Validate the setting
        $validated_value = self::validate_setting($module_key, $setting_key, $value);
        
        // Get current module settings
        $module_settings = self::get_module_settings($module_key);
        $module_settings[$setting_key] = $validated_value;
        
        // Update settings
        $option_name = self::get_option_name($module_key);
        $success = update_option($option_name, $module_settings);
        
        if ($success) {
            // Update cache
            $cache_key = $module_key . '_' . $setting_key;
            self::$settings_cache[$cache_key] = $validated_value;
            
            // Clear related caches
            self::clear_module_cache($module_key);
        }
        
        return $success;
    }

    /**
     * Get all settings for a module
     *
     * @param string $module_key Module identifier
     * @return array Module settings
     */
    public static function get_module_settings($module_key) {
        $option_name = self::get_option_name($module_key);
        $defaults = Redco_Config::get_module_defaults($module_key);
        $settings = get_option($option_name, array());
        
        // Merge with defaults to ensure all settings exist
        return wp_parse_args($settings, $defaults);
    }

    /**
     * Update all settings for a module
     *
     * @param string $module_key Module identifier
     * @param array $settings Settings array
     * @return bool Success status
     */
    public static function update_module_settings($module_key, $settings) {
        // Validate all settings
        $validated_settings = array();
        foreach ($settings as $key => $value) {
            $validated_settings[$key] = self::validate_setting($module_key, $key, $value);
        }
        
        $option_name = self::get_option_name($module_key);
        $success = update_option($option_name, $validated_settings);
        
        if ($success) {
            // Clear cache for this module
            self::clear_module_cache($module_key);
        }
        
        return $success;
    }

    /**
     * Reset module settings to defaults
     *
     * @param string $module_key Module identifier
     * @return bool Success status
     */
    public static function reset_module_settings($module_key) {
        $defaults = Redco_Config::get_module_defaults($module_key);
        return self::update_module_settings($module_key, $defaults);
    }

    /**
     * Validate setting value
     *
     * @param string $module_key Module identifier
     * @param string $setting_key Setting key
     * @param mixed $value Value to validate
     * @return mixed Validated value
     */
    public static function validate_setting($module_key, $setting_key, $value) {
        // Apply general validation from config
        $validated_value = Redco_Config::validate_setting($setting_key, $value);
        
        // Apply module-specific validation
        $validated_value = apply_filters(
            "redco_validate_{$module_key}_{$setting_key}",
            $validated_value,
            $value,
            $module_key
        );
        
        // Apply general validation filter
        return apply_filters(
            'redco_validate_setting',
            $validated_value,
            $setting_key,
            $value,
            $module_key
        );
    }

    /**
     * Get standardized option name for module
     *
     * @param string $module_key Module identifier
     * @return string Option name
     */
    public static function get_option_name($module_key) {
        return 'redco_optimizer_' . str_replace('-', '_', $module_key);
    }

    /**
     * Check if module supports auto-save
     *
     * @param string $module_key Module identifier
     * @return bool True if auto-save is supported
     */
    public static function supports_auto_save($module_key) {
        return in_array($module_key, self::$auto_save_modules);
    }

    /**
     * Clear module cache
     *
     * @param string $module_key Module identifier
     */
    public static function clear_module_cache($module_key) {
        // Clear settings cache
        foreach (self::$settings_cache as $cache_key => $value) {
            if (strpos($cache_key, $module_key . '_') === 0) {
                unset(self::$settings_cache[$cache_key]);
            }
        }
        
        // Clear WordPress object cache
        wp_cache_delete('redco_settings_' . $module_key, '');
        wp_cache_delete(self::get_option_name($module_key), 'options');
    }

    /**
     * Handle auto-save AJAX request
     */
    public static function handle_auto_save() {
        // Verify nonce and capability
        if (!Redco_Config::verify_nonce($_POST['nonce'] ?? '', 'settings_save') ||
            !Redco_Config::user_can_manage()) {
            wp_die('Security check failed');
        }

        $module_key = sanitize_text_field($_POST['module'] ?? '');
        $setting_key = sanitize_text_field($_POST['setting'] ?? '');
        $value = $_POST['value'] ?? '';

        // Sanitize value based on type
        if (is_array($value)) {
            $value = array_map('sanitize_text_field', $value);
        } else {
            $value = sanitize_text_field($value);
        }

        $success = self::set_setting($module_key, $setting_key, $value);

        wp_send_json(array(
            'success' => $success,
            'message' => $success ? __('Setting saved', 'redco-optimizer') : __('Failed to save setting', 'redco-optimizer')
        ));
    }

    /**
     * Handle setting validation AJAX request
     */
    public static function handle_validation() {
        // Verify nonce and capability
        if (!Redco_Config::verify_nonce($_POST['nonce'] ?? '', 'settings_save') ||
            !Redco_Config::user_can_manage()) {
            wp_die('Security check failed');
        }

        $module_key = sanitize_text_field($_POST['module'] ?? '');
        $setting_key = sanitize_text_field($_POST['setting'] ?? '');
        $value = $_POST['value'] ?? '';

        $validated_value = self::validate_setting($module_key, $setting_key, $value);
        $is_valid = ($validated_value === $value);

        wp_send_json(array(
            'valid' => $is_valid,
            'validated_value' => $validated_value,
            'message' => $is_valid ? __('Valid', 'redco-optimizer') : __('Value adjusted', 'redco-optimizer')
        ));
    }

    /**
     * Handle reset settings AJAX request
     */
    public static function handle_reset_settings() {
        // Verify nonce and capability
        if (!Redco_Config::verify_nonce($_POST['nonce'] ?? '', 'settings_save') ||
            !Redco_Config::user_can_manage()) {
            wp_die('Security check failed');
        }

        $module_key = sanitize_text_field($_POST['module'] ?? '');
        $success = self::reset_module_settings($module_key);

        wp_send_json(array(
            'success' => $success,
            'message' => $success ? __('Settings reset to defaults', 'redco-optimizer') : __('Failed to reset settings', 'redco-optimizer'),
            'settings' => $success ? self::get_module_settings($module_key) : array()
        ));
    }

    /**
     * Validate option updates
     */
    public static function validate_option_update($value, $option, $old_value) {
        // Only validate Redco Optimizer options
        if (strpos($option, 'redco_optimizer_') !== 0) {
            return $value;
        }

        // Extract module key from option name
        $module_key = str_replace('redco_optimizer_', '', $option);
        $module_key = str_replace('_', '-', $module_key);

        // Validate each setting in the array
        if (is_array($value)) {
            $validated_value = array();
            foreach ($value as $setting_key => $setting_value) {
                $validated_value[$setting_key] = self::validate_setting($module_key, $setting_key, $setting_value);
            }
            return $validated_value;
        }

        return $value;
    }
}

// Initialize settings manager
Redco_Settings_Manager::init();
