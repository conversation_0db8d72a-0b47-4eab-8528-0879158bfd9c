<?php
/**
 * Core Diagnostic Engine for Redco Diagnostic & Auto-Fix
 * 
 * Main diagnostic scanning and auto-fix functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Diagnostic_Engine {
    
    private $settings;
    private $scan_results;
    private $fix_results;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = get_option('redco_diagnostic_settings', array());
        $this->scan_results = array();
        $this->fix_results = array();
    }
    
    /**
     * Initialize the diagnostic engine
     */
    public function init() {
        // AJAX handlers
        add_action('wp_ajax_redco_diagnostic_scan', array($this, 'ajax_run_diagnostic_scan'));
        add_action('wp_ajax_redco_diagnostic_apply_fixes', array($this, 'ajax_apply_auto_fixes'));
        add_action('wp_ajax_redco_diagnostic_apply_single_fix', array($this, 'ajax_apply_single_fix'));
        add_action('wp_ajax_redco_diagnostic_rollback_fixes', array($this, 'ajax_rollback_fixes'));
        add_action('wp_ajax_redco_diagnostic_export_report', array($this, 'ajax_export_diagnostic_report'));
        
        // Scheduled scans
        add_action('redco_diagnostic_scheduled_scan', array($this, 'run_scheduled_scan'));
        
        // Initialize database tables
        $this->maybe_create_tables();
    }
    
    /**
     * AJAX: Run diagnostic scan
     */
    public function ajax_run_diagnostic_scan() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error(array(
                'message' => 'Security check failed',
                'error_code' => 'NONCE_FAILED'
            ));
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => 'Insufficient permissions',
                'error_code' => 'INSUFFICIENT_PERMISSIONS'
            ));
            return;
        }

        // Set execution limits
        $original_time_limit = ini_get('max_execution_time');
        $original_memory_limit = ini_get('memory_limit');
        
        @ini_set('max_execution_time', 300); // 5 minutes
        @ini_set('memory_limit', '512M');

        try {
            $scan_type = sanitize_text_field($_POST['scan_type'] ?? 'comprehensive');
            $results = $this->run_diagnostic_scan($scan_type);
            
            // Store results
            $this->store_scan_results($results);
            
            wp_send_json_success($results);
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => 'Scan failed: ' . $e->getMessage(),
                'error_code' => 'SCAN_EXCEPTION'
            ));
        } finally {
            // Restore original limits
            @ini_set('max_execution_time', $original_time_limit);
            @ini_set('memory_limit', $original_memory_limit);
        }
    }
    
    /**
     * Run comprehensive diagnostic scan
     */
    public function run_diagnostic_scan($scan_type = 'comprehensive') {
        $start_time = microtime(true);
        $scan_id = redco_diagnostic_generate_scan_id();
        
        redco_diagnostic_log("Starting diagnostic scan (Type: {$scan_type}, ID: {$scan_id})");
        
        $results = array(
            'scan_id' => $scan_id,
            'scan_type' => $scan_type,
            'timestamp' => time(),
            'issues' => array(),
            'performance_score' => 0,
            'health_score' => 0,
            'scan_duration' => 0,
            'recommendations' => array()
        );
        
        // Load diagnostic helpers
        if (!class_exists('Redco_Diagnostic_Helpers')) {
            require_once REDCO_DIAGNOSTIC_PLUGIN_DIR . 'includes/class-diagnostic-helpers.php';
        }
        
        $helpers = new Redco_Diagnostic_Helpers();
        
        // Run different scan types
        switch ($scan_type) {
            case 'quick':
                $results['issues'] = $this->run_quick_scan($helpers);
                break;
            case 'performance':
                $results['issues'] = $this->run_performance_scan($helpers);
                break;
            case 'security':
                $results['issues'] = $this->run_security_scan($helpers);
                break;
            case 'comprehensive':
            default:
                $results['issues'] = $this->run_comprehensive_scan($helpers);
                break;
        }
        
        // Calculate scores
        $results['performance_score'] = $this->calculate_performance_score($results['issues']);
        $results['health_score'] = $this->calculate_health_score($results['issues']);
        
        // Generate recommendations
        $results['recommendations'] = $this->generate_recommendations($results['issues']);
        
        // Calculate scan duration
        $results['scan_duration'] = round(microtime(true) - $start_time, 2);
        
        // Update statistics
        redco_diagnostic_increment_stat('total_scans');
        redco_diagnostic_increment_stat('total_issues_found', count($results['issues']));
        redco_diagnostic_update_stats('last_scan_time', time());
        
        redco_diagnostic_log("Diagnostic scan completed (Duration: {$results['scan_duration']}s, Issues: " . count($results['issues']) . ")");
        
        return $results;
    }
    
    /**
     * Run quick scan (essential issues only)
     */
    private function run_quick_scan($helpers) {
        $issues = array();
        
        // Critical WordPress issues
        $issues = array_merge($issues, $helpers->scan_wordpress_core_issues());
        
        // Basic performance issues
        $issues = array_merge($issues, $helpers->scan_basic_performance_issues());
        
        // Security essentials
        $issues = array_merge($issues, $helpers->scan_basic_security_issues());
        
        return $issues;
    }
    
    /**
     * Run performance-focused scan
     */
    private function run_performance_scan($helpers) {
        $issues = array();
        
        // Performance issues
        $issues = array_merge($issues, $helpers->scan_performance_issues());
        $issues = array_merge($issues, $helpers->scan_database_performance());
        $issues = array_merge($issues, $helpers->scan_caching_issues());
        $issues = array_merge($issues, $helpers->scan_image_optimization());
        $issues = array_merge($issues, $helpers->scan_css_js_optimization());
        
        return $issues;
    }
    
    /**
     * Run security-focused scan
     */
    private function run_security_scan($helpers) {
        $issues = array();
        
        // Security issues
        $issues = array_merge($issues, $helpers->scan_security_issues());
        $issues = array_merge($issues, $helpers->scan_file_permissions());
        $issues = array_merge($issues, $helpers->scan_wp_config_security());
        $issues = array_merge($issues, $helpers->scan_plugin_vulnerabilities());
        
        return $issues;
    }
    
    /**
     * Run comprehensive scan (all checks)
     */
    private function run_comprehensive_scan($helpers) {
        $issues = array();
        
        // All scan types
        $issues = array_merge($issues, $this->run_quick_scan($helpers));
        $issues = array_merge($issues, $this->run_performance_scan($helpers));
        $issues = array_merge($issues, $this->run_security_scan($helpers));
        
        // Additional comprehensive checks
        $issues = array_merge($issues, $helpers->scan_seo_issues());
        $issues = array_merge($issues, $helpers->scan_accessibility_issues());
        $issues = array_merge($issues, $helpers->scan_plugin_conflicts());
        
        // Remove duplicates
        $issues = $this->remove_duplicate_issues($issues);
        
        return $issues;
    }
    
    /**
     * Calculate performance score
     */
    private function calculate_performance_score($issues) {
        $score = 100;
        
        foreach ($issues as $issue) {
            if (isset($issue['category']) && in_array($issue['category'], array('performance', 'database', 'caching'))) {
                switch ($issue['severity']) {
                    case 'critical':
                        $score -= 25;
                        break;
                    case 'high':
                        $score -= 15;
                        break;
                    case 'medium':
                        $score -= 10;
                        break;
                    case 'low':
                        $score -= 5;
                        break;
                }
            }
        }
        
        return max(0, min(100, $score));
    }
    
    /**
     * Calculate health score
     */
    private function calculate_health_score($issues) {
        $score = 100;
        $total_issues = count($issues);
        
        if ($total_issues === 0) {
            return 100;
        }
        
        foreach ($issues as $issue) {
            switch ($issue['severity']) {
                case 'critical':
                    $score -= 20;
                    break;
                case 'high':
                    $score -= 12;
                    break;
                case 'medium':
                    $score -= 8;
                    break;
                case 'low':
                    $score -= 4;
                    break;
            }
        }
        
        return max(0, min(100, $score));
    }
    
    /**
     * Generate recommendations
     */
    private function generate_recommendations($issues) {
        $recommendations = array();
        
        // Priority recommendations
        $critical_issues = array_filter($issues, function($issue) {
            return $issue['severity'] === 'critical';
        });
        
        if (!empty($critical_issues)) {
            $recommendations[] = array(
                'type' => 'critical',
                'title' => 'Address Critical Issues First',
                'description' => 'You have ' . count($critical_issues) . ' critical issues that should be fixed immediately.',
                'action' => 'Fix critical issues'
            );
        }
        
        // Auto-fixable recommendations
        $auto_fixable = array_filter($issues, function($issue) {
            return isset($issue['auto_fixable']) && $issue['auto_fixable'];
        });
        
        if (!empty($auto_fixable)) {
            $recommendations[] = array(
                'type' => 'auto_fix',
                'title' => 'Apply Automatic Fixes',
                'description' => count($auto_fixable) . ' issues can be fixed automatically.',
                'action' => 'Apply auto-fixes'
            );
        }
        
        return $recommendations;
    }
    
    /**
     * Remove duplicate issues
     */
    private function remove_duplicate_issues($issues) {
        $unique_issues = array();
        $seen_ids = array();
        
        foreach ($issues as $issue) {
            $issue_id = $issue['id'] ?? md5(serialize($issue));
            
            if (!in_array($issue_id, $seen_ids)) {
                $seen_ids[] = $issue_id;
                $unique_issues[] = $issue;
            }
        }
        
        return $unique_issues;
    }
    
    /**
     * Store scan results in database
     */
    private function store_scan_results($results) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_results';
        
        $wpdb->insert(
            $table_name,
            array(
                'scan_id' => $results['scan_id'],
                'timestamp' => current_time('mysql'),
                'scan_type' => $results['scan_type'],
                'issues_found' => count($results['issues']),
                'critical_issues' => count(array_filter($results['issues'], function($issue) {
                    return $issue['severity'] === 'critical';
                })),
                'auto_fixable' => count(array_filter($results['issues'], function($issue) {
                    return isset($issue['auto_fixable']) && $issue['auto_fixable'];
                })),
                'performance_score' => $results['performance_score'],
                'health_score' => $results['health_score'],
                'scan_data' => json_encode($results)
            ),
            array('%s', '%s', '%s', '%d', '%d', '%d', '%d', '%d', '%s')
        );
    }
    
    /**
     * Maybe create database tables
     */
    private function maybe_create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Diagnostic results table
        $table_name = $wpdb->prefix . 'redco_diagnostic_results';
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
            $sql = "CREATE TABLE {$table_name} (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                scan_id varchar(32) NOT NULL,
                timestamp datetime NOT NULL,
                scan_type varchar(50) NOT NULL DEFAULT 'comprehensive',
                issues_found int(11) NOT NULL DEFAULT 0,
                critical_issues int(11) NOT NULL DEFAULT 0,
                auto_fixable int(11) NOT NULL DEFAULT 0,
                performance_score int(3) NOT NULL DEFAULT 0,
                health_score int(3) NOT NULL DEFAULT 0,
                scan_data longtext,
                PRIMARY KEY (id),
                KEY scan_id (scan_id),
                KEY timestamp (timestamp),
                KEY scan_type (scan_type)
            ) {$charset_collate};";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }
    
    /**
     * Get scan statistics
     */
    public function get_scan_statistics() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_results';
        
        $stats = $wpdb->get_row("
            SELECT 
                COUNT(*) as total_scans,
                AVG(performance_score) as avg_performance_score,
                AVG(health_score) as avg_health_score,
                SUM(issues_found) as total_issues_found,
                SUM(critical_issues) as total_critical_issues
            FROM {$table_name}
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ", ARRAY_A);
        
        return $stats ?: array(
            'total_scans' => 0,
            'avg_performance_score' => 0,
            'avg_health_score' => 0,
            'total_issues_found' => 0,
            'total_critical_issues' => 0
        );
    }
}
