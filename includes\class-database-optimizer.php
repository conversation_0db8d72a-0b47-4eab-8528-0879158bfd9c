<?php
/**
 * Database Optimization System for Redco Optimizer
 * 
 * Optimizes database queries, indexes, and performance
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Database_Optimizer {
    
    /**
     * Query cache
     */
    private static $query_cache = array();
    
    /**
     * Query statistics
     */
    private static $query_stats = array(
        'total_queries' => 0,
        'cached_queries' => 0,
        'slow_queries' => 0,
        'total_time' => 0
    );
    
    /**
     * Slow query threshold (in seconds)
     */
    const SLOW_QUERY_THRESHOLD = 0.1;
    
    /**
     * Cache duration for database queries (in seconds)
     */
    const CACHE_DURATION = 300; // 5 minutes
    
    /**
     * Maximum cache size (number of cached queries)
     */
    const MAX_CACHE_SIZE = 100;
    
    /**
     * Initialize database optimization
     */
    public static function init() {
        // Add query optimization hooks
        add_filter('posts_request', array(__CLASS__, 'optimize_posts_query'), 10, 2);
        add_action('pre_get_posts', array(__CLASS__, 'optimize_main_query'));
        
        // Add database maintenance hooks
        add_action('wp_scheduled_delete', array(__CLASS__, 'cleanup_expired_transients'));
        add_action('redco_daily_maintenance', array(__CLASS__, 'daily_database_maintenance'));
        
        // Schedule daily maintenance if not already scheduled
        if (!wp_next_scheduled('redco_daily_maintenance')) {
            wp_schedule_event(time(), 'daily', 'redco_daily_maintenance');
        }
        
        // Add query monitoring in development
        if (Redco_Config::is_development_environment()) {
            add_filter('query', array(__CLASS__, 'monitor_query'));
        }
    }
    
    /**
     * Get cached query result
     * 
     * @param string $query_key Unique query identifier
     * @param callable $query_callback Function to execute if not cached
     * @param int $cache_duration Cache duration in seconds
     * @return mixed Query result
     */
    public static function get_cached_query($query_key, $query_callback, $cache_duration = null) {
        $cache_duration = $cache_duration ?: self::CACHE_DURATION;
        $cache_key = 'redco_query_' . md5($query_key);
        
        // Try to get from WordPress transient cache first
        $cached_result = get_transient($cache_key);
        if ($cached_result !== false) {
            self::$query_stats['cached_queries']++;
            return $cached_result;
        }
        
        // Try to get from memory cache
        if (isset(self::$query_cache[$cache_key])) {
            $cache_data = self::$query_cache[$cache_key];
            if (time() - $cache_data['timestamp'] < $cache_duration) {
                self::$query_stats['cached_queries']++;
                return $cache_data['result'];
            } else {
                unset(self::$query_cache[$cache_key]);
            }
        }
        
        // Execute query and cache result
        $start_time = microtime(true);
        $result = call_user_func($query_callback);
        $execution_time = microtime(true) - $start_time;
        
        // Update statistics
        self::$query_stats['total_queries']++;
        self::$query_stats['total_time'] += $execution_time;
        
        // TEMPORARILY DISABLED: Slow query logging
        /*
        if ($execution_time > self::SLOW_QUERY_THRESHOLD) {
            self::$query_stats['slow_queries']++;
            Redco_Error_Handler::warning(
                "Slow database query detected: {$query_key} took {$execution_time}s",
                Redco_Error_Handler::CONTEXT_PERFORMANCE,
                array('query_key' => $query_key, 'execution_time' => $execution_time)
            );
        }
        */
        
        // Cache the result
        if ($result !== false && $result !== null) {
            // Store in WordPress transient cache
            set_transient($cache_key, $result, $cache_duration);
            
            // Store in memory cache
            self::$query_cache[$cache_key] = array(
                'result' => $result,
                'timestamp' => time()
            );
            
            // Limit memory cache size
            if (count(self::$query_cache) > self::MAX_CACHE_SIZE) {
                $oldest_key = array_keys(self::$query_cache)[0];
                unset(self::$query_cache[$oldest_key]);
            }
        }
        
        return $result;
    }
    
    /**
     * Optimize posts query
     * 
     * @param string $request SQL query
     * @param WP_Query $query Query object
     * @return string Optimized SQL query
     */
    public static function optimize_posts_query($request, $query) {
        // Skip optimization for admin queries
        if (is_admin() && !wp_doing_ajax()) {
            return $request;
        }
        
        // Add query hints for better performance
        if (strpos($request, 'SELECT') === 0) {
            // Add SQL_CALC_FOUND_ROWS only when needed
            if (!$query->get('no_found_rows') && $query->get('posts_per_page') != -1) {
                if (strpos($request, 'SQL_CALC_FOUND_ROWS') === false) {
                    $request = str_replace('SELECT', 'SELECT SQL_CALC_FOUND_ROWS', $request);
                }
            }
            
            // Optimize ORDER BY clauses
            if (strpos($request, 'ORDER BY') !== false) {
                // Use index hints for common sorting patterns
                if (strpos($request, 'ORDER BY wp_posts.post_date') !== false) {
                    $request = str_replace(
                        'FROM wp_posts',
                        'FROM wp_posts USE INDEX (type_status_date)',
                        $request
                    );
                }
            }
        }
        
        return $request;
    }
    
    /**
     * Optimize main query
     * 
     * @param WP_Query $query Query object
     */
    public static function optimize_main_query($query) {
        if (!$query->is_main_query()) {
            return;
        }
        
        // Optimize home page queries
        if ($query->is_home() && !is_admin()) {
            // Limit posts per page for better performance
            $posts_per_page = get_option('posts_per_page', 10);
            if ($posts_per_page > 20) {
                $query->set('posts_per_page', 20);
            }
            
            // Exclude unnecessary post meta queries
            $query->set('update_post_meta_cache', false);
            $query->set('update_post_term_cache', false);
        }
        
        // Optimize search queries
        if ($query->is_search() && !is_admin()) {
            // Limit search results for performance
            $query->set('posts_per_page', 10);
            
            // Exclude post content from search for better performance
            add_filter('posts_search', array(__CLASS__, 'optimize_search_query'));
        }
        
        // Optimize archive queries
        if ($query->is_archive() && !is_admin()) {
            // Disable unnecessary caches for archive pages
            $query->set('update_post_meta_cache', false);
        }
    }
    
    /**
     * Optimize search query
     * 
     * @param string $search Search SQL
     * @return string Optimized search SQL
     */
    public static function optimize_search_query($search) {
        // Remove post_content from search for better performance
        $search = str_replace(
            array('post_content', 'post_excerpt'),
            array('post_title', 'post_title'),
            $search
        );
        
        return $search;
    }
    
    /**
     * Monitor database queries
     * 
     * @param string $query SQL query
     * @return string Query (unchanged)
     */
    public static function monitor_query($query) {
        $start_time = microtime(true);
        
        // Log slow queries
        add_filter('posts_results', function($results) use ($query, $start_time) {
            $execution_time = microtime(true) - $start_time;
            
            // TEMPORARILY DISABLED: Slow query logging
            /*
            if ($execution_time > self::SLOW_QUERY_THRESHOLD) {
                Redco_Error_Handler::warning(
                    "Slow database query detected",
                    Redco_Error_Handler::CONTEXT_PERFORMANCE,
                    array(
                        'query' => substr($query, 0, 200) . '...',
                        'execution_time' => $execution_time
                    )
                );
            }
            */
            
            return $results;
        });
        
        return $query;
    }
    
    /**
     * Clean up expired transients
     */
    public static function cleanup_expired_transients() {
        global $wpdb;
        
        // SECURITY FIX: Use prepared statements for database queries
        // Delete expired transients
        $wpdb->query($wpdb->prepare("
            DELETE FROM {$wpdb->options}
            WHERE option_name LIKE %s
            AND option_value < %d
        ", '_transient_timeout_%', time()));

        // Delete orphaned transient options
        $wpdb->query($wpdb->prepare("
            DELETE FROM {$wpdb->options}
            WHERE option_name LIKE %s
            AND option_name NOT LIKE %s
            AND NOT EXISTS (
                SELECT 1 FROM {$wpdb->options} t2
                WHERE t2.option_name = CONCAT(%s, SUBSTRING(option_name, 12))
            )
        ", '_transient_%', '_transient_timeout_%', '_transient_timeout_'));
        
        Redco_Error_Handler::info(
            "Cleaned up expired transients",
            Redco_Error_Handler::CONTEXT_PERFORMANCE
        );
    }
    
    /**
     * Daily database maintenance
     */
    public static function daily_database_maintenance() {
        global $wpdb;
        
        // Clean up expired transients
        self::cleanup_expired_transients();
        
        // Clean up old revisions (keep last 5)
        $wpdb->query("
            DELETE p1 FROM {$wpdb->posts} p1
            INNER JOIN (
                SELECT post_parent, COUNT(*) as revision_count
                FROM {$wpdb->posts}
                WHERE post_type = 'revision'
                GROUP BY post_parent
                HAVING revision_count > 5
            ) p2 ON p1.post_parent = p2.post_parent
            WHERE p1.post_type = 'revision'
            AND p1.ID NOT IN (
                SELECT ID FROM (
                    SELECT ID FROM {$wpdb->posts}
                    WHERE post_type = 'revision'
                    AND post_parent = p1.post_parent
                    ORDER BY post_date DESC
                    LIMIT 5
                ) tmp
            )
        ");
        
        // Clean up orphaned postmeta
        $wpdb->query("
            DELETE pm FROM {$wpdb->postmeta} pm
            LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE p.ID IS NULL
        ");
        
        // Clean up orphaned commentmeta
        $wpdb->query("
            DELETE cm FROM {$wpdb->commentmeta} cm
            LEFT JOIN {$wpdb->comments} c ON cm.comment_id = c.comment_ID
            WHERE c.comment_ID IS NULL
        ");
        
        // SECURITY FIX: Optimize database tables with proper validation
        $tables = $wpdb->get_col($wpdb->prepare("SHOW TABLES LIKE %s", $wpdb->prefix . '%'));
        foreach ($tables as $table) {
            // SECURITY FIX: Validate table name before optimization
            if (self::is_valid_table_name($table)) {
                $wpdb->query("OPTIMIZE TABLE `" . esc_sql($table) . "`");
            }
        }
        
        Redco_Error_Handler::info(
            "Completed daily database maintenance",
            Redco_Error_Handler::CONTEXT_PERFORMANCE,
            array('tables_optimized' => count($tables))
        );
    }
    
    /**
     * Get database statistics
     * 
     * @return array Database statistics
     */
    public static function get_database_stats() {
        global $wpdb;
        
        return self::get_cached_query('database_stats', function() use ($wpdb) {
            $stats = array();
            
            // Get database size
            $db_size = $wpdb->get_var("
                SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as size_mb
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
            ");
            $stats['database_size_mb'] = $db_size ?: 0;
            
            // Get table statistics
            $table_stats = $wpdb->get_results("
                SELECT 
                    table_name,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb,
                    table_rows
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
                AND table_name LIKE '{$wpdb->prefix}%'
                ORDER BY (data_length + index_length) DESC
                LIMIT 10
            ");
            $stats['largest_tables'] = $table_stats ?: array();
            
            // Get cleanup statistics
            $stats['revisions'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'revision'");
            $stats['auto_drafts'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'auto-draft'");
            $stats['trashed_posts'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'trash'");
            $stats['spam_comments'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_approved = 'spam'");
            $stats['expired_transients'] = $wpdb->get_var("
                SELECT COUNT(*) FROM {$wpdb->options} 
                WHERE option_name LIKE '_transient_timeout_%' 
                AND option_value < UNIX_TIMESTAMP()
            ");
            
            return $stats;
        }, 3600); // Cache for 1 hour
    }
    
    /**
     * Get query statistics
     * 
     * @return array Query statistics
     */
    public static function get_query_stats() {
        return self::$query_stats;
    }
    
    /**
     * Clear query cache
     */
    public static function clear_query_cache() {
        self::$query_cache = array();
        
        // Clear WordPress transient cache for queries
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_redco_query_%'");
        
        // TEMPORARILY DISABLED: Database cache logging
        /*
        Redco_Error_Handler::info(
            "Cleared database query cache",
            Redco_Error_Handler::CONTEXT_PERFORMANCE
        );
        */
    }
    
    /**
     * Get cache hit ratio
     * 
     * @return float Cache hit ratio (0-1)
     */
    public static function get_cache_hit_ratio() {
        $total = self::$query_stats['total_queries'] + self::$query_stats['cached_queries'];
        return $total > 0 ? self::$query_stats['cached_queries'] / $total : 0;
    }
    
    /**
     * Reset statistics
     */
    public static function reset_stats() {
        self::$query_stats = array(
            'total_queries' => 0,
            'cached_queries' => 0,
            'slow_queries' => 0,
            'total_time' => 0
        );
    }

    /**
     * SECURITY FIX: Validate table name to prevent SQL injection
     *
     * @param string $table_name The table name to validate
     * @return bool True if valid, false otherwise
     */
    private static function is_valid_table_name($table_name) {
        global $wpdb;

        // Check if table name is a string
        if (!is_string($table_name)) {
            return false;
        }

        // Check if table name starts with WordPress prefix
        if (strpos($table_name, $wpdb->prefix) !== 0) {
            return false;
        }

        // Check for valid characters (alphanumeric, underscore only)
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $table_name)) {
            return false;
        }

        // Check length (reasonable limit)
        if (strlen($table_name) > 64) {
            return false;
        }

        // Check against known WordPress tables for additional security
        $known_wp_tables = array(
            $wpdb->posts,
            $wpdb->postmeta,
            $wpdb->comments,
            $wpdb->commentmeta,
            $wpdb->users,
            $wpdb->usermeta,
            $wpdb->options,
            $wpdb->links,
            $wpdb->terms,
            $wpdb->term_taxonomy,
            $wpdb->term_relationships
        );

        // Allow known WordPress tables and tables with WordPress prefix
        $is_known_table = in_array($table_name, $known_wp_tables);
        $has_wp_prefix = strpos($table_name, $wpdb->prefix) === 0;

        return $is_known_table || $has_wp_prefix;
    }
}
